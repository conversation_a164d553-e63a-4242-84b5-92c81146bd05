# Candid Connections

A Next.js application that uses graph database technology to match job seekers with hiring authorities based on skills and requirements.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Technology Stack](#technology-stack)
- [Project Architecture](#project-architecture)
- [Database Architecture](#database-architecture)
- [Getting Started](#getting-started)
- [Development](#development)
- [Deployment](#deployment)
- [Why Graph Databases?](#why-graph-databases)
- [AWS Integration](#aws-integration)

## Overview

Candid Connections is a platform that connects job seekers with the right hiring authorities within companies by analyzing the relationships between skills, requirements, and organizational hierarchies. The application uses a graph database (ArangoDB) to store and query these relationships, providing powerful matching capabilities and visualizations.

## Features

- Job seeker profiles with skills and experience
- Company profiles with organizational hierarchies
- Position listings with required skills and experience
- Matching algorithm based on graph relationships
- Interactive graph visualizations of connections
- CRUD operations for all entities
- Responsive UI with Tailwind CSS

## Technology Stack

- **Frontend**: Next.js 15, React, <PERSON>Script, Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: ArangoDB (graph database)
- **Visualization**: React Force Graph
- **Deployment**: Docker, AWS Amplify (optional)

## Project Architecture

```mermaid
graph TD
    Client[Client Browser] --> NextJS[Next.js Application]
    NextJS --> AppRouter[App Router]
    AppRouter --> Pages[Pages]
    AppRouter --> APIRoutes[API Routes]

    Pages --> Components[React Components]
    Components --> UI[UI Components]
    Components --> GraphViz[Graph Visualization]

    APIRoutes --> DBConnection[Database Connection]
    DBConnection --> ArangoDB[ArangoDB]

    ArangoDB --> DocumentCollections[Document Collections]
    ArangoDB --> EdgeCollections[Edge Collections]

    DocumentCollections --> JobSeekers[Job Seekers]
    DocumentCollections --> Companies[Companies]
    DocumentCollections --> HiringAuthorities[Hiring Authorities]
    DocumentCollections --> Positions[Positions]
    DocumentCollections --> Skills[Skills]

    EdgeCollections --> SeekerSkills[Seeker-Skills]
    EdgeCollections --> PositionSkills[Position-Skills]
    EdgeCollections --> CompanyHiringAuthorities[Company-HiringAuthorities]
    EdgeCollections --> HiringAuthorityPositions[HiringAuthority-Positions]
```

## Database Architecture

```mermaid
erDiagram
    JobSeeker ||--o{ SeekerSkill : has
    Skill ||--o{ SeekerSkill : belongs_to
    Skill ||--o{ PositionSkill : belongs_to
    Position ||--o{ PositionSkill : requires
    HiringAuthority ||--o{ Position : manages
    Company ||--o{ HiringAuthority : employs

    JobSeeker {
        string _id
        string name
        string email
        string title
        number experience
        string education
        string location
        string bio
    }

    Company {
        string _id
        string name
        string industry
        number size
        string location
        string description
        string website
    }

    HiringAuthority {
        string _id
        string name
        string email
        string title
        string level
        number directReports
        string companyId
    }

    Position {
        string _id
        string title
        string description
        string location
        boolean remote
        object salary
        number requiredExperience
        string hiringAuthorityId
    }

    Skill {
        string _id
        string name
        string category
        string level
    }

    SeekerSkill {
        string _from
        string _to
        number proficiency
        number yearsOfExperience
    }

    PositionSkill {
        string _from
        string _to
        number importance
        boolean required
    }

    CompanyHiringAuthority {
        string _from
        string _to
        string relationship
    }

    HiringAuthorityPosition {
        string _from
        string _to
        string relationship
    }
```

## Getting Started

### Prerequisites

- Node.js 18 or higher
- npm or yarn
- Docker (for running ArangoDB locally)
- AWS account (for deployment)
- AWS CLI (for AWS interactions)

### Setup Order of Operations

This section provides a step-by-step guide for setting up the project locally and deploying it to AWS.

### 1. Local Development Setup

#### 1.1. Clone the repository:
```bash
git clone https://github.com/yourusername/candid-connections.git
cd candid-connections
```

#### 1.2. Run the automated setup script:
```bash
chmod +x setup.sh
./setup.sh
```

This script will:
- Check if Docker is running
- Install dependencies
- Install ArangoDB CLI tools
- Create a `.env.local` file with default values
- Start ArangoDB using Docker
- Initialize the database
- Seed the database with sample data

Alternatively, you can use our npm scripts:

```bash
# Complete setup (recommended for first-time setup)
npm run setup

# Database operations
npm run db:setup-all     # Complete database setup with ArangoDB CLI tools
npm run db:init          # Initialize database only
npm run db:seed          # Seed database with mock data
npm run db:reset         # Reset and reseed database
npm run db:start         # Start ArangoDB Docker container
npm run db:stop          # Stop ArangoDB Docker container
npm run db:backup        # Backup database
npm run db:restore       # Restore database from backup
npm run db:install-tools # Install ArangoDB CLI tools
npm run db:credentials   # Interactive credential management

# Amplify operations
npm run amplify:setup    # Set up AWS Amplify
npm run amplify:deploy   # Deploy to AWS Amplify
npm run amplify:domain   # Set up custom domain
```

If you prefer to do these steps manually, follow the instructions below:

#### 1.3. Install dependencies:
```bash
npm install
```

#### 1.4. Set up environment variables:
Create a `.env.local` file in the root directory with the following content:
```
NEXT_PUBLIC_API_URL=http://localhost:3000
ARANGO_URL=http://localhost:8529
ARANGO_USERNAME=root
ARANGO_PASSWORD=rootpassword
ARANGO_DB_NAME=candid_connections
```

#### 1.5. Start ArangoDB using Docker:
Make sure Docker is running on your machine, then:
```bash
cd docker
docker-compose up -d
cd ..
```

You can verify that ArangoDB is running by visiting the web interface at [http://localhost:8529](http://localhost:8529). Use the following credentials:
- Username: root
- Password: rootpassword

#### 1.6. Initialize the database:
```bash
node scripts/init-db.js
```

#### 1.7. Seed the database with sample data:
```bash
node scripts/seed-data.js
```

#### 1.8. Start the development server:
```bash
npm run dev
```

#### 1.9. Open [http://localhost:3000](http://localhost:3000) in your browser.

### 2. Managing Environment Variables Securely

#### 2.1. Local Environment Variables

For local development, you can store your ArangoDB credentials in your shell configuration file:

For Zsh (recommended):
```bash
# Add these lines to your ~/.zshrc file
export ARANGO_HOST="localhost"
export ARANGO_PORT="8529"
export ARANGO_USERNAME="root"
export ARANGO_PASSWORD="rootpassword"
export ARANGO_DB_NAME="candid_connections"
```

For Bash:
```bash
# Add these lines to your ~/.bashrc or ~/.bash_profile file
export ARANGO_HOST="localhost"
export ARANGO_PORT="8529"
export ARANGO_USERNAME="root"
export ARANGO_PASSWORD="rootpassword"
export ARANGO_DB_NAME="candid_connections"
```

After adding these lines, reload your shell configuration:
```bash
# For Zsh
source ~/.zshrc

# For Bash
source ~/.bashrc  # or source ~/.bash_profile
```

Now you can run scripts without hardcoding credentials:
```bash
# The scripts will use the environment variables from your shell
./scripts/backup-db.sh
```

#### 2.2. AWS Credentials

For AWS interactions, you should use the AWS CLI to configure your credentials:

```bash
aws configure
```

This will prompt you for:
- AWS Access Key ID
- AWS Secret Access Key
- Default region
- Default output format

These credentials will be stored in `~/.aws/credentials` and used by both the AWS CLI and the Amplify CLI.

### 3. ArangoDB Setup

#### 3.1. Local Development (Already covered in section 1)

#### 3.2. Production Setup Options

##### Option A: ArangoDB Cloud (Recommended)

1. Sign up for [ArangoDB Cloud](https://cloud.arangodb.com/)
2. Create a new deployment
3. Save your credentials securely
4. Update your production environment variables with these credentials

##### Option B: Self-hosted on AWS EC2

1. Launch an EC2 instance
2. Install Docker on the instance
3. Run ArangoDB in a Docker container
4. Configure security groups to allow access from your application
5. Save the connection details securely

#### 3.3. Finding and Managing Your ArangoDB Credentials

We've created an interactive script to help you manage your ArangoDB credentials securely. Run:

```bash
npm run db:credentials
```

This script will guide you through:
1. Setting up new credentials
2. Storing them securely in your shell configuration
3. Testing the connection
4. Syncing them with AWS Amplify

##### Finding Credentials in ArangoDB Cloud:

1. **Sign up and create a deployment**:
   - Go to [ArangoDB Cloud](https://cloud.arangodb.com/)
   - Sign up for an account or log in
   - Click "Create Deployment"
   - Choose your cloud provider (AWS recommended for this project)
   - Select a region close to your users (us-east-1 recommended)
   - Choose a deployment size:
     - **Development**: Start with "Oneshard" (free tier available)
     - **Production**: Choose based on your expected load
   - Set up authentication:
     - Create a strong username (not "root")
     - Generate a secure password
     - **IMPORTANT**: Save these credentials immediately!

2. **Get your connection details**:
   - After deployment is created, go to "Overview"
   - Click on "Connection" tab
   - You'll find:
     - **Endpoint URL**: Copy the full URL (e.g., `https://abc123.arangodb.cloud:8529`)
     - **Username**: The username you created
     - **Password**: The password you created
     - **Database**: Default is `_system`, but we'll create `candid_connections`

3. **Create the application database**:
   - In the ArangoDB Cloud dashboard, click "Web Interface"
   - Log in with your credentials
   - Click "Databases" in the sidebar
   - Click "Add Database"
   - Enter `candid_connections` as the name
   - Click "Create"

4. **Secure your credentials**:
   - Use our credential management script: `npm run db:credentials`
   - Or manually add to your `~/.zshrc`:
     ```bash
     # ArangoDB Configuration
     export ARANGO_URL="https://your-deployment.arangodb.cloud:8529"
     export ARANGO_USERNAME="your-username"
     export ARANGO_PASSWORD="your-password"
     export ARANGO_DB_NAME="candid_connections"
     ```
   - Run `source ~/.zshrc` to load the configuration

##### For Local Development:

If you're using the local Docker setup:
- **ARANGO_URL**: `http://localhost:8529`
- **ARANGO_USERNAME**: `root`
- **ARANGO_PASSWORD**: `rootpassword`
- **ARANGO_DB_NAME**: `candid_connections`

##### Automatic Credential Sync with AWS Amplify:

Once you have your credentials set up in your shell configuration, you can automatically sync them with AWS Amplify:

1. **Using the credential management script**:
   ```bash
   npm run db:credentials
   # Choose option 4: "Sync credentials with AWS Amplify"
   ```

2. **Manual sync using AWS CLI**:
   ```bash
   # Get your Amplify app ID
   amplify status -v

   # Set environment variables
   aws amplify put-backend-environment \
     --app-id YOUR_APP_ID \
     --environment-name main \
     --environment-variables \
       ARANGO_URL="$ARANGO_URL" \
       ARANGO_USERNAME="$ARANGO_USERNAME" \
       ARANGO_PASSWORD="$ARANGO_PASSWORD" \
       ARANGO_DB_NAME="$ARANGO_DB_NAME"
   ```

This ensures your production deployment has the same credentials as your local development environment.

### 4. AWS Amplify Deployment

#### 4.1. Run the automated Amplify setup script:
```bash
chmod +x amplify-setup.sh
./amplify-setup.sh
```

This script will:
- Check if AWS CLI and Amplify CLI are installed
- Verify AWS credentials
- Initialize Amplify in your project
- Add hosting
- Push changes to Amplify

If you prefer to do these steps manually, follow the instructions below:

#### 4.2. Install the AWS Amplify CLI:
```bash
npm install -g @aws-amplify/cli
```

#### 4.3. Configure the Amplify CLI:
```bash
amplify configure
```

Follow the prompts to create an IAM user with appropriate permissions.

#### 4.4. Initialize Amplify in your project:
```bash
amplify init
```

Select appropriate options for your project.

#### 4.5. Add hosting:
```bash
amplify add hosting
```

Choose "Hosting with Amplify Console" and "Continuous deployment".

#### 4.6. Push your changes to Amplify:
```bash
amplify push
```

#### 4.7. Transfer Environment Variables to Amplify

To transfer your ArangoDB credentials to Amplify:

1. Go to the AWS Amplify Console
2. Select your app
3. Go to "Environment variables"
4. Add the following variables:
   ```
   ARANGO_URL=<your-arangodb-url>
   ARANGO_USERNAME=<your-arangodb-username>
   ARANGO_PASSWORD=<your-arangodb-password>
   ARANGO_DB_NAME=candid_connections
   ```

You can use the AWS CLI to automate this:
```bash
aws amplify create-backend-environment \
  --app-id <your-amplify-app-id> \
  --environment-name prod \
  --variables ARANGO_URL=<your-arangodb-url>,ARANGO_USERNAME=<your-arangodb-username>,ARANGO_PASSWORD=<your-arangodb-password>,ARANGO_DB_NAME=candid_connections
```

#### 4.8. Initialize the production database:
```bash
# Set environment variables for the production database
export ARANGO_URL=<your-arangodb-url>
export ARANGO_USERNAME=<your-arangodb-username>
export ARANGO_PASSWORD=<your-arangodb-password>
export ARANGO_DB_NAME=candid_connections

# Run the initialization script
node scripts/init-prod-db.js
```

#### 4.9. Deploy your application:
```bash
amplify publish
```

#### 4.10. Set up a custom domain:

You can set up a custom domain for your Amplify app using our automated script:

```bash
npm run amplify:domain
```

This script will:
1. Get your Amplify app ID
2. Set up a subdomain (default: candid.pbradygeorgen.com)
3. Provide DNS records that you need to add to your domain provider

Alternatively, you can do this manually:

1. Go to the AWS Amplify Console
2. Select your app
3. Go to "Domain management"
4. Click "Add domain"
5. Enter your domain (e.g., pbradygeorgen.com)
6. Add a subdomain prefix (e.g., candid)
7. Follow the instructions to add DNS records to your domain provider

After DNS propagation (which can take up to 48 hours), your app will be available at the custom domain.

## Development

### Project Structure

```
candid-connections/
├── docker/
│   └── docker-compose.yml
├── scripts/
│   ├── init-db.js
│   └── seed-data.js
├── src/
│   ├── app/
│   │   ├── api/
│   │   │   ├── job-seekers/
│   │   │   ├── companies/
│   │   │   ├── hiring-authorities/
│   │   │   ├── positions/
│   │   │   ├── skills/
│   │   │   └── matches/
│   │   ├── job-seekers/
│   │   ├── companies/
│   │   ├── positions/
│   │   ├── matches/
│   │   ├── layout.tsx
│   │   └── page.tsx
│   ├── components/
│   │   ├── ui/
│   │   ├── job-seekers/
│   │   ├── companies/
│   │   ├── hiring-authorities/
│   │   ├── positions/
│   │   └── graph/
│   └── lib/
│       └── db/
│           └── connection.ts
├── .env
├── .env.local
├── .gitignore
├── next.config.js
├── package.json
├── README.md
└── tsconfig.json
```

### Environment Variables

Create a `.env.local` file with the following variables:

```
NEXT_PUBLIC_API_URL=http://localhost:3000
ARANGO_URL=http://localhost:8529
ARANGO_USERNAME=root
ARANGO_PASSWORD=rootpassword
ARANGO_DB_NAME=candid_connections
```

## Deployment

### AWS Amplify Gen 2 Deployment

AWS Amplify is a set of tools and services that enables developers to build full-stack applications. Amplify Gen 2 provides a more streamlined experience for deploying Next.js applications.

#### Setting Up AWS Amplify

1. **Install the AWS Amplify CLI:**
   ```bash
   npm install -g @aws-amplify/cli
   ```

2. **Configure the Amplify CLI:**
   ```bash
   amplify configure
   ```
   Follow the prompts to create an IAM user with appropriate permissions:
   - Sign in to the AWS Console
   - Create a new IAM user with AdministratorAccess policy
   - Save the access key ID and secret access key
   - Enter these credentials when prompted by the Amplify CLI

3. **Initialize Amplify in your project:**
   ```bash
   amplify init
   ```
   Select the following options:
   - Enter a name for the project: `candid-connections`
   - Choose your default editor
   - Choose the type of app: `javascript`
   - Choose the framework: `react`
   - Choose the source directory path: `src`
   - Choose the distribution directory path: `.next`
   - Choose the build command: `npm run build`
   - Choose the start command: `npm run start`

4. **Add hosting:**
   ```bash
   amplify add hosting
   ```
   Choose "Hosting with Amplify Console" and "Continuous deployment".

5. **Push your changes to Amplify:**
   ```bash
   amplify push
   ```

6. **Deploy your application:**
   ```bash
   amplify publish
   ```

7. **Update environment variables in the Amplify Console:**
   - Go to the AWS Amplify Console
   - Select your app
   - Go to "Environment variables"
   - Add the following variables:
     ```
     ARANGO_URL=<your-arangodb-url>
     ARANGO_USERNAME=<your-arangodb-username>
     ARANGO_PASSWORD=<your-arangodb-password>
     ARANGO_DB_NAME=candid_connections
     ```

### Database Deployment Options

#### Option 1: ArangoDB Cloud (Recommended for Production)

ArangoDB offers a managed cloud service that can be used for production deployments.

1. **Sign up for ArangoDB Cloud:**
   - Go to [ArangoDB Cloud](https://cloud.arangodb.com/)
   - Create an account and sign in

2. **Create a new deployment:**
   - Click "Create Deployment"
   - Choose a cloud provider (AWS, Google Cloud, or Azure)
   - Select a region close to your users
   - Choose a deployment size (start with a small instance for testing)
   - Set up authentication (save these credentials)
   - Create the deployment

3. **Configure security:**
   - Set up IP allowlisting to restrict access to your application servers
   - Create a dedicated database user with appropriate permissions

4. **Update your environment variables:**
   - Update the `.env.local` file with the cloud instance details:
     ```
     ARANGO_URL=<your-arangodb-cloud-url>
     ARANGO_USERNAME=<your-arangodb-username>
     ARANGO_PASSWORD=<your-arangodb-password>
     ARANGO_DB_NAME=candid_connections
     ```

5. **Initialize and seed the database:**
   - Run the initialization script against the cloud instance:
     ```bash
     ARANGO_URL=<your-arangodb-cloud-url> ARANGO_USERNAME=<username> ARANGO_PASSWORD=<password> node scripts/init-db.js
     ```
   - Run the seed script:
     ```bash
     ARANGO_URL=<your-arangodb-cloud-url> ARANGO_USERNAME=<username> ARANGO_PASSWORD=<password> node scripts/seed-data.js
     ```

#### Option 2: Self-Hosted on AWS EC2

You can deploy ArangoDB on AWS using EC2 for more control over your database.

1. **Launch an EC2 instance:**
   - Go to the AWS EC2 Console
   - Launch a new instance (t3.medium or larger recommended)
   - Choose Amazon Linux 2 or Ubuntu as the operating system
   - Configure security groups to allow inbound traffic on port 8529
   - Launch the instance and connect to it

2. **Install Docker on the EC2 instance:**
   ```bash
   sudo yum update -y
   sudo amazon-linux-extras install docker
   sudo service docker start
   sudo usermod -a -G docker ec2-user
   sudo chkconfig docker on
   ```

3. **Deploy ArangoDB using Docker:**
   ```bash
   docker run -d --name arangodb \
     -p 8529:8529 \
     -e ARANGO_ROOT_PASSWORD=<secure-password> \
     -v arangodb_data:/var/lib/arangodb3 \
     -v arangodb_apps:/var/lib/arangodb3-apps \
     arangodb:latest
   ```

4. **Configure security:**
   - Set up a more secure password
   - Configure the EC2 security group to only allow access from your application servers

5. **Update your environment variables:**
   - Update the environment variables in your Amplify deployment:
     ```
     ARANGO_URL=http://<your-ec2-public-ip>:8529
     ARANGO_USERNAME=root
     ARANGO_PASSWORD=<your-secure-password>
     ARANGO_DB_NAME=candid_connections
     ```

6. **Initialize and seed the database:**
   - Run the initialization and seed scripts as in Option 1

#### Option 3: AWS DynamoDB + Neptune Hybrid Approach

For a fully managed AWS solution, you can use a combination of DynamoDB and Neptune:

1. **Set up DynamoDB tables:**
   - Go to the AWS DynamoDB Console
   - Create tables for entities that don't require graph relationships:
     - Users
     - Settings
     - Analytics
     - Cached data

2. **Set up Amazon Neptune:**
   - Go to the AWS Neptune Console
   - Create a new Neptune cluster
   - Configure security groups to allow access from your application
   - Create the necessary graph structure

3. **Modify the application code:**
   - Update the database connection utilities to work with both databases
   - Store document data in DynamoDB
   - Store relationship data in Neptune

4. **Update environment variables:**
   - Add DynamoDB and Neptune connection details to your environment variables

This approach requires more complex code but leverages fully managed AWS services for better scalability and reliability.

### AWS DynamoDB Integration

While the primary database for this application is ArangoDB (a graph database), certain data that doesn't require graph relationships could be stored in AWS DynamoDB for better scalability and integration with other AWS services.

#### Data to Store in DynamoDB

- **User Authentication Data**: User credentials, session information
- **Application Settings**: Configuration settings, feature flags
- **Analytics Data**: Usage statistics, event logs
- **Cached Data**: Frequently accessed data that doesn't change often

#### Implementation Steps

1. **Add the AWS SDK to your project:**
   ```bash
   npm install aws-sdk
   ```

2. **Create DynamoDB tables using the AWS CLI:**
   First, configure the AWS CLI with your credentials:
   ```bash
   aws configure
   ```

   Then create the necessary tables:
   ```bash
   # Users table
   aws dynamodb create-table \
     --table-name candid-connections-users \
     --attribute-definitions AttributeName=id,AttributeType=S \
     --key-schema AttributeName=id,KeyType=HASH \
     --billing-mode PAY_PER_REQUEST

   # Settings table
   aws dynamodb create-table \
     --table-name candid-connections-settings \
     --attribute-definitions AttributeName=key,AttributeType=S \
     --key-schema AttributeName=key,KeyType=HASH \
     --billing-mode PAY_PER_REQUEST

   # Analytics table
   aws dynamodb create-table \
     --table-name candid-connections-analytics \
     --attribute-definitions \
       AttributeName=eventType,AttributeType=S \
       AttributeName=timestamp,AttributeType=N \
     --key-schema \
       AttributeName=eventType,KeyType=HASH \
       AttributeName=timestamp,KeyType=RANGE \
     --billing-mode PAY_PER_REQUEST
   ```

3. **Create a DynamoDB utility in your project:**
   ```typescript
   // src/lib/db/dynamodb.ts
   import AWS from 'aws-sdk';

   // Initialize AWS SDK
   AWS.config.update({
     region: process.env.AWS_REGION || 'us-east-1',
     accessKeyId: process.env.AWS_ACCESS_KEY_ID,
     secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
   });

   // Create DynamoDB client
   const dynamoDb = new AWS.DynamoDB.DocumentClient();

   // User-related functions
   export const userOperations = {
     // Create or update a user
     async saveUser(user: any) {
       const params = {
         TableName: 'candid-connections-users',
         Item: {
           id: user.id,
           email: user.email,
           name: user.name,
           createdAt: user.createdAt || Date.now(),
           updatedAt: Date.now(),
           ...user
         }
       };

       await dynamoDb.put(params).promise();
       return params.Item;
     },

     // Get a user by ID
     async getUserById(id: string) {
       const params = {
         TableName: 'candid-connections-users',
         Key: { id }
       };

       const result = await dynamoDb.get(params).promise();
       return result.Item;
     }
   };

   // Settings-related functions
   export const settingsOperations = {
     // Save a setting
     async saveSetting(key: string, value: any) {
       const params = {
         TableName: 'candid-connections-settings',
         Item: {
           key,
           value,
           updatedAt: Date.now()
         }
       };

       await dynamoDb.put(params).promise();
       return params.Item;
     },

     // Get a setting by key
     async getSettingByKey(key: string) {
       const params = {
         TableName: 'candid-connections-settings',
         Key: { key }
       };

       const result = await dynamoDb.get(params).promise();
       return result.Item;
     }
   };

   // Analytics-related functions
   export const analyticsOperations = {
     // Log an event
     async logEvent(eventType: string, data: any) {
       const params = {
         TableName: 'candid-connections-analytics',
         Item: {
           eventType,
           timestamp: Date.now(),
           data
         }
       };

       await dynamoDb.put(params).promise();
       return params.Item;
     },

     // Get events by type and time range
     async getEventsByTypeAndTimeRange(eventType: string, startTime: number, endTime: number) {
       const params = {
         TableName: 'candid-connections-analytics',
         KeyConditionExpression: 'eventType = :eventType AND timestamp BETWEEN :startTime AND :endTime',
         ExpressionAttributeValues: {
           ':eventType': eventType,
           ':startTime': startTime,
           ':endTime': endTime
         }
       };

       const result = await dynamoDb.query(params).promise();
       return result.Items;
     }
   };
   ```

4. **Update your environment variables to include AWS credentials:**
   Add these to your `.env.local` file for local development:
   ```
   AWS_REGION=us-east-1
   AWS_ACCESS_KEY_ID=your-access-key
   AWS_SECRET_ACCESS_KEY=your-secret-key
   ```

   And add them to your Amplify environment variables for production:
   - Go to the AWS Amplify Console
   - Select your app
   - Go to "Environment variables"
   - Add the AWS credentials

5. **Create API routes for DynamoDB data:**
   ```typescript
   // src/app/api/users/route.ts
   import { NextRequest, NextResponse } from 'next/server';
   import { userOperations } from '@/lib/db/dynamodb';

   export async function GET(request: NextRequest) {
     try {
       const searchParams = request.nextUrl.searchParams;
       const userId = searchParams.get('id');

       if (!userId) {
         return NextResponse.json(
           { error: 'User ID is required' },
           { status: 400 }
         );
       }

       const user = await userOperations.getUserById(userId);

       if (!user) {
         return NextResponse.json(
           { error: 'User not found' },
           { status: 404 }
         );
       }

       return NextResponse.json(user);
     } catch (error) {
       console.error('Error fetching user:', error);
       return NextResponse.json(
         { error: 'Failed to fetch user' },
         { status: 500 }
       );
     }
   }

   export async function POST(request: NextRequest) {
     try {
       const data = await request.json();

       if (!data.id || !data.email) {
         return NextResponse.json(
           { error: 'User ID and email are required' },
           { status: 400 }
         );
       }

       const user = await userOperations.saveUser(data);

       return NextResponse.json(user, { status: 201 });
     } catch (error) {
       console.error('Error creating user:', error);
       return NextResponse.json(
         { error: 'Failed to create user' },
         { status: 500 }
       );
     }
   }
   ```

## Why Graph Databases?

### Benefits of Using ArangoDB for This Application

1. **Natural Representation of Relationships**

   Graph databases like ArangoDB naturally represent the relationships between job seekers, skills, companies, hiring authorities, and positions. These relationships are first-class citizens in the database, making it easy to traverse and query complex networks of connections.

2. **Efficient Matching Algorithms**

   Finding matches between job seekers and positions requires traversing multiple relationships (job seeker → skills → positions → hiring authorities → companies). Graph databases excel at these types of traversals, making the matching process efficient and performant.

3. **Flexible Schema**

   As the application evolves, we may need to add new types of relationships or attributes. Graph databases provide schema flexibility that allows the data model to evolve without major migrations.

4. **Multi-Model Capabilities**

   ArangoDB is a multi-model database that supports document, key-value, and graph data models. This allows us to use the most appropriate model for each part of our application while maintaining a single database.

5. **Powerful Query Language**

   ArangoDB's AQL (ArangoDB Query Language) provides powerful graph traversal capabilities that make it easy to express complex matching queries that would be difficult or inefficient in a traditional relational database.

6. **Visualization-Friendly Data**

   Graph data naturally lends itself to visualization, making it easier to create interactive visualizations of the connections between entities in our application.
