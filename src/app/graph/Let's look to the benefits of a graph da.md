Let's look to the benefits of a graph database and it's application to a social connection platform that would find relationships between job seekers and employers by using the aspects of a resume from a job seeker to a potential employer by marrying the employer's desired skill set to the job seeker. The goal would be to look through the organization hierarchy of a company with various levels of hiring authorities (HR / VP/ CTO/ CEO, etc) to find the correct hiring authority that matches with the job seeker's skills and requirements. Let's build a sample Next.js project that would accomplish this goal by generating a zsh shell script (or bash) that would generate this project for local development in a Next.js application with a graph database as its back end. This should include exhaustive documentation of why particular graph database features are used and have a number of mock applicants and a mock company with various tiers of hiring authority to match the applicant with. The company should base its levels of hiring authority on the scale of the number of its current employees in relationship to its size (i.e a startup of under a hundred people would more likely direct the job seeker to higher tiers of hiring authority, whereas a company of over 1000 employees would more likely have an HR department that would have its own hierarchy of hiring authority) - the weight of finding connections between the job seeker and the company would be driven by the connecting edges of the nodes of interconnections of skills and experience requirements shared between the job seeker and the hiring authority's requirements. We should use a best of breed React based visualization library to represent the graph database that should visualize the interconnections between the job seeker and company and hiring authority. The script should use Next.js 15 and have pages for each job seeker, company, and subsequent hiring authority that can be navigated as well as sub pages of each that will show the node based visualization of their relative connections utilizing a React visualization library to interact with and explore the connections. We should install all dependencies necessary to install and populate a local graph database with the sample data and provide options to deploy the database to AWS so that the sample data can be pushed to a live production environment when local testing is completed within the script's generated project. We will need to include configuration and project architecture in the project's README.md file as well as Mermaid visualizations of the project architecture and database architecture in the README.md documentation. When complete we should have a shell script that will generate a ready to run Next.js application with a robust set of sample data stored in a graph database ready to test locally and then push to production in AWS.

let's include a generated UI with CRUD capabilities into the project, using Next.js 15 api structure to first run a local instance of the data in the database including all mock data an their relationships to test, and in a format that will be easy to use AWS Amplify Gen 2 to deploy with - the script should generate the entire app and hydrate all mock data to the local and subsequently deployable graph database to explore the relationships of job seekers to hiring authorities - it seems that neo4j will only allow api access on a paid tier - is there a free alternative to begin to experiment with?

can the script automatically generate all of the pages with associated CRUD capabilities included, thus the script would make pages coherent to the identities created in the graph database with a home dashboard that will allow for exploration of the identities from an admin user standpoint and individual pages for each job-seeker, company, and hiring-authority that would intuitively display relevant matches in a list hierarchy as well as the graph based connections that show why they are a better match in relationship between job-seekers and hiring-authorities (thus the mock data could have multiple hiring authorities looking for different positions, potentially multiple positions under the same hiring-authority within a company) - this would visualize to a job-seeker what companies have multiple positions available that rank in their node based connections and would visualize to a company what job-seekers meet multiple requirements that any given number of hiring-authorities may have in positions to fill - thus we may want to associate positions and requirements in relationship to the job-seeker's skill set and resume 

let's wrap all of this up into a single bash shell script that will generate this application in the current directory that it is run from - thus including the shell script within the generated Next.js application that will be generated around it in the current directory