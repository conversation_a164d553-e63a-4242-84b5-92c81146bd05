import { Database } from 'arangojs';

// Create a singleton instance of the database connection
let dbInstance: Database | null = null;

export function getDatabase(): Database {
  if (!dbInstance) {
    dbInstance = new Database({
      url: process.env.ARANGO_URL || 'http://localhost:8529',
      auth: {
        username: process.env.ARANGO_USERNAME || 'root',
        password: process.env.ARANGO_PASSWORD || 'rootpassword',
      },
      databaseName: process.env.ARANGO_DB_NAME || 'candid_connections',
    });
  }
  
  return dbInstance;
}

// Get collection references
export function getCollections() {
  const db = getDatabase();
  
  return {
    jobSeekers: db.collection('jobSeekers'),
    companies: db.collection('companies'),
    hiringAuthorities: db.collection('hiringAuthorities'),
    positions: db.collection('positions'),
    skills: db.collection('skills'),
    seekerSkills: db.collection('seekerSkills'),
    positionSkills: db.collection('positionSkills'),
    companyHiringAuthorities: db.collection('companyHiringAuthorities'),
    hiringAuthorityPositions: db.collection('hiringAuthorityPositions'),
  };
}
