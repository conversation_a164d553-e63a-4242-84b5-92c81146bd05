import AWS from 'aws-sdk';

// Initialize AWS SDK
AWS.config.update({
  region: process.env.AWS_REGION || 'us-east-1',
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
});

// Create DynamoDB client
const dynamoDb = new AWS.DynamoDB.DocumentClient();

// User-related functions
export const userOperations = {
  // Create or update a user
  async saveUser(user: any) {
    const params = {
      TableName: 'candid-connections-users',
      Item: {
        id: user.id,
        email: user.email,
        name: user.name,
        createdAt: user.createdAt || Date.now(),
        updatedAt: Date.now(),
        ...user
      }
    };
    
    await dynamoDb.put(params).promise();
    return params.Item;
  },
  
  // Get a user by ID
  async getUserById(id: string) {
    const params = {
      TableName: 'candid-connections-users',
      Key: { id }
    };
    
    const result = await dynamoDb.get(params).promise();
    return result.Item;
  }
};

// Settings-related functions
export const settingsOperations = {
  // Save a setting
  async saveSetting(key: string, value: any) {
    const params = {
      TableName: 'candid-connections-settings',
      Item: {
        key,
        value,
        updatedAt: Date.now()
      }
    };
    
    await dynamoDb.put(params).promise();
    return params.Item;
  },
  
  // Get a setting by key
  async getSettingByKey(key: string) {
    const params = {
      TableName: 'candid-connections-settings',
      Key: { key }
    };
    
    const result = await dynamoDb.get(params).promise();
    return result.Item;
  }
};

// Analytics-related functions
export const analyticsOperations = {
  // Log an event
  async logEvent(eventType: string, data: any) {
    const params = {
      TableName: 'candid-connections-analytics',
      Item: {
        eventType,
        timestamp: Date.now(),
        data
      }
    };
    
    await dynamoDb.put(params).promise();
    return params.Item;
  },
  
  // Get events by type and time range
  async getEventsByTypeAndTimeRange(eventType: string, startTime: number, endTime: number) {
    const params = {
      TableName: 'candid-connections-analytics',
      KeyConditionExpression: 'eventType = :eventType AND timestamp BETWEEN :startTime AND :endTime',
      ExpressionAttributeValues: {
        ':eventType': eventType,
        ':startTime': startTime,
        ':endTime': endTime
      }
    };
    
    const result = await dynamoDb.query(params).promise();
    return result.Items;
  }
};
