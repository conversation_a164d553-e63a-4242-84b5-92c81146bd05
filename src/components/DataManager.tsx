'use client';

import { useState, useEffect } from 'react';
import { Database } from 'arangojs';

interface DataRecord {
  _id: string;
  _key: string;
  [key: string]: any;
}

interface DataManagerProps {
  collection: string;
  title: string;
  icon: string;
}

export default function DataManager({ collection, title, icon }: DataManagerProps) {
  const [records, setRecords] = useState<DataRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedRecord, setSelectedRecord] = useState<DataRecord | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState<Record<string, any>>({});

  useEffect(() => {
    fetchRecords();
  }, [collection]);

  const fetchRecords = async () => {
    try {
      setIsLoading(true);

      const db = new Database({
        url: process.env.NEXT_PUBLIC_ARANGO_URL || 'https://f2c8a97499a8.arangodb.cloud:8529',
        auth: {
          username: process.env.NEXT_PUBLIC_ARANGO_USERNAME || 'root',
          password: process.env.NEXT_PUBLIC_ARANGO_PASSWORD || 'XMx2qSsHU8RWMX9VSxAx'
        },
        databaseName: process.env.NEXT_PUBLIC_ARANGO_DB_NAME || 'candid_connections'
      });

      const cursor = await db.query(`FOR doc IN ${collection} RETURN doc`);
      const data = await cursor.all();
      setRecords(data);
    } catch (err) {
      console.error('Error fetching records:', err);
      setError('Failed to load records');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEdit = (record: DataRecord) => {
    setSelectedRecord(record);
    setEditForm({ ...record });
    setIsEditing(true);
  };

  const handleSave = async () => {
    try {
      const db = new Database({
        url: process.env.NEXT_PUBLIC_ARANGO_URL || 'https://f2c8a97499a8.arangodb.cloud:8529',
        auth: {
          username: process.env.NEXT_PUBLIC_ARANGO_USERNAME || 'root',
          password: process.env.NEXT_PUBLIC_ARANGO_PASSWORD || 'XMx2qSsHU8RWMX9VSxAx'
        },
        databaseName: process.env.NEXT_PUBLIC_ARANGO_DB_NAME || 'candid_connections'
      });

      await db.collection(collection).update(editForm._key, editForm);

      // Update local state
      setRecords(records.map(record =>
        record._key === editForm._key ? editForm : record
      ));

      setIsEditing(false);
      setSelectedRecord(null);
    } catch (err) {
      console.error('Error saving record:', err);
      setError('Failed to save record');
    }
  };

  const handleDelete = async (record: DataRecord) => {
    if (!confirm(`Are you sure you want to delete this ${title.toLowerCase()}?`)) {
      return;
    }

    try {
      const db = new Database({
        url: process.env.NEXT_PUBLIC_ARANGO_URL || 'https://f2c8a97499a8.arangodb.cloud:8529',
        auth: {
          username: process.env.NEXT_PUBLIC_ARANGO_USERNAME || 'root',
          password: process.env.NEXT_PUBLIC_ARANGO_PASSWORD || 'XMx2qSsHU8RWMX9VSxAx'
        },
        databaseName: process.env.NEXT_PUBLIC_ARANGO_DB_NAME || 'candid_connections'
      });

      await db.collection(collection).remove(record._key);

      // Update local state
      setRecords(records.filter(r => r._key !== record._key));
    } catch (err) {
      console.error('Error deleting record:', err);
      setError('Failed to delete record');
    }
  };

  const handleInputChange = (key: string, value: any) => {
    setEditForm(prev => ({ ...prev, [key]: value }));
  };

  const getDisplayFields = (record: DataRecord) => {
    return Object.entries(record).filter(([key]) => !key.startsWith('_'));
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-4 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center">
          <div className="text-red-600 text-4xl mb-4">⚠️</div>
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchRecords}
            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <span className="text-2xl mr-2">{icon}</span>
          {title} ({records.length})
        </h3>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {records.length > 0 && getDisplayFields(records[0]).map(([key]) => (
                <th key={key} className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {key.replace(/([A-Z])/g, ' $1').trim()}
                </th>
              ))}
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {records.map((record) => (
              <tr key={record._key} className="hover:bg-gray-50">
                {getDisplayFields(record).map(([key, value]) => (
                  <td key={key} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {String(value)}
                  </td>
                ))}
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button
                    onClick={() => handleEdit(record)}
                    className="text-indigo-600 hover:text-indigo-900"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleDelete(record)}
                    className="text-red-600 hover:text-red-900"
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Edit Modal */}
      {isEditing && selectedRecord && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Edit {title.slice(0, -1)}
              </h3>

              <div className="space-y-4">
                {getDisplayFields(selectedRecord).map(([key, value]) => (
                  <div key={key}>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {key.replace(/([A-Z])/g, ' $1').trim()}
                    </label>
                    <input
                      type="text"
                      value={editForm[key] || ''}
                      onChange={(e) => handleInputChange(key, e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    />
                  </div>
                ))}
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setIsEditing(false)}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
                >
                  Save
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
