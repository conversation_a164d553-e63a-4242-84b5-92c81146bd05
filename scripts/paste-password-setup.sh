#!/bin/bash

# Colors for output
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

echo -e "${BLUE}🔐 Copy/Paste Friendly Password Setup${NC}"
echo -e "${YELLOW}This method is guaranteed to work with copy/paste!${NC}"
echo

# Pre-filled values (updated based on your screenshot)
URL="https://12c8a97499a8.arangodb.cloud"
USERNAME="root"
DB_NAME="candid_connections"

echo -e "${GREEN}Auto-detected values:${NC}"
echo -e "URL: ${BLUE}$URL${NC}"
echo -e "Username: ${BLUE}$USERNAME${NC}"
echo -e "Database: ${BLUE}$DB_NAME${NC}"
echo

echo -e "${YELLOW}📋 Step-by-step instructions:${NC}"
echo -e "1. In your browser, look at the 'Root password' field"
echo -e "2. You should see: Root password: **************** 📋"
echo -e "3. ${BLUE}Click the copy button (📋) next to the asterisks${NC}"
echo -e "4. ${RED}DO NOT try to select the asterisks - use the copy button!${NC}"
echo -e "5. Come back here and paste it (Cmd+V or Ctrl+V)"
echo

# Create a temporary file for the password
TEMP_FILE=$(mktemp)
trap "rm -f $TEMP_FILE" EXIT

echo -e "${BLUE}Method 1: Direct paste (recommended)${NC}"
echo -e "Paste your password and press Enter:"
echo -n "> "

# Read the password with echo enabled for copy/paste compatibility
IFS= read -r PASSWORD

if [ -z "$PASSWORD" ]; then
  echo -e "\n${YELLOW}No password entered. Let's try an alternative method...${NC}"
  echo

  echo -e "${BLUE}Method 2: File-based input${NC}"
  echo -e "1. Open a text editor (TextEdit, nano, etc.)"
  echo -e "2. Paste your password there"
  echo -e "3. Save it as 'temp_password.txt' in this directory"
  echo -e "4. Press Enter here when ready"
  echo

  read -p "Press Enter when you've saved the password file..."

  if [ -f "temp_password.txt" ]; then
    PASSWORD=$(cat temp_password.txt)
    rm -f temp_password.txt
    echo -e "${GREEN}✓ Password read from file${NC}"
  else
    echo -e "${RED}File not found. Exiting.${NC}"
    exit 1
  fi
fi

# Clear the line to hide the password
echo -e "\033[1A\033[2K\r${GREEN}✓ Password received (hidden for security)${NC}"

if [ -z "$PASSWORD" ]; then
  echo -e "${RED}Password is required!${NC}"
  exit 1
fi

echo -e "\n${YELLOW}Testing connection...${NC}"

# Test connection
RESPONSE=$(curl -s -w "%{http_code}" -o /dev/null \
  -u "$USERNAME:$PASSWORD" \
  "$URL/_api/version")

if [ "$RESPONSE" = "200" ]; then
  echo -e "${GREEN}✓ Connection successful!${NC}"

  # Get ArangoDB version
  VERSION=$(curl -s -u "$USERNAME:$PASSWORD" \
    "$URL/_api/version" | grep -o '"version":"[^"]*"' | cut -d'"' -f4)
  echo -e "${GREEN}ArangoDB Version: $VERSION${NC}"

  echo -e "\n${YELLOW}Adding credentials to ~/.zshrc...${NC}"

  # Backup existing .zshrc
  if [ -f ~/.zshrc ]; then
    cp ~/.zshrc ~/.zshrc.backup.$(date +%Y%m%d_%H%M%S)
    echo -e "${GREEN}Backed up existing .zshrc${NC}"
  fi

  # Remove existing ArangoDB configuration if it exists
  if grep -q "# ArangoDB Configuration" ~/.zshrc 2>/dev/null; then
    echo -e "${YELLOW}Removing existing ArangoDB configuration...${NC}"
    sed -i.bak '/# ArangoDB Configuration/,/# End ArangoDB Configuration/d' ~/.zshrc
  fi

  # Add new configuration
  cat >> ~/.zshrc << EOF

# ArangoDB Configuration
export ARANGO_URL="$URL"
export ARANGO_USERNAME="$USERNAME"
export ARANGO_PASSWORD="$PASSWORD"
export ARANGO_DB_NAME="$DB_NAME"
export ARANGO_HOST="12c8a97499a8.arangodb.cloud"
export ARANGO_PORT="443"
# End ArangoDB Configuration
EOF

  # Load the new configuration in current session
  export ARANGO_URL="$URL"
  export ARANGO_USERNAME="$USERNAME"
  export ARANGO_PASSWORD="$PASSWORD"
  export ARANGO_DB_NAME="$DB_NAME"
  export ARANGO_HOST="12c8a97499a8.arangodb.cloud"
  export ARANGO_PORT="443"

  echo -e "${GREEN}✅ Credentials saved to ~/.zshrc${NC}"
  echo -e "${GREEN}✅ Credentials loaded in current session${NC}"

  echo -e "\n${BLUE}🎯 Next Steps:${NC}"
  echo -e "1. ${YELLOW}Create the database:${NC}"
  echo -e "   • Go to your ArangoDB Cloud web interface"
  echo -e "   • Click 'Databases' → 'Add Database'"
  echo -e "   • Name it: ${BLUE}candid_connections${NC}"
  echo
  echo -e "2. ${YELLOW}Initialize and seed the database:${NC}"
  echo -e "   ${BLUE}npm run db:setup-all${NC}"
  echo
  echo -e "3. ${YELLOW}Start development:${NC}"
  echo -e "   ${BLUE}npm run dev${NC}"
  echo
  echo -e "${GREEN}🚀 You're all set! Run 'source ~/.zshrc' in other terminals to load the config.${NC}"

else
  echo -e "${RED}✗ Connection failed (HTTP $RESPONSE)${NC}"
  echo -e "${YELLOW}Please check your password and try again.${NC}"
  echo -e "${YELLOW}Make sure you copied the actual password, not the asterisks.${NC}"
  exit 1
fi
