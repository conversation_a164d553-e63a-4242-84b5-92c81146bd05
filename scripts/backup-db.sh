#!/bin/bash

# Set variables
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="./backups"
ARANGO_HOST="${ARANGO_HOST:-localhost}"
ARANGO_PORT="${ARANGO_PORT:-8529}"
ARANGO_USER="${ARANGO_USERNAME:-root}"
ARANGO_PASSWORD="${ARANGO_PASSWORD:-rootpassword}"
ARANGO_DB="${ARANGO_DB_NAME:-candid_connections}"

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

# Create backup
echo "Creating backup of $ARANGO_DB database..."
arangodump \
  --server.endpoint tcp://$ARANGO_HOST:$ARANGO_PORT \
  --server.username $ARANGO_USER \
  --server.password $ARANGO_PASSWORD \
  --server.database $ARANGO_DB \
  --output-directory "$BACKUP_DIR/$ARANGO_DB-$TIMES<PERSON>MP"

# Check if backup was successful
if [ $? -eq 0 ]; then
  echo "Backup created successfully at $BACKUP_DIR/$ARANGO_DB-$TIMESTAMP"
  
  # Create a compressed archive
  echo "Creating compressed archive..."
  tar -czf "$BACKUP_DIR/$ARANGO_DB-$TIMESTAMP.tar.gz" -C "$BACKUP_DIR" "$ARANGO_DB-$TIMESTAMP"
  
  # Check if compression was successful
  if [ $? -eq 0 ]; then
    echo "Compressed archive created successfully at $BACKUP_DIR/$ARANGO_DB-$TIMESTAMP.tar.gz"
    
    # Remove the uncompressed directory
    rm -rf "$BACKUP_DIR/$ARANGO_DB-$TIMESTAMP"
    
    # Optional: Upload to S3 if AWS credentials are configured
    if command -v aws &> /dev/null; then
      echo "Uploading backup to S3..."
      aws s3 cp "$BACKUP_DIR/$ARANGO_DB-$TIMESTAMP.tar.gz" "s3://your-backup-bucket/$ARANGO_DB-$TIMESTAMP.tar.gz"
      
      if [ $? -eq 0 ]; then
        echo "Backup uploaded to S3 successfully."
      else
        echo "Failed to upload backup to S3."
      fi
    fi
  else
    echo "Failed to create compressed archive."
  fi
else
  echo "Backup failed."
fi
