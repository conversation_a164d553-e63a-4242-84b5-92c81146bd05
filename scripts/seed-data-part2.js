// Function to generate positions for a hiring authority
async function generatePositions(authority, company) {
  const positionCount = Math.floor(Math.random() * 3) + 1; // 1-3 positions per authority
  
  const positionTitles = [
    'Frontend Developer', 'Backend Developer', 'Full Stack Developer',
    'Dev<PERSON>ps Engineer', 'Data Scientist', 'UX Designer',
    'Product Manager', 'Project Manager', 'QA Engineer',
    'Mobile Developer', 'Systems Architect', 'Database Administrator'
  ];
  
  const experienceLevels = ['Entry', 'Mid', 'Senior', 'Lead', 'Principal'];
  
  for (let i = 0; i < positionCount; i++) {
    const title = positionTitles[Math.floor(Math.random() * positionTitles.length)];
    const level = experienceLevels[Math.floor(Math.random() * experienceLevels.length)];
    const fullTitle = `${level} ${title}`;
    
    // Required skills based on position
    let requiredSkills = [];
    if (title.includes('Frontend')) {
      requiredSkills = ['JavaScript', 'React', 'HTML', 'CSS'];
    } else if (title.includes('Backend')) {
      requiredSkills = ['Node.js', 'SQL', 'REST API'];
    } else if (title.includes('Full Stack')) {
      requiredSkills = ['JavaScript', 'React', 'Node.js', 'SQL'];
    } else if (title.includes('DevOps')) {
      requiredSkills = ['AWS', 'Docker', 'CI/CD'];
    } else if (title.includes('Data')) {
      requiredSkills = ['Python', 'SQL', 'NoSQL'];
    } else if (title.includes('UX')) {
      requiredSkills = ['Communication', 'Problem Solving'];
    } else if (title.includes('Product') || title.includes('Project')) {
      requiredSkills = ['Agile', 'Communication', 'Project Management'];
    } else {
      requiredSkills = ['Problem Solving', 'Communication'];
    }
    
    // Add some random skills
    const allSkillNames = skillsData.map(s => s.name);
    while (requiredSkills.length < 5) {
      const randomSkill = allSkillNames[Math.floor(Math.random() * allSkillNames.length)];
      if (!requiredSkills.includes(randomSkill)) {
        requiredSkills.push(randomSkill);
      }
    }
    
    const position = {
      title: fullTitle,
      description: `${fullTitle} position at ${company.name}`,
      location: company.location,
      remote: Math.random() > 0.5,
      salary: {
        min: 50000 + (level === 'Entry' ? 0 : level === 'Mid' ? 30000 : level === 'Senior' ? 60000 : 90000),
        max: 80000 + (level === 'Entry' ? 0 : level === 'Mid' ? 40000 : level === 'Senior' ? 80000 : 120000)
      },
      requiredExperience: level === 'Entry' ? 0 : level === 'Mid' ? 2 : level === 'Senior' ? 5 : 8,
      hiringAuthorityId: authority._id,
      skillIds: requiredSkills
    };
    
    const positionDoc = await positions.save(position);
    positionsData.push(positionDoc);
    
    // Connect position to hiring authority
    await hiringAuthorityPositions.save({
      _from: authority._id,
      _to: positionDoc._id,
      relationship: 'responsible_for'
    });
  }
}

// Function to connect job seekers to skills
async function connectJobSeekersToSkills(jobSeekerDocs) {
  for (const seeker of jobSeekerDocs) {
    for (const skillName of seeker.skillIds) {
      const skillDoc = await skills.document(skillName);
      
      await seekerSkills.save({
        _from: seeker._id,
        _to: skillDoc._id,
        proficiency: Math.floor(Math.random() * 5) + 1, // 1-5 proficiency level
        yearsOfExperience: Math.floor(Math.random() * seeker.experience) + 1
      });
    }
  }
}

// Function to connect positions to skills
async function connectPositionsToSkills() {
  for (const position of positionsData) {
    for (const skillName of position.skillIds) {
      const skillDoc = await skills.document(skillName);
      
      await positionSkills.save({
        _from: position._id,
        _to: skillDoc._id,
        importance: Math.floor(Math.random() * 5) + 1, // 1-5 importance level
        required: Math.random() > 0.3 // 70% chance of being required
      });
    }
  }
}

// Run the seed function
seedDatabase();
