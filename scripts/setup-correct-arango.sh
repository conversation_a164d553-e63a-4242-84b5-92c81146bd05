#!/bin/bash

# Colors for output
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

echo -e "${BLUE}🎉 Setting Up ArangoDB with Correct Credentials${NC}"
echo

# Correct credentials from your dashboard
URL="https://f2c8a97499a8.arangodb.cloud:8529"
USERNAME="root"
PASSWORD="XMx2qSsHU8RWMX9VSxAx"
DB_NAME="candid_connections"

echo -e "${GREEN}Using correct credentials:${NC}"
echo -e "URL: ${BLUE}$URL${NC}"
echo -e "Username: ${BLUE}$USERNAME${NC}"
echo -e "Database: ${BLUE}$DB_NAME${NC}"
echo

echo -e "${YELLOW}Testing connection...${NC}"

# Test connection
RESPONSE=$(curl -s -w "%{http_code}" -o /dev/null \
  -u "$USERNAME:$PASSWORD" \
  "$URL/_api/version")

if [ "$RESPONSE" = "200" ]; then
  echo -e "${GREEN}✓ Connection successful!${NC}"
  
  # Get version info
  VERSION=$(curl -s -u "$USERNAME:$PASSWORD" \
    "$URL/_api/version" | grep -o '"version":"[^"]*"' | cut -d'"' -f4)
  echo -e "${GREEN}ArangoDB Version: $VERSION${NC}"
  
  echo -e "\n${YELLOW}Setting up environment variables...${NC}"
  
  # Backup existing .zshrc
  if [ -f ~/.zshrc ]; then
    cp ~/.zshrc ~/.zshrc.backup.$(date +%Y%m%d_%H%M%S)
    echo -e "${GREEN}Backed up existing .zshrc${NC}"
  fi
  
  # Remove existing ArangoDB configuration if it exists
  if grep -q "# ArangoDB Configuration" ~/.zshrc 2>/dev/null; then
    echo -e "${YELLOW}Removing existing ArangoDB configuration...${NC}"
    sed -i.bak '/# ArangoDB Configuration/,/# End ArangoDB Configuration/d' ~/.zshrc
  fi
  
  # Add new configuration
  cat >> ~/.zshrc << EOF

# ArangoDB Configuration
export ARANGO_URL="$URL"
export ARANGO_USERNAME="$USERNAME"
export ARANGO_PASSWORD="$PASSWORD"
export ARANGO_DB_NAME="$DB_NAME"
export ARANGO_HOST="f2c8a97499a8.arangodb.cloud"
export ARANGO_PORT="8529"
# End ArangoDB Configuration
EOF
  
  # Set for current session
  export ARANGO_URL="$URL"
  export ARANGO_USERNAME="$USERNAME"
  export ARANGO_PASSWORD="$PASSWORD"
  export ARANGO_DB_NAME="$DB_NAME"
  export ARANGO_HOST="f2c8a97499a8.arangodb.cloud"
  export ARANGO_PORT="8529"
  
  echo -e "${GREEN}✅ Credentials saved to ~/.zshrc${NC}"
  echo -e "${GREEN}✅ Credentials loaded in current session${NC}"
  
  # Create .env.local for the application
  echo -e "\n${YELLOW}Creating .env.local for the application...${NC}"
  cat > .env.local << EOF
NEXT_PUBLIC_API_URL=http://localhost:3000
ARANGO_URL=$URL
ARANGO_USERNAME=$USERNAME
ARANGO_PASSWORD=$PASSWORD
ARANGO_DB_NAME=$DB_NAME
EOF
  
  echo -e "${GREEN}✅ Created .env.local${NC}"
  
  echo -e "\n${BLUE}🎯 Next Steps:${NC}"
  echo -e "1. ${YELLOW}Create the database in ArangoDB Cloud:${NC}"
  echo -e "   • Go to: https://f2c8a97499a8.arangodb.cloud"
  echo -e "   • Click 'Databases' → 'Add Database'"
  echo -e "   • Name it: ${BLUE}candid_connections${NC}"
  echo
  echo -e "2. ${YELLOW}Initialize and seed the database:${NC}"
  echo -e "   ${BLUE}npm run db:setup-all${NC}"
  echo
  echo -e "3. ${YELLOW}Start development:${NC}"
  echo -e "   ${BLUE}npm run dev${NC}"
  echo
  echo -e "4. ${YELLOW}When ready to deploy:${NC}"
  echo -e "   ${BLUE}npm run amplify:setup${NC}"
  echo -e "   ${BLUE}npm run amplify:deploy${NC}"
  echo
  echo -e "${GREEN}🚀 You're all set! Run 'source ~/.zshrc' in other terminals to load the config.${NC}"
  
else
  echo -e "${RED}✗ Connection failed (HTTP $RESPONSE)${NC}"
  exit 1
fi
