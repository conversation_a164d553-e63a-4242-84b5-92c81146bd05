#!/bin/bash

# Colors for output
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

echo -e "${BLUE}🔍 Finding Your Correct ArangoDB Endpoint${NC}"
echo

# Extract deployment ID from your dashboard URL
DEPLOYMENT_ID="lsjj5kbinwbavsjhf79n"
USERNAME="root"
PASSWORD="XMx2qSsHU8RWMX9VSxAx"

echo -e "${YELLOW}Based on your deployment ID: $DEPLOYMENT_ID${NC}"
echo -e "${YELLOW}Testing possible endpoint formats...${NC}"
echo

# Test different URL formats with the correct deployment ID
URLS=(
  "https://$DEPLOYMENT_ID.arangodb.cloud:8529"
  "https://$DEPLOYMENT_ID.arangodb.cloud"
  "https://$DEPLOYMENT_ID.arangodb.cloud:443"
  "https://$DEPLOYMENT_ID.cloud.arangodb.com:8529"
  "https://$DEPLOYMENT_ID.cloud.arangodb.com"
  "https://$DEPLOYMENT_ID-8529.arangodb.cloud"
  "https://api-$DEPLOYMENT_ID.arangodb.cloud:8529"
  "https://db-$DEPLOYMENT_ID.arangodb.cloud:8529"
)

for URL in "${URLS[@]}"; do
  echo -e "${BLUE}Testing: $URL${NC}"
  
  # Test basic connectivity first
  echo -n "  DNS resolution: "
  if nslookup $(echo $URL | sed 's|https://||' | sed 's|:.*||') > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Resolves${NC}"
  else
    echo -e "${RED}✗ No DNS${NC}"
    continue
  fi
  
  # Test with credentials
  echo -n "  Connection test: "
  RESPONSE=$(curl -s -w "%{http_code}" -o /dev/null \
    --connect-timeout 10 --max-time 15 \
    -u "$USERNAME:$PASSWORD" \
    "$URL/_api/version" 2>/dev/null)
  
  if [ "$RESPONSE" = "200" ]; then
    echo -e "${GREEN}✓ SUCCESS! (HTTP $RESPONSE)${NC}"
    echo -e "${GREEN}🎯 FOUND YOUR WORKING ENDPOINT: $URL${NC}"
    
    # Get version info
    VERSION=$(curl -s -u "$USERNAME:$PASSWORD" \
      "$URL/_api/version" 2>/dev/null | grep -o '"version":"[^"]*"' | cut -d'"' -f4)
    echo -e "${GREEN}ArangoDB Version: $VERSION${NC}"
    
    echo -e "\n${YELLOW}✅ Use this configuration:${NC}"
    echo -e "${BLUE}export ARANGO_URL=\"$URL\"${NC}"
    echo -e "${BLUE}export ARANGO_USERNAME=\"$USERNAME\"${NC}"
    echo -e "${BLUE}export ARANGO_PASSWORD=\"$PASSWORD\"${NC}"
    echo -e "${BLUE}export ARANGO_DB_NAME=\"candid_connections\"${NC}"
    
    echo -e "\n${YELLOW}🚀 Want me to set this up automatically? (y/n)${NC}"
    read -p "> " setup_auto
    
    if [[ "$setup_auto" == "y" || "$setup_auto" == "Y" ]]; then
      # Backup existing .zshrc
      if [ -f ~/.zshrc ]; then
        cp ~/.zshrc ~/.zshrc.backup.$(date +%Y%m%d_%H%M%S)
        echo -e "${GREEN}Backed up existing .zshrc${NC}"
      fi
      
      # Remove existing ArangoDB configuration if it exists
      if grep -q "# ArangoDB Configuration" ~/.zshrc 2>/dev/null; then
        echo -e "${YELLOW}Removing existing ArangoDB configuration...${NC}"
        sed -i.bak '/# ArangoDB Configuration/,/# End ArangoDB Configuration/d' ~/.zshrc
      fi
      
      # Add new configuration
      cat >> ~/.zshrc << EOF

# ArangoDB Configuration
export ARANGO_URL="$URL"
export ARANGO_USERNAME="$USERNAME"
export ARANGO_PASSWORD="$PASSWORD"
export ARANGO_DB_NAME="candid_connections"
export ARANGO_HOST="$(echo $URL | sed 's|https://||' | sed 's|:.*||')"
export ARANGO_PORT="$(echo $URL | grep -o ':[0-9]*' | sed 's|:||' || echo '443')"
# End ArangoDB Configuration
EOF
      
      # Load the new configuration in current session
      export ARANGO_URL="$URL"
      export ARANGO_USERNAME="$USERNAME"
      export ARANGO_PASSWORD="$PASSWORD"
      export ARANGO_DB_NAME="candid_connections"
      
      echo -e "${GREEN}✅ Configuration saved to ~/.zshrc${NC}"
      echo -e "${GREEN}✅ Configuration loaded in current session${NC}"
      echo -e "${YELLOW}Run 'source ~/.zshrc' in other terminals to load the config.${NC}"
      
      echo -e "\n${BLUE}🎯 Next Steps:${NC}"
      echo -e "1. Create the database in ArangoDB Cloud web interface"
      echo -e "2. Run: ${BLUE}npm run db:setup-all${NC}"
      echo -e "3. Run: ${BLUE}npm run dev${NC}"
    fi
    
    exit 0
  elif [ "$RESPONSE" = "401" ]; then
    echo -e "${YELLOW}✗ Auth failed (HTTP $RESPONSE)${NC}"
  elif [ "$RESPONSE" = "000" ]; then
    echo -e "${RED}✗ No connection (HTTP $RESPONSE)${NC}"
  else
    echo -e "${YELLOW}✗ HTTP $RESPONSE${NC}"
  fi
  echo
done

echo -e "\n${RED}❌ Could not find working endpoint automatically.${NC}"
echo -e "\n${YELLOW}📋 Manual steps:${NC}"
echo -e "1. Go to your ArangoDB Cloud dashboard"
echo -e "2. Look for 'Connection' tab or 'Endpoint URL'"
echo -e "3. Copy the exact endpoint URL"
echo -e "4. Run the setup script with the correct URL"

echo -e "\n${BLUE}💡 The endpoint should look like:${NC}"
echo -e "   https://$DEPLOYMENT_ID.arangodb.cloud:8529"
echo -e "   or similar format"
