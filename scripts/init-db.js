const { Database } = require('arangojs');

async function initializeDatabase() {
  console.log('Initializing ArangoDB database...');
  
  const db = new Database({
    url: 'http://localhost:8529',
    auth: { username: 'root', password: 'rootpassword' },
    databaseName: '_system'
  });
  
  // Create database if it doesn't exist
  const dbName = 'candid_connections';
  const dbExists = await db.listDatabases().then(dbs => dbs.includes(dbName));
  
  if (!dbExists) {
    console.log(`Creating database: ${dbName}`);
    await db.createDatabase(dbName);
    console.log(`Database ${dbName} created successfully.`);
  } else {
    console.log(`Database ${dbName} already exists.`);
  }
  
  // Switch to the application database
  const appDb = db.database(dbName);
  
  // Create collections
  const collections = [
    { name: 'jobSeekers', type: 'document' },
    { name: 'companies', type: 'document' },
    { name: 'hiringAuthorities', type: 'document' },
    { name: 'positions', type: 'document' },
    { name: 'skills', type: 'document' },
    { name: 'seekerSkills', type: 'edge' },
    { name: 'positionSkills', type: 'edge' },
    { name: 'companyHiringAuthorities', type: 'edge' },
    { name: 'hiringAuthorityPositions', type: 'edge' }
  ];
  
  for (const collection of collections) {
    const collectionExists = await appDb.collections()
      .then(cols => cols.some(col => col.name === collection.name));
    
    if (!collectionExists) {
      console.log(`Creating collection: ${collection.name}`);
      if (collection.type === 'edge') {
        await appDb.createEdgeCollection(collection.name);
      } else {
        await appDb.createCollection(collection.name);
      }
      console.log(`Collection ${collection.name} created successfully.`);
    } else {
      console.log(`Collection ${collection.name} already exists.`);
    }
  }
  
  console.log('Database initialization completed successfully.');
}

initializeDatabase().catch(err => {
  console.error('Error initializing database:', err);
  process.exit(1);
});
