const { Database } = require('arangojs');
const { v4: uuidv4 } = require('uuid');

// Connect to the database
const db = new Database({
  url: 'http://localhost:8529',
  auth: { username: 'root', password: 'rootpassword' },
  databaseName: 'candid_connections'
});

// Collection references
const jobSeekers = db.collection('jobSeekers');
const companies = db.collection('companies');
const hiringAuthorities = db.collection('hiringAuthorities');
const positions = db.collection('positions');
const skills = db.collection('skills');
const seekerSkills = db.collection('seekerSkills');
const positionSkills = db.collection('positionSkills');
const companyHiringAuthorities = db.collection('companyHiringAuthorities');
const hiringAuthorityPositions = db.collection('hiringAuthorityPositions');

// Sample skills data
const skillsData = [
  { name: 'JavaScript', category: 'Programming Language', level: 'Advanced' },
  { name: 'TypeScript', category: 'Programming Language', level: 'Advanced' },
  { name: 'React', category: 'Frontend Framework', level: 'Advanced' },
  { name: 'Next.js', category: 'Frontend Framework', level: 'Advanced' },
  { name: 'Node.js', category: 'Backend Framework', level: 'Advanced' },
  { name: 'GraphQL', category: 'API', level: 'Intermediate' },
  { name: 'REST API', category: 'API', level: 'Advanced' },
  { name: 'SQL', category: 'Database', level: 'Intermediate' },
  { name: 'NoSQL', category: 'Database', level: 'Intermediate' },
  { name: 'MongoDB', category: 'Database', level: 'Intermediate' },
  { name: 'AWS', category: 'Cloud', level: 'Intermediate' },
  { name: 'Docker', category: 'DevOps', level: 'Intermediate' },
  { name: 'Kubernetes', category: 'DevOps', level: 'Beginner' },
  { name: 'CI/CD', category: 'DevOps', level: 'Intermediate' },
  { name: 'Git', category: 'Version Control', level: 'Advanced' },
  { name: 'Agile', category: 'Methodology', level: 'Advanced' },
  { name: 'Scrum', category: 'Methodology', level: 'Advanced' },
  { name: 'Project Management', category: 'Soft Skill', level: 'Advanced' },
  { name: 'Team Leadership', category: 'Soft Skill', level: 'Advanced' },
  { name: 'Communication', category: 'Soft Skill', level: 'Advanced' },
  { name: 'Problem Solving', category: 'Soft Skill', level: 'Advanced' },
  { name: 'Critical Thinking', category: 'Soft Skill', level: 'Advanced' },
  { name: 'Python', category: 'Programming Language', level: 'Intermediate' },
  { name: 'Java', category: 'Programming Language', level: 'Intermediate' },
  { name: 'C#', category: 'Programming Language', level: 'Intermediate' },
  { name: 'PHP', category: 'Programming Language', level: 'Intermediate' },
  { name: 'Ruby', category: 'Programming Language', level: 'Beginner' },
  { name: 'Go', category: 'Programming Language', level: 'Beginner' },
  { name: 'Rust', category: 'Programming Language', level: 'Beginner' },
  { name: 'Swift', category: 'Programming Language', level: 'Beginner' }
];

// Sample job seekers data
const jobSeekersData = [
  {
    name: 'John Doe',
    email: '<EMAIL>',
    title: 'Senior Frontend Developer',
    experience: 5,
    education: 'Bachelor of Computer Science',
    location: 'San Francisco, CA',
    bio: 'Experienced frontend developer with a passion for creating intuitive user interfaces.',
    skillIds: ['JavaScript', 'TypeScript', 'React', 'Next.js', 'GraphQL', 'Git']
  },
  {
    name: 'Jane Smith',
    email: '<EMAIL>',
    title: 'Full Stack Developer',
    experience: 3,
    education: 'Master of Information Technology',
    location: 'New York, NY',
    bio: 'Full stack developer with experience in both frontend and backend technologies.',
    skillIds: ['JavaScript', 'TypeScript', 'React', 'Node.js', 'MongoDB', 'REST API']
  },
  {
    name: 'Michael Johnson',
    email: '<EMAIL>',
    title: 'DevOps Engineer',
    experience: 7,
    education: 'Bachelor of Engineering',
    location: 'Seattle, WA',
    bio: 'DevOps engineer with a focus on automation and infrastructure as code.',
    skillIds: ['AWS', 'Docker', 'Kubernetes', 'CI/CD', 'Python', 'Git']
  },
  {
    name: 'Emily Davis',
    email: '<EMAIL>',
    title: 'Product Manager',
    experience: 4,
    education: 'MBA',
    location: 'Austin, TX',
    bio: 'Product manager with a technical background and a focus on user experience.',
    skillIds: ['Agile', 'Scrum', 'Project Management', 'Communication', 'Problem Solving']
  },
  {
    name: 'David Wilson',
    email: '<EMAIL>',
    title: 'Backend Developer',
    experience: 6,
    education: 'Bachelor of Software Engineering',
    location: 'Chicago, IL',
    bio: 'Backend developer with expertise in building scalable and performant APIs.',
    skillIds: ['Node.js', 'Python', 'SQL', 'NoSQL', 'REST API', 'GraphQL']
  }
];

// Sample companies data
const companiesData = [
  {
    name: 'TechStartup Inc.',
    industry: 'Technology',
    size: 50,
    location: 'San Francisco, CA',
    description: 'Innovative startup focused on AI-powered solutions for businesses.',
    website: 'https://techstartup.example.com'
  },
  {
    name: 'Enterprise Solutions',
    industry: 'Enterprise Software',
    size: 1200,
    location: 'New York, NY',
    description: 'Leading provider of enterprise software solutions for Fortune 500 companies.',
    website: 'https://enterprise-solutions.example.com'
  },
  {
    name: 'Digital Agency Co.',
    industry: 'Digital Marketing',
    size: 120,
    location: 'Austin, TX',
    description: 'Creative digital agency specializing in web development and digital marketing.',
    website: 'https://digital-agency.example.com'
  }
];

// Sample hiring authorities data (to be populated based on company size)
const hiringAuthoritiesData = [];

// Sample positions data (to be populated based on hiring authorities)
const positionsData = [];

// Function to seed the database
async function seedDatabase() {
  try {
    console.log('Seeding database with sample data...');

    // Clear existing data
    await clearCollections();

    // Insert skills
    console.log('Inserting skills...');
    const skillDocs = await Promise.all(
      skillsData.map(skill => skills.save({ ...skill, _key: skill.name }))
    );
    console.log(`${skillDocs.length} skills inserted.`);

    // Insert job seekers
    console.log('Inserting job seekers...');
    const jobSeekerDocs = await Promise.all(
      jobSeekersData.map(seeker => jobSeekers.save(seeker))
    );
    console.log(`${jobSeekerDocs.length} job seekers inserted.`);

    // Insert companies
    console.log('Inserting companies...');
    const companyDocs = await Promise.all(
      companiesData.map(company => companies.save(company))
    );
    console.log(`${companyDocs.length} companies inserted.`);

    // Generate and insert hiring authorities based on company size
    console.log('Generating hiring authorities...');
    await generateHiringAuthorities(companyDocs);

    // Connect job seekers to skills
    console.log('Connecting job seekers to skills...');
    await connectJobSeekersToSkills(jobSeekerDocs);

    // Connect positions to skills
    console.log('Connecting positions to skills...');
    await connectPositionsToSkills();

    console.log('Database seeded successfully!');
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
}

// Function to clear all collections
async function clearCollections() {
  console.log('Clearing existing data...');

  // Clear edge collections first to avoid constraint violations
  await seekerSkills.truncate();
  await positionSkills.truncate();
  await companyHiringAuthorities.truncate();
  await hiringAuthorityPositions.truncate();

  // Then clear document collections
  await jobSeekers.truncate();
  await companies.truncate();
  await hiringAuthorities.truncate();
  await positions.truncate();
  await skills.truncate();

  console.log('All collections cleared.');
}

// Function to generate hiring authorities based on company size
async function generateHiringAuthorities(companyDocs) {
  for (const company of companyDocs) {
    const companySize = company.size;
    let hiringStructure = [];

    if (companySize < 100) {
      // Small company: CEO, CTO, and maybe a Team Lead
      hiringStructure = [
        { title: 'CEO', level: 'Executive', directReports: 2 },
        { title: 'CTO', level: 'Executive', directReports: 3 },
        { title: 'Team Lead', level: 'Management', directReports: 5 }
      ];
    } else if (companySize < 500) {
      // Medium company: CEO, CTO, VP, HR Manager, Team Leads
      hiringStructure = [
        { title: 'CEO', level: 'Executive', directReports: 3 },
        { title: 'CTO', level: 'Executive', directReports: 5 },
        { title: 'VP of Engineering', level: 'Executive', directReports: 10 },
        { title: 'HR Manager', level: 'Management', directReports: 2 },
        { title: 'Engineering Manager', level: 'Management', directReports: 15 },
        { title: 'Product Manager', level: 'Management', directReports: 8 }
      ];
    } else {
      // Large company: Full hierarchy
      hiringStructure = [
        { title: 'CEO', level: 'Executive', directReports: 5 },
        { title: 'CTO', level: 'Executive', directReports: 8 },
        { title: 'VP of Engineering', level: 'Executive', directReports: 15 },
        { title: 'VP of Product', level: 'Executive', directReports: 10 },
        { title: 'HR Director', level: 'Executive', directReports: 20 },
        { title: 'HR Manager', level: 'Management', directReports: 5 },
        { title: 'Engineering Manager', level: 'Management', directReports: 25 },
        { title: 'Product Manager', level: 'Management', directReports: 15 },
        { title: 'Team Lead', level: 'Management', directReports: 10 },
        { title: 'HR Recruiter', level: 'Staff', directReports: 0 }
      ];
    }

    // Insert hiring authorities for this company
    for (const authority of hiringStructure) {
      const authorityDoc = await hiringAuthorities.save({
        ...authority,
        name: `${authority.title} at ${company.name}`,
        email: `${authority.title.toLowerCase().replace(/ /g, '.')}@${company.website.split('//')[1]}`,
        companyId: company._id
      });

      // Connect hiring authority to company
      await companyHiringAuthorities.save({
        _from: company._id,
        _to: authorityDoc._id,
        relationship: authority.level
      });

      // Generate positions for this hiring authority
      await generatePositions(authorityDoc, company);
    }
  }
}

// Function to generate positions for a hiring authority
async function generatePositions(authority, company) {
  const positionCount = Math.floor(Math.random() * 3) + 1; // 1-3 positions per authority

  const positionTitles = [
    'Frontend Developer', 'Backend Developer', 'Full Stack Developer',
    'DevOps Engineer', 'Data Scientist', 'UX Designer',
    'Product Manager', 'Project Manager', 'QA Engineer',
    'Mobile Developer', 'Systems Architect', 'Database Administrator'
  ];

  const experienceLevels = ['Entry', 'Mid', 'Senior', 'Lead', 'Principal'];

  for (let i = 0; i < positionCount; i++) {
    const title = positionTitles[Math.floor(Math.random() * positionTitles.length)];
    const level = experienceLevels[Math.floor(Math.random() * experienceLevels.length)];
    const fullTitle = `${level} ${title}`;

    // Required skills based on position
    let requiredSkills = [];
    if (title.includes('Frontend')) {
      requiredSkills = ['JavaScript', 'React', 'HTML', 'CSS'];
    } else if (title.includes('Backend')) {
      requiredSkills = ['Node.js', 'SQL', 'REST API'];
    } else if (title.includes('Full Stack')) {
      requiredSkills = ['JavaScript', 'React', 'Node.js', 'SQL'];
    } else if (title.includes('DevOps')) {
      requiredSkills = ['AWS', 'Docker', 'CI/CD'];
    } else if (title.includes('Data')) {
      requiredSkills = ['Python', 'SQL', 'NoSQL'];
    } else if (title.includes('UX')) {
      requiredSkills = ['Communication', 'Problem Solving'];
    } else if (title.includes('Product') || title.includes('Project')) {
      requiredSkills = ['Agile', 'Communication', 'Project Management'];
    } else {
      requiredSkills = ['Problem Solving', 'Communication'];
    }

    // Add some random skills
    const allSkillNames = skillsData.map(s => s.name);
    while (requiredSkills.length < 5) {
      const randomSkill = allSkillNames[Math.floor(Math.random() * allSkillNames.length)];
      if (!requiredSkills.includes(randomSkill)) {
        requiredSkills.push(randomSkill);
      }
    }

    const position = {
      title: fullTitle,
      description: `${fullTitle} position at ${company.name}`,
      location: company.location,
      remote: Math.random() > 0.5,
      salary: {
        min: 50000 + (level === 'Entry' ? 0 : level === 'Mid' ? 30000 : level === 'Senior' ? 60000 : 90000),
        max: 80000 + (level === 'Entry' ? 0 : level === 'Mid' ? 40000 : level === 'Senior' ? 80000 : 120000)
      },
      requiredExperience: level === 'Entry' ? 0 : level === 'Mid' ? 2 : level === 'Senior' ? 5 : 8,
      hiringAuthorityId: authority._id,
      skillIds: requiredSkills
    };

    const positionDoc = await positions.save(position);
    positionsData.push(positionDoc);

    // Connect position to hiring authority
    await hiringAuthorityPositions.save({
      _from: authority._id,
      _to: positionDoc._id,
      relationship: 'responsible_for'
    });
  }
}

// Function to connect job seekers to skills
async function connectJobSeekersToSkills(jobSeekerDocs) {
  for (const seeker of jobSeekerDocs) {
    for (const skillName of seeker.skillIds) {
      const skillDoc = await skills.document(skillName);

      await seekerSkills.save({
        _from: seeker._id,
        _to: skillDoc._id,
        proficiency: Math.floor(Math.random() * 5) + 1, // 1-5 proficiency level
        yearsOfExperience: Math.floor(Math.random() * seeker.experience) + 1
      });
    }
  }
}

// Function to connect positions to skills
async function connectPositionsToSkills() {
  for (const position of positionsData) {
    for (const skillName of position.skillIds) {
      const skillDoc = await skills.document(skillName);

      await positionSkills.save({
        _from: position._id,
        _to: skillDoc._id,
        importance: Math.floor(Math.random() * 5) + 1, // 1-5 importance level
        required: Math.random() > 0.3 // 70% chance of being required
      });
    }
  }
}

// Run the seed function
seedDatabase();