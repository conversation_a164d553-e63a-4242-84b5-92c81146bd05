#!/usr/bin/env node

const { Database } = require('arangojs');

async function seedSampleData() {
  console.log('🌱 Seeding Candid Connections with sample data...');
  
  // Use the correct credentials
  const config = {
    url: 'https://f2c8a97499a8.arangodb.cloud:8529',
    auth: { 
      username: 'root', 
      password: 'XMx2qSsHU8RWMX9VSxAx' 
    },
    databaseName: 'candid_connections'
  };
  
  try {
    const db = new Database(config);
    
    // Test connection
    console.log('Testing connection...');
    await db.version();
    console.log('✅ Connected to ArangoDB');
    
    // Sample skills
    console.log('\n📚 Adding skills...');
    const skillsCollection = db.collection('skills');
    const skills = [
      { _key: 'javascript', name: 'JavaScript', category: 'Programming' },
      { _key: 'python', name: 'Python', category: 'Programming' },
      { _key: 'react', name: 'React', category: 'Frontend' },
      { _key: 'nodejs', name: 'Node.js', category: 'Backend' },
      { _key: 'aws', name: 'A<PERSON>', category: 'Cloud' },
      { _key: 'docker', name: 'Docker', category: 'DevOps' },
      { _key: 'sql', name: 'SQL', category: 'Database' },
      { _key: 'mongodb', name: 'MongoDB', category: 'Database' },
      { _key: 'graphql', name: 'GraphQL', category: 'API' },
      { _key: 'typescript', name: 'TypeScript', category: 'Programming' }
    ];
    
    for (const skill of skills) {
      try {
        await skillsCollection.save(skill);
        console.log(`  ✅ Added skill: ${skill.name}`);
      } catch (error) {
        if (error.code === 1210) { // Document already exists
          console.log(`  ⚠️  Skill already exists: ${skill.name}`);
        } else {
          throw error;
        }
      }
    }
    
    // Sample companies
    console.log('\n🏢 Adding companies...');
    const companiesCollection = db.collection('companies');
    const companies = [
      { 
        _key: 'techcorp', 
        name: 'TechCorp Solutions', 
        industry: 'Technology',
        size: 'Large',
        location: 'San Francisco, CA'
      },
      { 
        _key: 'innovate', 
        name: 'Innovate Labs', 
        industry: 'Software',
        size: 'Medium',
        location: 'Austin, TX'
      },
      { 
        _key: 'dataflow', 
        name: 'DataFlow Analytics', 
        industry: 'Data Science',
        size: 'Small',
        location: 'Seattle, WA'
      },
      { 
        _key: 'cloudnine', 
        name: 'Cloud Nine Systems', 
        industry: 'Cloud Computing',
        size: 'Medium',
        location: 'Denver, CO'
      },
      { 
        _key: 'webcraft', 
        name: 'WebCraft Studio', 
        industry: 'Web Development',
        size: 'Small',
        location: 'Portland, OR'
      }
    ];
    
    for (const company of companies) {
      try {
        await companiesCollection.save(company);
        console.log(`  ✅ Added company: ${company.name}`);
      } catch (error) {
        if (error.code === 1210) {
          console.log(`  ⚠️  Company already exists: ${company.name}`);
        } else {
          throw error;
        }
      }
    }
    
    // Sample job seekers
    console.log('\n👤 Adding job seekers...');
    const jobSeekersCollection = db.collection('jobSeekers');
    const jobSeekers = [
      { 
        _key: 'alice', 
        name: 'Alice Johnson', 
        email: '<EMAIL>',
        experience: 'Senior',
        location: 'San Francisco, CA',
        title: 'Full Stack Developer'
      },
      { 
        _key: 'bob', 
        name: 'Bob Smith', 
        email: '<EMAIL>',
        experience: 'Mid-level',
        location: 'Austin, TX',
        title: 'Backend Developer'
      },
      { 
        _key: 'carol', 
        name: 'Carol Davis', 
        email: '<EMAIL>',
        experience: 'Junior',
        location: 'Seattle, WA',
        title: 'Frontend Developer'
      },
      { 
        _key: 'david', 
        name: 'David Wilson', 
        email: '<EMAIL>',
        experience: 'Senior',
        location: 'Denver, CO',
        title: 'DevOps Engineer'
      },
      { 
        _key: 'eve', 
        name: 'Eve Brown', 
        email: '<EMAIL>',
        experience: 'Mid-level',
        location: 'Portland, OR',
        title: 'Data Scientist'
      }
    ];
    
    for (const seeker of jobSeekers) {
      try {
        await jobSeekersCollection.save(seeker);
        console.log(`  ✅ Added job seeker: ${seeker.name}`);
      } catch (error) {
        if (error.code === 1210) {
          console.log(`  ⚠️  Job seeker already exists: ${seeker.name}`);
        } else {
          throw error;
        }
      }
    }
    
    // Sample positions
    console.log('\n💼 Adding positions...');
    const positionsCollection = db.collection('positions');
    const positions = [
      { 
        _key: 'pos1', 
        title: 'Senior React Developer', 
        company: 'techcorp',
        level: 'Senior',
        location: 'San Francisco, CA',
        salary: '$120,000 - $150,000'
      },
      { 
        _key: 'pos2', 
        title: 'Backend Engineer', 
        company: 'innovate',
        level: 'Mid-level',
        location: 'Austin, TX',
        salary: '$90,000 - $110,000'
      },
      { 
        _key: 'pos3', 
        title: 'Data Analyst', 
        company: 'dataflow',
        level: 'Junior',
        location: 'Seattle, WA',
        salary: '$70,000 - $85,000'
      },
      { 
        _key: 'pos4', 
        title: 'Cloud Architect', 
        company: 'cloudnine',
        level: 'Senior',
        location: 'Denver, CO',
        salary: '$130,000 - $160,000'
      },
      { 
        _key: 'pos5', 
        title: 'Frontend Developer', 
        company: 'webcraft',
        level: 'Mid-level',
        location: 'Portland, OR',
        salary: '$80,000 - $100,000'
      }
    ];
    
    for (const position of positions) {
      try {
        await positionsCollection.save(position);
        console.log(`  ✅ Added position: ${position.title}`);
      } catch (error) {
        if (error.code === 1210) {
          console.log(`  ⚠️  Position already exists: ${position.title}`);
        } else {
          throw error;
        }
      }
    }
    
    // Sample skill connections
    console.log('\n🔗 Adding skill connections...');
    const seekerSkillsCollection = db.collection('seekerSkills');
    const skillConnections = [
      { _from: 'jobSeekers/alice', _to: 'skills/javascript', proficiency: 'Expert' },
      { _from: 'jobSeekers/alice', _to: 'skills/react', proficiency: 'Expert' },
      { _from: 'jobSeekers/alice', _to: 'skills/nodejs', proficiency: 'Advanced' },
      { _from: 'jobSeekers/bob', _to: 'skills/python', proficiency: 'Expert' },
      { _from: 'jobSeekers/bob', _to: 'skills/sql', proficiency: 'Advanced' },
      { _from: 'jobSeekers/carol', _to: 'skills/javascript', proficiency: 'Intermediate' },
      { _from: 'jobSeekers/carol', _to: 'skills/react', proficiency: 'Intermediate' },
      { _from: 'jobSeekers/david', _to: 'skills/aws', proficiency: 'Expert' },
      { _from: 'jobSeekers/david', _to: 'skills/docker', proficiency: 'Expert' },
      { _from: 'jobSeekers/eve', _to: 'skills/python', proficiency: 'Advanced' },
      { _from: 'jobSeekers/eve', _to: 'skills/mongodb', proficiency: 'Intermediate' }
    ];
    
    for (const connection of skillConnections) {
      try {
        await seekerSkillsCollection.save(connection);
        console.log(`  ✅ Added skill connection`);
      } catch (error) {
        if (error.code === 1210) {
          console.log(`  ⚠️  Skill connection already exists`);
        } else {
          throw error;
        }
      }
    }
    
    console.log('\n🎉 Sample data seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`  • ${skills.length} skills`);
    console.log(`  • ${companies.length} companies`);
    console.log(`  • ${jobSeekers.length} job seekers`);
    console.log(`  • ${positions.length} positions`);
    console.log(`  • ${skillConnections.length} skill connections`);
    
    console.log('\n🚀 Ready to view your dashboard at http://localhost:3000');
    
  } catch (error) {
    console.error('❌ Error seeding data:', error.message);
    process.exit(1);
  }
}

seedSampleData();
