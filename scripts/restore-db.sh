#!/bin/bash

# Check if backup file is provided
if [ -z "$1" ]; then
  echo "Usage: $0 <backup-file.tar.gz>"
  exit 1
fi

BACKUP_FILE="$1"
TEMP_DIR="./temp_restore"
ARANGO_HOST="${ARANGO_HOST:-localhost}"
ARANGO_PORT="${ARANGO_PORT:-8529}"
ARANGO_USER="${ARANGO_USERNAME:-root}"
ARANGO_PASSWORD="${ARANGO_PASSWORD:-rootpassword}"
ARANGO_DB="${ARANGO_DB_NAME:-candid_connections}"

# Check if backup file exists
if [ ! -f "$BACKUP_FILE" ]; then
  echo "Backup file $BACKUP_FILE does not exist."
  exit 1
fi

# Create temporary directory
mkdir -p "$TEMP_DIR"

# Extract backup
echo "Extracting backup file..."
tar -xzf "$BACKUP_FILE" -C "$TEMP_DIR"

# Check if extraction was successful
if [ $? -ne 0 ]; then
  echo "Failed to extract backup file."
  rm -rf "$TEMP_DIR"
  exit 1
fi

# Find the extracted directory
EXTRACTED_DIR=$(find "$TEMP_DIR" -type d -name "$ARANGO_DB-*" | head -n 1)

if [ -z "$EXTRACTED_DIR" ]; then
  echo "Could not find extracted backup directory."
  rm -rf "$TEMP_DIR"
  exit 1
fi

# Restore database
echo "Restoring database $ARANGO_DB..."
arangorestore \
  --server.endpoint tcp://$ARANGO_HOST:$ARANGO_PORT \
  --server.username $ARANGO_USER \
  --server.password $ARANGO_PASSWORD \
  --server.database $ARANGO_DB \
  --input-directory "$EXTRACTED_DIR" \
  --create-database true

# Check if restore was successful
if [ $? -eq 0 ]; then
  echo "Database restored successfully."
else
  echo "Failed to restore database."
fi

# Clean up
echo "Cleaning up temporary files..."
rm -rf "$TEMP_DIR"
