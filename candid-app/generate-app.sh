#!/bin/bash

# ============================================================================
# Candid Connections - Job Seeker to Hiring Authority Matching Platform
# ============================================================================
# This script generates a Next.js 15 application with ArangoDB as the graph database
# backend for matching job seekers with hiring authorities based on skills and requirements.
#
# Features:
# - Next.js 15 application with App Router
# - ArangoDB for graph database (free for local development)
# - React Force Graph for visualization
# - CRUD operations for all entities
# - Mock data generation
# - AWS deployment configuration
# - Comprehensive documentation
# ============================================================================

set -e

# Text formatting
BOLD="\033[1m"
GREEN="\033[0;32m"
BLUE="\033[0;34m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
NC="\033[0m" # No Color

# Function to print section headers
print_section() {
  echo -e "\n${BOLD}${BLUE}==== $1 ====${NC}\n"
}

# Function to print success messages
print_success() {
  echo -e "${GREEN}✓ $1${NC}"
}

# Function to print info messages
print_info() {
  echo -e "${YELLOW}ℹ $1${NC}"
}

# Function to print error messages
print_error() {
  echo -e "${RED}✗ $1${NC}"
}

# Function to check if a command exists
command_exists() {
  command -v "$1" >/dev/null 2>&1
}

# Check for required dependencies
check_dependencies() {
  print_section "Checking Dependencies"

  local missing_deps=0

  if ! command_exists node; then
    print_error "Node.js is not installed. Please install Node.js 18 or higher."
    missing_deps=1
  else
    local node_version=$(node -v | cut -d 'v' -f 2)
    if [[ $(echo "$node_version 18.0.0" | awk '{print ($1 < $2)}') -eq 1 ]]; then
      print_error "Node.js version $node_version is too old. Please install Node.js 18 or higher."
      missing_deps=1
    else
      print_success "Node.js $(node -v) is installed."
    fi
  fi

  if ! command_exists npm; then
    print_error "npm is not installed. Please install npm."
    missing_deps=1
  else
    print_success "npm $(npm -v) is installed."
  fi

  if ! command_exists docker; then
    print_info "Docker is not installed. It's recommended for running ArangoDB locally."
    print_info "You can install Docker from https://docs.docker.com/get-docker/"
  else
    print_success "Docker is installed."
  fi

  if [[ $missing_deps -eq 1 ]]; then
    print_error "Please install the missing dependencies and run this script again."
    exit 1
  fi
}

# Create Next.js application
create_nextjs_app() {
  print_section "Creating Next.js Application"

  # Create Next.js app with TypeScript
  print_info "Initializing Next.js 15 application with TypeScript..."
  npx create-next-app@latest . --ts --tailwind --eslint --app --src-dir --import-alias "@/*" --use-npm

  print_success "Next.js application created successfully."
}

# Install additional dependencies
install_dependencies() {
  print_section "Installing Additional Dependencies"

  print_info "Installing ArangoDB client and other dependencies..."
  npm install arangojs react-force-graph three @react-three/fiber @react-three/drei d3 swr axios zod react-hook-form @hookform/resolvers uuid
  npm install -D @types/uuid @types/three

  print_success "Dependencies installed successfully."
}

# Create Docker Compose file for ArangoDB
create_docker_compose() {
  print_section "Creating Docker Compose Configuration for ArangoDB"

  mkdir -p docker

  cat > docker/docker-compose.yml << 'EOL'
version: '3.8'
services:
  arangodb:
    image: arangodb:latest
    environment:
      - ARANGO_ROOT_PASSWORD=rootpassword
    ports:
      - "8529:8529"
    volumes:
      - arangodb_data:/var/lib/arangodb3
      - arangodb_apps:/var/lib/arangodb3-apps
    restart: unless-stopped

volumes:
  arangodb_data:
  arangodb_apps:
EOL

  print_success "Docker Compose configuration created."
}

# Create database setup scripts
create_db_setup_scripts() {
  print_section "Creating Database Setup Scripts"

  mkdir -p scripts

  # Create database initialization script
  cat > scripts/init-db.js << 'EOL'
const { Database } = require('arangojs');

async function initializeDatabase() {
  console.log('Initializing ArangoDB database...');

  const db = new Database({
    url: 'http://localhost:8529',
    auth: { username: 'root', password: 'rootpassword' },
    databaseName: '_system'
  });

  // Create database if it doesn't exist
  const dbName = 'candid_connections';
  const dbExists = await db.listDatabases().then(dbs => dbs.includes(dbName));

  if (!dbExists) {
    console.log(`Creating database: ${dbName}`);
    await db.createDatabase(dbName);
    console.log(`Database ${dbName} created successfully.`);
  } else {
    console.log(`Database ${dbName} already exists.`);
  }

  // Switch to the application database
  const appDb = db.database(dbName);

  // Create collections
  const collections = [
    { name: 'jobSeekers', type: 'document' },
    { name: 'companies', type: 'document' },
    { name: 'hiringAuthorities', type: 'document' },
    { name: 'positions', type: 'document' },
    { name: 'skills', type: 'document' },
    { name: 'seekerSkills', type: 'edge' },
    { name: 'positionSkills', type: 'edge' },
    { name: 'companyHiringAuthorities', type: 'edge' },
    { name: 'hiringAuthorityPositions', type: 'edge' }
  ];

  for (const collection of collections) {
    const collectionExists = await appDb.collections()
      .then(cols => cols.some(col => col.name === collection.name));

    if (!collectionExists) {
      console.log(`Creating collection: ${collection.name}`);
      if (collection.type === 'edge') {
        await appDb.createEdgeCollection(collection.name);
      } else {
        await appDb.createCollection(collection.name);
      }
      console.log(`Collection ${collection.name} created successfully.`);
    } else {
      console.log(`Collection ${collection.name} already exists.`);
    }
  }

  console.log('Database initialization completed successfully.');
}

initializeDatabase().catch(err => {
  console.error('Error initializing database:', err);
  process.exit(1);
});
EOL

  # Create seed data script
  cat > scripts/seed-data.js << 'EOL'
const { Database } = require('arangojs');
const { v4: uuidv4 } = require('uuid');

// Connect to the database
const db = new Database({
  url: 'http://localhost:8529',
  auth: { username: 'root', password: 'rootpassword' },
  databaseName: 'candid_connections'
});

// Collection references
const jobSeekers = db.collection('jobSeekers');
const companies = db.collection('companies');
const hiringAuthorities = db.collection('hiringAuthorities');
const positions = db.collection('positions');
const skills = db.collection('skills');
const seekerSkills = db.collection('seekerSkills');
const positionSkills = db.collection('positionSkills');
const companyHiringAuthorities = db.collection('companyHiringAuthorities');
const hiringAuthorityPositions = db.collection('hiringAuthorityPositions');

// Sample skills data
const skillsData = [
  { name: 'JavaScript', category: 'Programming Language', level: 'Advanced' },
  { name: 'TypeScript', category: 'Programming Language', level: 'Advanced' },
  { name: 'React', category: 'Frontend Framework', level: 'Advanced' },
  { name: 'Next.js', category: 'Frontend Framework', level: 'Advanced' },
  { name: 'Node.js', category: 'Backend Framework', level: 'Advanced' },
  { name: 'GraphQL', category: 'API', level: 'Intermediate' },
  { name: 'REST API', category: 'API', level: 'Advanced' },
  { name: 'SQL', category: 'Database', level: 'Intermediate' },
  { name: 'NoSQL', category: 'Database', level: 'Intermediate' },
  { name: 'MongoDB', category: 'Database', level: 'Intermediate' },
  { name: 'AWS', category: 'Cloud', level: 'Intermediate' },
  { name: 'Docker', category: 'DevOps', level: 'Intermediate' },
  { name: 'Kubernetes', category: 'DevOps', level: 'Beginner' },
  { name: 'CI/CD', category: 'DevOps', level: 'Intermediate' },
  { name: 'Git', category: 'Version Control', level: 'Advanced' },
  { name: 'Agile', category: 'Methodology', level: 'Advanced' },
  { name: 'Scrum', category: 'Methodology', level: 'Advanced' },
  { name: 'Project Management', category: 'Soft Skill', level: 'Advanced' },
  { name: 'Team Leadership', category: 'Soft Skill', level: 'Advanced' },
  { name: 'Communication', category: 'Soft Skill', level: 'Advanced' },
  { name: 'Problem Solving', category: 'Soft Skill', level: 'Advanced' },
  { name: 'Critical Thinking', category: 'Soft Skill', level: 'Advanced' },
  { name: 'Python', category: 'Programming Language', level: 'Intermediate' },
  { name: 'Java', category: 'Programming Language', level: 'Intermediate' },
  { name: 'C#', category: 'Programming Language', level: 'Intermediate' },
  { name: 'PHP', category: 'Programming Language', level: 'Intermediate' },
  { name: 'Ruby', category: 'Programming Language', level: 'Beginner' },
  { name: 'Go', category: 'Programming Language', level: 'Beginner' },
  { name: 'Rust', category: 'Programming Language', level: 'Beginner' },
  { name: 'Swift', category: 'Programming Language', level: 'Beginner' }
];

// Sample job seekers data
const jobSeekersData = [
  {
    name: 'John Doe',
    email: '<EMAIL>',
    title: 'Senior Frontend Developer',
    experience: 5,
    education: 'Bachelor of Computer Science',
    location: 'San Francisco, CA',
    bio: 'Experienced frontend developer with a passion for creating intuitive user interfaces.',
    skillIds: ['JavaScript', 'TypeScript', 'React', 'Next.js', 'GraphQL', 'Git']
  },
  {
    name: 'Jane Smith',
    email: '<EMAIL>',
    title: 'Full Stack Developer',
    experience: 3,
    education: 'Master of Information Technology',
    location: 'New York, NY',
    bio: 'Full stack developer with experience in both frontend and backend technologies.',
    skillIds: ['JavaScript', 'TypeScript', 'React', 'Node.js', 'MongoDB', 'REST API']
  },
  {
    name: 'Michael Johnson',
    email: '<EMAIL>',
    title: 'DevOps Engineer',
    experience: 7,
    education: 'Bachelor of Engineering',
    location: 'Seattle, WA',
    bio: 'DevOps engineer with a focus on automation and infrastructure as code.',
    skillIds: ['AWS', 'Docker', 'Kubernetes', 'CI/CD', 'Python', 'Git']
  },
  {
    name: 'Emily Davis',
    email: '<EMAIL>',
    title: 'Product Manager',
    experience: 4,
    education: 'MBA',
    location: 'Austin, TX',
    bio: 'Product manager with a technical background and a focus on user experience.',
    skillIds: ['Agile', 'Scrum', 'Project Management', 'Communication', 'Problem Solving']
  },
  {
    name: 'David Wilson',
    email: '<EMAIL>',
    title: 'Backend Developer',
    experience: 6,
    education: 'Bachelor of Software Engineering',
    location: 'Chicago, IL',
    bio: 'Backend developer with expertise in building scalable and performant APIs.',
    skillIds: ['Node.js', 'Python', 'SQL', 'NoSQL', 'REST API', 'GraphQL']
  }
];

// Sample companies data
const companiesData = [
  {
    name: 'TechStartup Inc.',
    industry: 'Technology',
    size: 50,
    location: 'San Francisco, CA',
    description: 'Innovative startup focused on AI-powered solutions for businesses.',
    website: 'https://techstartup.example.com'
  },
  {
    name: 'Enterprise Solutions',
    industry: 'Enterprise Software',
    size: 1200,
    location: 'New York, NY',
    description: 'Leading provider of enterprise software solutions for Fortune 500 companies.',
    website: 'https://enterprise-solutions.example.com'
  },
  {
    name: 'Digital Agency Co.',
    industry: 'Digital Marketing',
    size: 120,
    location: 'Austin, TX',
    description: 'Creative digital agency specializing in web development and digital marketing.',
    website: 'https://digital-agency.example.com'
  }
];

// Sample hiring authorities data (to be populated based on company size)
const hiringAuthoritiesData = [];

// Sample positions data (to be populated based on hiring authorities)
const positionsData = [];

// Function to seed the database
async function seedDatabase() {
  try {
    console.log('Seeding database with sample data...');

    // Clear existing data
    await clearCollections();

    // Insert skills
    console.log('Inserting skills...');
    const skillDocs = await Promise.all(
      skillsData.map(skill => skills.save({ ...skill, _key: skill.name }))
    );
    console.log(`${skillDocs.length} skills inserted.`);

    // Insert job seekers
    console.log('Inserting job seekers...');
    const jobSeekerDocs = await Promise.all(
      jobSeekersData.map(seeker => jobSeekers.save(seeker))
    );
    console.log(`${jobSeekerDocs.length} job seekers inserted.`);

    // Insert companies
    console.log('Inserting companies...');
    const companyDocs = await Promise.all(
      companiesData.map(company => companies.save(company))
    );
    console.log(`${companyDocs.length} companies inserted.`);

    // Generate and insert hiring authorities based on company size
    console.log('Generating hiring authorities...');
    await generateHiringAuthorities(companyDocs);

    // Connect job seekers to skills
    console.log('Connecting job seekers to skills...');
    await connectJobSeekersToSkills(jobSeekerDocs);

    // Connect positions to skills
    console.log('Connecting positions to skills...');
    await connectPositionsToSkills();

    console.log('Database seeded successfully!');
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
}

// Function to clear all collections
async function clearCollections() {
  console.log('Clearing existing data...');

  // Clear edge collections first to avoid constraint violations
  await seekerSkills.truncate();
  await positionSkills.truncate();
  await companyHiringAuthorities.truncate();
  await hiringAuthorityPositions.truncate();

  // Then clear document collections
  await jobSeekers.truncate();
  await companies.truncate();
  await hiringAuthorities.truncate();
  await positions.truncate();
  await skills.truncate();

  console.log('All collections cleared.');
}

// Function to generate hiring authorities based on company size
async function generateHiringAuthorities(companyDocs) {
  for (const company of companyDocs) {
    const companySize = company.size;
    let hiringStructure = [];

    if (companySize < 100) {
      // Small company: CEO, CTO, and maybe a Team Lead
      hiringStructure = [
        { title: 'CEO', level: 'Executive', directReports: 2 },
        { title: 'CTO', level: 'Executive', directReports: 3 },
        { title: 'Team Lead', level: 'Management', directReports: 5 }
      ];
    } else if (companySize < 500) {
      // Medium company: CEO, CTO, VP, HR Manager, Team Leads
      hiringStructure = [
        { title: 'CEO', level: 'Executive', directReports: 3 },
        { title: 'CTO', level: 'Executive', directReports: 5 },
        { title: 'VP of Engineering', level: 'Executive', directReports: 10 },
        { title: 'HR Manager', level: 'Management', directReports: 2 },
        { title: 'Engineering Manager', level: 'Management', directReports: 15 },
        { title: 'Product Manager', level: 'Management', directReports: 8 }
      ];
    } else {
      // Large company: Full hierarchy
      hiringStructure = [
        { title: 'CEO', level: 'Executive', directReports: 5 },
        { title: 'CTO', level: 'Executive', directReports: 8 },
        { title: 'VP of Engineering', level: 'Executive', directReports: 15 },
        { title: 'VP of Product', level: 'Executive', directReports: 10 },
        { title: 'HR Director', level: 'Executive', directReports: 20 },
        { title: 'HR Manager', level: 'Management', directReports: 5 },
        { title: 'Engineering Manager', level: 'Management', directReports: 25 },
        { title: 'Product Manager', level: 'Management', directReports: 15 },
        { title: 'Team Lead', level: 'Management', directReports: 10 },
        { title: 'HR Recruiter', level: 'Staff', directReports: 0 }
      ];
    }

    // Insert hiring authorities for this company
    for (const authority of hiringStructure) {
      const authorityDoc = await hiringAuthorities.save({
        ...authority,
        name: `${authority.title} at ${company.name}`,
        email: `${authority.title.toLowerCase().replace(/ /g, '.')}@${company.website.split('//')[1]}`,
        companyId: company._id
      });

      // Connect hiring authority to company
      await companyHiringAuthorities.save({
        _from: company._id,
        _to: authorityDoc._id,
        relationship: authority.level
      });

      // Generate positions for this hiring authority
      await generatePositions(authorityDoc, company);
    }
  }
}

// Function to generate positions for a hiring authority
async function generatePositions(authority, company) {
  const positionCount = Math.floor(Math.random() * 3) + 1; // 1-3 positions per authority

  const positionTitles = [
    'Frontend Developer', 'Backend Developer', 'Full Stack Developer',
    'DevOps Engineer', 'Data Scientist', 'UX Designer',
    'Product Manager', 'Project Manager', 'QA Engineer',
    'Mobile Developer', 'Systems Architect', 'Database Administrator'
  ];

  const experienceLevels = ['Entry', 'Mid', 'Senior', 'Lead', 'Principal'];

  for (let i = 0; i < positionCount; i++) {
    const title = positionTitles[Math.floor(Math.random() * positionTitles.length)];
    const level = experienceLevels[Math.floor(Math.random() * experienceLevels.length)];
    const fullTitle = `${level} ${title}`;

    // Required skills based on position
    let requiredSkills = [];
    if (title.includes('Frontend')) {
      requiredSkills = ['JavaScript', 'React', 'HTML', 'CSS'];
    } else if (title.includes('Backend')) {
      requiredSkills = ['Node.js', 'SQL', 'REST API'];
    } else if (title.includes('Full Stack')) {
      requiredSkills = ['JavaScript', 'React', 'Node.js', 'SQL'];
    } else if (title.includes('DevOps')) {
      requiredSkills = ['AWS', 'Docker', 'CI/CD'];
    } else if (title.includes('Data')) {
      requiredSkills = ['Python', 'SQL', 'NoSQL'];
    } else if (title.includes('UX')) {
      requiredSkills = ['Communication', 'Problem Solving'];
    } else if (title.includes('Product') || title.includes('Project')) {
      requiredSkills = ['Agile', 'Communication', 'Project Management'];
    } else {
      requiredSkills = ['Problem Solving', 'Communication'];
    }

    // Add some random skills
    const allSkillNames = skillsData.map(s => s.name);
    while (requiredSkills.length < 5) {
      const randomSkill = allSkillNames[Math.floor(Math.random() * allSkillNames.length)];
      if (!requiredSkills.includes(randomSkill)) {
        requiredSkills.push(randomSkill);
      }
    }

    const position = {
      title: fullTitle,
      description: `${fullTitle} position at ${company.name}`,
      location: company.location,
      remote: Math.random() > 0.5,
      salary: {
        min: 50000 + (level === 'Entry' ? 0 : level === 'Mid' ? 30000 : level === 'Senior' ? 60000 : 90000),
        max: 80000 + (level === 'Entry' ? 0 : level === 'Mid' ? 40000 : level === 'Senior' ? 80000 : 120000)
      },
      requiredExperience: level === 'Entry' ? 0 : level === 'Mid' ? 2 : level === 'Senior' ? 5 : 8,
      hiringAuthorityId: authority._id,
      skillIds: requiredSkills
    };

    const positionDoc = await positions.save(position);
    positionsData.push(positionDoc);

    // Connect position to hiring authority
    await hiringAuthorityPositions.save({
      _from: authority._id,
      _to: positionDoc._id,
      relationship: 'responsible_for'
    });
  }
}

// Function to connect job seekers to skills
async function connectJobSeekersToSkills(jobSeekerDocs) {
  for (const seeker of jobSeekerDocs) {
    for (const skillName of seeker.skillIds) {
      const skillDoc = await skills.document(skillName);

      await seekerSkills.save({
        _from: seeker._id,
        _to: skillDoc._id,
        proficiency: Math.floor(Math.random() * 5) + 1, // 1-5 proficiency level
        yearsOfExperience: Math.floor(Math.random() * seeker.experience) + 1
      });
    }
  }
}

// Function to connect positions to skills
async function connectPositionsToSkills() {
  for (const position of positionsData) {
    for (const skillName of position.skillIds) {
      const skillDoc = await skills.document(skillName);

      await positionSkills.save({
        _from: position._id,
        _to: skillDoc._id,
        importance: Math.floor(Math.random() * 5) + 1, // 1-5 importance level
        required: Math.random() > 0.3 // 70% chance of being required
      });
    }
  }
}

// Run the seed function
seedDatabase();
EOL

  print_success "Database setup and seed scripts created."
}

# Create Next.js API routes
create_api_routes() {
  print_section "Creating Next.js API Routes"

  # Create API directory structure
  mkdir -p src/app/api/job-seekers
  mkdir -p src/app/api/companies
  mkdir -p src/app/api/hiring-authorities
  mkdir -p src/app/api/positions
  mkdir -p src/app/api/skills
  mkdir -p src/app/api/matches

  # Create database connection utility
  mkdir -p src/lib/db

  cat > src/lib/db/connection.ts << 'EOL'
import { Database } from 'arangojs';

// Create a singleton instance of the database connection
let dbInstance: Database | null = null;

export function getDatabase(): Database {
  if (!dbInstance) {
    dbInstance = new Database({
      url: process.env.ARANGO_URL || 'http://localhost:8529',
      auth: {
        username: process.env.ARANGO_USERNAME || 'root',
        password: process.env.ARANGO_PASSWORD || 'rootpassword',
      },
      databaseName: process.env.ARANGO_DB_NAME || 'candid_connections',
    });
  }

  return dbInstance;
}

// Get collection references
export function getCollections() {
  const db = getDatabase();

  return {
    jobSeekers: db.collection('jobSeekers'),
    companies: db.collection('companies'),
    hiringAuthorities: db.collection('hiringAuthorities'),
    positions: db.collection('positions'),
    skills: db.collection('skills'),
    seekerSkills: db.collection('seekerSkills'),
    positionSkills: db.collection('positionSkills'),
    companyHiringAuthorities: db.collection('companyHiringAuthorities'),
    hiringAuthorityPositions: db.collection('hiringAuthorityPositions'),
  };
}
EOL

  # Create API route for job seekers
  cat > src/app/api/job-seekers/route.ts << 'EOL'
import { NextRequest, NextResponse } from 'next/server';
import { getCollections } from '@/lib/db/connection';

export async function GET(request: NextRequest) {
  try {
    const { jobSeekers } = getCollections();
    const cursor = await jobSeekers.all();
    const result = await cursor.all();

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching job seekers:', error);
    return NextResponse.json(
      { error: 'Failed to fetch job seekers' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { jobSeekers } = getCollections();
    const data = await request.json();

    const result = await jobSeekers.save(data);

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Error creating job seeker:', error);
    return NextResponse.json(
      { error: 'Failed to create job seeker' },
      { status: 500 }
    );
  }
}
EOL

  # Create API route for job seeker by ID
  cat > src/app/api/job-seekers/[id]/route.ts << 'EOL'
import { NextRequest, NextResponse } from 'next/server';
import { getCollections, getDatabase } from '@/lib/db/connection';
import { aql } from 'arangojs';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { jobSeekers } = getCollections();
    const id = params.id;

    const jobSeeker = await jobSeekers.document(id);

    // Get skills for this job seeker
    const db = getDatabase();
    const query = aql`
      FOR seeker IN jobSeekers
        FILTER seeker._id == ${id}
        LET skills = (
          FOR skill, edge IN OUTBOUND seeker._id seekerSkills
            RETURN {
              _id: skill._id,
              name: skill.name,
              category: skill.category,
              level: skill.level,
              proficiency: edge.proficiency,
              yearsOfExperience: edge.yearsOfExperience
            }
        )
        RETURN MERGE(seeker, { skills: skills })
    `;

    const cursor = await db.query(query);
    const result = await cursor.all();

    if (result.length === 0) {
      return NextResponse.json(
        { error: 'Job seeker not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(result[0]);
  } catch (error) {
    console.error('Error fetching job seeker:', error);
    return NextResponse.json(
      { error: 'Failed to fetch job seeker' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { jobSeekers } = getCollections();
    const id = params.id;
    const data = await request.json();

    const result = await jobSeekers.update(id, data);

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error updating job seeker:', error);
    return NextResponse.json(
      { error: 'Failed to update job seeker' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { jobSeekers } = getCollections();
    const id = params.id;

    await jobSeekers.remove(id);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting job seeker:', error);
    return NextResponse.json(
      { error: 'Failed to delete job seeker' },
      { status: 500 }
    );
  }
}
EOL

  # Create API route for matches
  cat > src/app/api/matches/route.ts << 'EOL'
import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '@/lib/db/connection';
import { aql } from 'arangojs';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const jobSeekerId = searchParams.get('jobSeekerId');
    const positionId = searchParams.get('positionId');
    const companyId = searchParams.get('companyId');

    const db = getDatabase();
    let query;

    if (jobSeekerId) {
      // Find matching positions for a job seeker
      query = aql`
        LET seeker = DOCUMENT(${jobSeekerId})
        LET seekerSkills = (
          FOR skill, edge IN OUTBOUND seeker._id seekerSkills
            RETURN {
              skillId: skill._id,
              name: skill.name,
              proficiency: edge.proficiency,
              yearsOfExperience: edge.yearsOfExperience
            }
        )

        FOR position IN positions
          LET positionSkills = (
            FOR skill, edge IN OUTBOUND position._id positionSkills
              RETURN {
                skillId: skill._id,
                name: skill.name,
                importance: edge.importance,
                required: edge.required
              }
          )

          LET matchingSkills = (
            FOR seekerSkill IN seekerSkills
              FOR positionSkill IN positionSkills
                FILTER seekerSkill.skillId == positionSkill.skillId
                RETURN {
                  skillId: seekerSkill.skillId,
                  name: seekerSkill.name,
                  seekerProficiency: seekerSkill.proficiency,
                  positionImportance: positionSkill.importance,
                  matchScore: seekerSkill.proficiency * positionSkill.importance,
                  required: positionSkill.required
                }
          )

          LET requiredSkillsCount = COUNT(
            FOR skill IN positionSkills
              FILTER skill.required == true
              RETURN 1
          )

          LET matchedRequiredSkillsCount = COUNT(
            FOR match IN matchingSkills
              FILTER match.required == true
              RETURN 1
          )

          LET matchScore = SUM(
            FOR match IN matchingSkills
              RETURN match.matchScore
          )

          LET hiringAuthority = FIRST(
            FOR ha IN hiringAuthorities
              FILTER ha._id == position.hiringAuthorityId
              RETURN ha
          )

          LET company = FIRST(
            FOR c IN companies
              FOR ha IN hiringAuthorities
                FILTER ha._id == position.hiringAuthorityId
                FILTER c._id == ha.companyId
                RETURN c
          )

          FILTER matchedRequiredSkillsCount == requiredSkillsCount

          SORT matchScore DESC

          RETURN {
            position: position,
            hiringAuthority: hiringAuthority,
            company: company,
            matchingSkills: matchingSkills,
            matchScore: matchScore,
            matchPercentage: matchScore / (SUM(FOR skill IN positionSkills RETURN skill.importance) * 5) * 100
          }
      `;
    } else if (positionId) {
      // Find matching job seekers for a position
      query = aql`
        LET position = DOCUMENT(${positionId})
        LET positionSkills = (
          FOR skill, edge IN OUTBOUND position._id positionSkills
            RETURN {
              skillId: skill._id,
              name: skill.name,
              importance: edge.importance,
              required: edge.required
            }
        )

        FOR seeker IN jobSeekers
          LET seekerSkills = (
            FOR skill, edge IN OUTBOUND seeker._id seekerSkills
              RETURN {
                skillId: skill._id,
                name: skill.name,
                proficiency: edge.proficiency,
                yearsOfExperience: edge.yearsOfExperience
              }
          )

          LET matchingSkills = (
            FOR positionSkill IN positionSkills
              FOR seekerSkill IN seekerSkills
                FILTER positionSkill.skillId == seekerSkill.skillId
                RETURN {
                  skillId: positionSkill.skillId,
                  name: positionSkill.name,
                  seekerProficiency: seekerSkill.proficiency,
                  positionImportance: positionSkill.importance,
                  matchScore: seekerSkill.proficiency * positionSkill.importance,
                  required: positionSkill.required
                }
          )

          LET requiredSkillsCount = COUNT(
            FOR skill IN positionSkills
              FILTER skill.required == true
              RETURN 1
          )

          LET matchedRequiredSkillsCount = COUNT(
            FOR match IN matchingSkills
              FILTER match.required == true
              RETURN 1
          )

          LET matchScore = SUM(
            FOR match IN matchingSkills
              RETURN match.matchScore
          )

          FILTER matchedRequiredSkillsCount == requiredSkillsCount

          SORT matchScore DESC

          RETURN {
            jobSeeker: seeker,
            matchingSkills: matchingSkills,
            matchScore: matchScore,
            matchPercentage: matchScore / (SUM(FOR skill IN positionSkills RETURN skill.importance) * 5) * 100
          }
      `;
    } else if (companyId) {
      // Find all positions for a company with potential matches
      query = aql`
        LET company = DOCUMENT(${companyId})

        FOR ha IN hiringAuthorities
          FILTER ha.companyId == ${companyId}

          FOR position IN OUTBOUND ha._id hiringAuthorityPositions
            LET positionSkills = (
              FOR skill, edge IN OUTBOUND position._id positionSkills
                RETURN {
                  skillId: skill._id,
                  name: skill.name,
                  importance: edge.importance,
                  required: edge.required
                }
            )

            LET matchingJobSeekers = (
              FOR seeker IN jobSeekers
                LET seekerSkills = (
                  FOR skill, edge IN OUTBOUND seeker._id seekerSkills
                    RETURN {
                      skillId: skill._id,
                      name: skill.name,
                      proficiency: edge.proficiency,
                      yearsOfExperience: edge.yearsOfExperience
                    }
                )

                LET matchingSkills = (
                  FOR positionSkill IN positionSkills
                    FOR seekerSkill IN seekerSkills
                      FILTER positionSkill.skillId == seekerSkill.skillId
                      RETURN {
                        skillId: positionSkill.skillId,
                        name: positionSkill.name,
                        seekerProficiency: seekerSkill.proficiency,
                        positionImportance: positionSkill.importance,
                        matchScore: seekerSkill.proficiency * positionSkill.importance,
                        required: positionSkill.required
                      }
                )

                LET requiredSkillsCount = COUNT(
                  FOR skill IN positionSkills
                    FILTER skill.required == true
                    RETURN 1
                )

                LET matchedRequiredSkillsCount = COUNT(
                  FOR match IN matchingSkills
                    FILTER match.required == true
                    RETURN 1
                )

                LET matchScore = SUM(
                  FOR match IN matchingSkills
                    RETURN match.matchScore
                )

                FILTER matchedRequiredSkillsCount == requiredSkillsCount

                SORT matchScore DESC

                RETURN {
                  jobSeeker: seeker,
                  matchingSkills: matchingSkills,
                  matchScore: matchScore,
                  matchPercentage: matchScore / (SUM(FOR skill IN positionSkills RETURN skill.importance) * 5) * 100
                }
            )

            RETURN {
              position: position,
              hiringAuthority: ha,
              matchingJobSeekers: matchingJobSeekers
            }
      `;
    } else {
      return NextResponse.json(
        { error: 'Missing required parameter: jobSeekerId, positionId, or companyId' },
        { status: 400 }
      );
    }

    const cursor = await db.query(query);
    const result = await cursor.all();

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error finding matches:', error);
    return NextResponse.json(
      { error: 'Failed to find matches' },
      { status: 500 }
    );
  }
}
EOL

  print_success "API routes created successfully."
}

# Create React components for the UI
create_ui_components() {
  print_section "Creating UI Components"

  # Create components directory
  mkdir -p src/components/ui
  mkdir -p src/components/job-seekers
  mkdir -p src/components/companies
  mkdir -p src/components/hiring-authorities
  mkdir -p src/components/positions
  mkdir -p src/components/graph

  # Create base UI components
  cat > src/components/ui/Button.tsx << 'EOL'
import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  isLoading?: boolean;
}

export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  isLoading = false,
  className = '',
  disabled,
  ...props
}) => {
  const baseStyles = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50';

  const variantStyles = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus-visible:ring-blue-500',
    secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200 focus-visible:ring-gray-500',
    outline: 'border border-gray-300 bg-transparent hover:bg-gray-50 focus-visible:ring-gray-500',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500',
  };

  const sizeStyles = {
    sm: 'h-8 px-3 text-xs',
    md: 'h-10 px-4 py-2',
    lg: 'h-12 px-6 py-3 text-lg',
  };

  const loadingStyles = isLoading ? 'cursor-not-allowed' : '';

  return (
    <button
      className={`${baseStyles} ${variantStyles[variant]} ${sizeStyles[size]} ${loadingStyles} ${className}`}
      disabled={disabled || isLoading}
      {...props}
    >
      {isLoading && (
        <svg
          className="mr-2 h-4 w-4 animate-spin"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          ></circle>
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
      )}
      {children}
    </button>
  );
};
EOL

  cat > src/components/ui/Card.tsx << 'EOL'
import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
}

export const Card: React.FC<CardProps> = ({ children, className = '' }) => {
  return (
    <div className={`rounded-lg border bg-white shadow-sm ${className}`}>
      {children}
    </div>
  );
};

interface CardHeaderProps {
  children: React.ReactNode;
  className?: string;
}

export const CardHeader: React.FC<CardHeaderProps> = ({ children, className = '' }) => {
  return (
    <div className={`border-b px-6 py-4 ${className}`}>
      {children}
    </div>
  );
};

interface CardContentProps {
  children: React.ReactNode;
  className?: string;
}

export const CardContent: React.FC<CardContentProps> = ({ children, className = '' }) => {
  return (
    <div className={`p-6 ${className}`}>
      {children}
    </div>
  );
};

interface CardFooterProps {
  children: React.ReactNode;
  className?: string;
}

export const CardFooter: React.FC<CardFooterProps> = ({ children, className = '' }) => {
  return (
    <div className={`border-t px-6 py-4 ${className}`}>
      {children}
    </div>
  );
};
EOL

  # Create graph visualization component
  cat > src/components/graph/GraphVisualization.tsx << 'EOL'
'use client';

import React, { useRef, useEffect } from 'react';
import ForceGraph3D from 'react-force-graph-3d';
import { useThree, Canvas } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import * as THREE from 'three';

interface Node {
  id: string;
  name: string;
  type: 'jobSeeker' | 'company' | 'hiringAuthority' | 'position' | 'skill';
  val?: number;
}

interface Link {
  source: string;
  target: string;
  type: string;
  value?: number;
}

interface GraphData {
  nodes: Node[];
  links: Link[];
}

interface GraphVisualizationProps {
  data: GraphData;
  onNodeClick?: (node: Node) => void;
  height?: number;
  width?: number;
}

export const GraphVisualization: React.FC<GraphVisualizationProps> = ({
  data,
  onNodeClick,
  height = 600,
  width = 800,
}) => {
  const graphRef = useRef<any>();

  const nodeColors = {
    jobSeeker: '#4299E1', // blue
    company: '#48BB78', // green
    hiringAuthority: '#ED8936', // orange
    position: '#9F7AEA', // purple
    skill: '#F56565', // red
  };

  useEffect(() => {
    if (graphRef.current) {
      // Add custom node and link styling
      graphRef.current
        .nodeColor((node: Node) => nodeColors[node.type])
        .nodeLabel((node: Node) => `${node.name} (${node.type})`)
        .linkWidth((link: Link) => link.value || 1)
        .linkDirectionalParticles(2)
        .linkDirectionalParticleWidth(2);

      // Center the graph
      graphRef.current.zoomToFit(1000, 50);
    }
  }, [data]);

  return (
    <div className="relative" style={{ height, width }}>
      <ForceGraph3D
        ref={graphRef}
        graphData={data}
        nodeAutoColorBy="type"
        nodeVal={(node) => node.val || 1}
        onNodeClick={onNodeClick}
        linkDirectionalArrowLength={3.5}
        linkDirectionalArrowRelPos={1}
        linkCurvature={0.25}
        backgroundColor="#ffffff"
      />
    </div>
  );
};

// Simplified 2D version for smaller screens or simpler visualizations
export const SimpleGraphVisualization: React.FC<GraphVisualizationProps> = ({
  data,
  onNodeClick,
  height = 400,
  width = 600,
}) => {
  const graphRef = useRef<any>();

  const nodeColors = {
    jobSeeker: '#4299E1', // blue
    company: '#48BB78', // green
    hiringAuthority: '#ED8936', // orange
    position: '#9F7AEA', // purple
    skill: '#F56565', // red
  };

  return (
    <div className="relative" style={{ height, width }}>
      <ForceGraph3D
        ref={graphRef}
        graphData={data}
        nodeAutoColorBy="type"
        nodeVal={(node) => node.val || 1}
        onNodeClick={onNodeClick}
        linkDirectionalArrowLength={3.5}
        linkDirectionalArrowRelPos={1}
        backgroundColor="#ffffff"
        enableNodeDrag={true}
        enableZoomPanInteraction={true}
      />
    </div>
  );
};
EOL

  # Create job seeker components
  cat > src/components/job-seekers/JobSeekerCard.tsx << 'EOL'
import React from 'react';
import Link from 'next/link';
import { Card, CardHeader, CardContent, CardFooter } from '../ui/Card';
import { Button } from '../ui/Button';

interface Skill {
  _id: string;
  name: string;
  category: string;
  level: string;
  proficiency?: number;
  yearsOfExperience?: number;
}

interface JobSeeker {
  _id: string;
  name: string;
  email: string;
  title: string;
  experience: number;
  education: string;
  location: string;
  bio: string;
  skills?: Skill[];
}

interface JobSeekerCardProps {
  jobSeeker: JobSeeker;
  showActions?: boolean;
}

export const JobSeekerCard: React.FC<JobSeekerCardProps> = ({
  jobSeeker,
  showActions = true,
}) => {
  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex flex-col space-y-1.5">
          <h3 className="text-2xl font-semibold">{jobSeeker.name}</h3>
          <p className="text-sm text-gray-500">{jobSeeker.title}</p>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h4 className="font-medium">Contact</h4>
            <p className="text-sm">{jobSeeker.email}</p>
          </div>
          <div>
            <h4 className="font-medium">Location</h4>
            <p className="text-sm">{jobSeeker.location}</p>
          </div>
          <div>
            <h4 className="font-medium">Experience</h4>
            <p className="text-sm">{jobSeeker.experience} years</p>
          </div>
          <div>
            <h4 className="font-medium">Education</h4>
            <p className="text-sm">{jobSeeker.education}</p>
          </div>
          <div>
            <h4 className="font-medium">Bio</h4>
            <p className="text-sm">{jobSeeker.bio}</p>
          </div>
          {jobSeeker.skills && (
            <div>
              <h4 className="font-medium">Skills</h4>
              <div className="mt-2 flex flex-wrap gap-2">
                {jobSeeker.skills.map((skill) => (
                  <span
                    key={skill._id}
                    className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800"
                  >
                    {skill.name}
                    {skill.proficiency && ` (${skill.proficiency}/5)`}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
      {showActions && (
        <CardFooter className="flex justify-between">
          <Link href={`/job-seekers/${jobSeeker._id}`} passHref>
            <Button variant="outline">View Profile</Button>
          </Link>
          <Link href={`/job-seekers/${jobSeeker._id}/matches`} passHref>
            <Button>Find Matches</Button>
          </Link>
        </CardFooter>
      )}
    </Card>
  );
};
EOL

  # Create company components
  cat > src/components/companies/CompanyCard.tsx << 'EOL'
import React from 'react';
import Link from 'next/link';
import { Card, CardHeader, CardContent, CardFooter } from '../ui/Card';
import { Button } from '../ui/Button';

interface Company {
  _id: string;
  name: string;
  industry: string;
  size: number;
  location: string;
  description: string;
  website: string;
}

interface CompanyCardProps {
  company: Company;
  showActions?: boolean;
}

export const CompanyCard: React.FC<CompanyCardProps> = ({
  company,
  showActions = true,
}) => {
  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex flex-col space-y-1.5">
          <h3 className="text-2xl font-semibold">{company.name}</h3>
          <p className="text-sm text-gray-500">{company.industry}</p>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h4 className="font-medium">Size</h4>
            <p className="text-sm">{company.size} employees</p>
          </div>
          <div>
            <h4 className="font-medium">Location</h4>
            <p className="text-sm">{company.location}</p>
          </div>
          <div>
            <h4 className="font-medium">Website</h4>
            <a
              href={company.website}
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm text-blue-600 hover:underline"
            >
              {company.website}
            </a>
          </div>
          <div>
            <h4 className="font-medium">Description</h4>
            <p className="text-sm">{company.description}</p>
          </div>
        </div>
      </CardContent>
      {showActions && (
        <CardFooter className="flex justify-between">
          <Link href={`/companies/${company._id}`} passHref>
            <Button variant="outline">View Details</Button>
          </Link>
          <Link href={`/companies/${company._id}/positions`} passHref>
            <Button>View Positions</Button>
          </Link>
        </CardFooter>
      )}
    </Card>
  );
};
EOL

  # Create position components
  cat > src/components/positions/PositionCard.tsx << 'EOL'
import React from 'react';
import Link from 'next/link';
import { Card, CardHeader, CardContent, CardFooter } from '../ui/Card';
import { Button } from '../ui/Button';

interface Skill {
  _id: string;
  name: string;
  importance?: number;
  required?: boolean;
}

interface Position {
  _id: string;
  title: string;
  description: string;
  location: string;
  remote: boolean;
  salary: {
    min: number;
    max: number;
  };
  requiredExperience: number;
  hiringAuthorityId: string;
  skills?: Skill[];
  hiringAuthority?: {
    name: string;
    title: string;
  };
  company?: {
    name: string;
  };
}

interface PositionCardProps {
  position: Position;
  showActions?: boolean;
}

export const PositionCard: React.FC<PositionCardProps> = ({
  position,
  showActions = true,
}) => {
  const formatSalary = (min: number, max: number) => {
    return `$${min.toLocaleString()} - $${max.toLocaleString()}`;
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex flex-col space-y-1.5">
          <h3 className="text-2xl font-semibold">{position.title}</h3>
          {position.company && (
            <p className="text-sm text-gray-500">{position.company.name}</p>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h4 className="font-medium">Location</h4>
            <p className="text-sm">
              {position.location}
              {position.remote && ' (Remote available)'}
            </p>
          </div>
          <div>
            <h4 className="font-medium">Salary Range</h4>
            <p className="text-sm">
              {formatSalary(position.salary.min, position.salary.max)}
            </p>
          </div>
          <div>
            <h4 className="font-medium">Required Experience</h4>
            <p className="text-sm">{position.requiredExperience} years</p>
          </div>
          {position.hiringAuthority && (
            <div>
              <h4 className="font-medium">Hiring Manager</h4>
              <p className="text-sm">{position.hiringAuthority.name}</p>
            </div>
          )}
          <div>
            <h4 className="font-medium">Description</h4>
            <p className="text-sm">{position.description}</p>
          </div>
          {position.skills && (
            <div>
              <h4 className="font-medium">Required Skills</h4>
              <div className="mt-2 flex flex-wrap gap-2">
                {position.skills.map((skill) => (
                  <span
                    key={skill._id}
                    className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                      skill.required
                        ? 'bg-red-100 text-red-800'
                        : 'bg-blue-100 text-blue-800'
                    }`}
                  >
                    {skill.name}
                    {skill.importance && ` (${skill.importance}/5)`}
                    {skill.required && ' *'}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
      {showActions && (
        <CardFooter className="flex justify-between">
          <Link href={`/positions/${position._id}`} passHref>
            <Button variant="outline">View Details</Button>
          </Link>
          <Link href={`/positions/${position._id}/matches`} passHref>
            <Button>Find Matches</Button>
          </Link>
        </CardFooter>
      )}
    </Card>
  );
};
EOL

  print_success "UI components created successfully."
}

# Create Next.js pages
create_nextjs_pages() {
  print_section "Creating Next.js Pages"

  # Create layout
  cat > src/app/layout.tsx << 'EOL'
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import Link from 'next/link';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Candid Connections',
  description: 'Job seeker to hiring authority matching platform',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <div className="flex min-h-screen flex-col">
          <header className="sticky top-0 z-50 border-b bg-white">
            <div className="container mx-auto flex h-16 items-center justify-between px-4">
              <Link href="/" className="flex items-center space-x-2">
                <span className="text-xl font-bold">Candid Connections</span>
              </Link>
              <nav className="flex items-center space-x-6">
                <Link href="/job-seekers" className="text-sm font-medium hover:underline">
                  Job Seekers
                </Link>
                <Link href="/companies" className="text-sm font-medium hover:underline">
                  Companies
                </Link>
                <Link href="/positions" className="text-sm font-medium hover:underline">
                  Positions
                </Link>
                <Link href="/matches" className="text-sm font-medium hover:underline">
                  Matches
                </Link>
              </nav>
            </div>
          </header>
          <main className="flex-1">{children}</main>
          <footer className="border-t py-6">
            <div className="container mx-auto px-4">
              <div className="flex flex-col items-center justify-between gap-4 md:flex-row">
                <p className="text-sm text-gray-500">
                  &copy; {new Date().getFullYear()} Candid Connections. All rights reserved.
                </p>
                <nav className="flex items-center space-x-4">
                  <Link href="/about" className="text-sm text-gray-500 hover:underline">
                    About
                  </Link>
                  <Link href="/privacy" className="text-sm text-gray-500 hover:underline">
                    Privacy
                  </Link>
                  <Link href="/terms" className="text-sm text-gray-500 hover:underline">
                    Terms
                  </Link>
                </nav>
              </div>
            </div>
          </footer>
        </div>
      </body>
    </html>
  );
}
EOL

  # Create home page
  cat > src/app/page.tsx << 'EOL'
import Link from 'next/link';
import { Button } from '@/components/ui/Button';

export default function Home() {
  return (
    <div className="flex flex-col">
      <section className="bg-gradient-to-r from-blue-600 to-indigo-700 py-20 text-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
            <div className="flex flex-col justify-center space-y-4">
              <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">
                Connect Job Seekers with the Right Hiring Authorities
              </h1>
              <p className="text-lg text-blue-100">
                Using graph database technology to find meaningful connections between job seekers and hiring authorities based on skills and requirements.
              </p>
              <div className="flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
                <Link href="/job-seekers" passHref>
                  <Button size="lg" className="w-full sm:w-auto">
                    Find Jobs
                  </Button>
                </Link>
                <Link href="/companies" passHref>
                  <Button size="lg" variant="outline" className="w-full border-white text-white hover:bg-white hover:text-blue-600 sm:w-auto">
                    For Employers
                  </Button>
                </Link>
              </div>
            </div>
            <div className="hidden md:flex md:items-center md:justify-center">
              <div className="relative h-80 w-80">
                <div className="absolute inset-0 rounded-full bg-blue-500 opacity-20 blur-3xl"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="240"
                    height="240"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="1"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <circle cx="12" cy="12" r="10" />
                    <circle cx="12" cy="12" r="4" />
                    <line x1="4.93" y1="4.93" x2="9.17" y2="9.17" />
                    <line x1="14.83" y1="14.83" x2="19.07" y2="19.07" />
                    <line x1="14.83" y1="9.17" x2="19.07" y2="4.93" />
                    <line x1="4.93" y1="19.07" x2="9.17" y2="14.83" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="mb-12 text-center">
            <h2 className="text-3xl font-bold">How It Works</h2>
            <p className="mt-4 text-lg text-gray-600">
              Our platform uses graph database technology to create meaningful connections
            </p>
          </div>
          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            <div className="rounded-lg border p-6">
              <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-blue-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                  <circle cx="9" cy="7" r="4" />
                  <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                  <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                </svg>
              </div>
              <h3 className="mb-2 text-xl font-bold">For Job Seekers</h3>
              <p className="text-gray-600">
                Create a profile with your skills and experience, and our system will match you with the right hiring authorities.
              </p>
            </div>
            <div className="rounded-lg border p-6">
              <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-blue-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <rect x="2" y="7" width="20" height="14" rx="2" ry="2" />
                  <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16" />
                </svg>
              </div>
              <h3 className="mb-2 text-xl font-bold">For Companies</h3>
              <p className="text-gray-600">
                List your company and positions, and our system will connect you with qualified candidates based on your requirements.
              </p>
            </div>
            <div className="rounded-lg border p-6">
              <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-blue-600">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z" />
                  <line x1="12" y1="9" x2="12" y2="13" />
                  <line x1="12" y1="17" x2="12.01" y2="17" />
                </svg>
              </div>
              <h3 className="mb-2 text-xl font-bold">Graph Visualization</h3>
              <p className="text-gray-600">
                Explore the connections between job seekers, companies, and hiring authorities through interactive graph visualizations.
              </p>
            </div>
          </div>
        </div>
      </section>

      <section className="bg-gray-50 py-20">
        <div className="container mx-auto px-4">
          <div className="mb-12 text-center">
            <h2 className="text-3xl font-bold">Why Use Graph Database Technology?</h2>
            <p className="mt-4 text-lg text-gray-600">
              Graph databases excel at finding complex relationships between entities
            </p>
          </div>
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
            <div className="space-y-4">
              <h3 className="text-xl font-bold">Relationship-First Approach</h3>
              <p className="text-gray-600">
                Unlike traditional databases that focus on tables and rows, graph databases prioritize the relationships between entities, making them perfect for social networks and recommendation systems.
              </p>

              <h3 className="text-xl font-bold">Complex Pattern Matching</h3>
              <p className="text-gray-600">
                Graph databases excel at finding complex patterns and relationships that would be difficult or impossible to express in SQL.
              </p>

              <h3 className="text-xl font-bold">Performance for Connected Data</h3>
              <p className="text-gray-600">
                When working with highly connected data, graph databases offer superior performance for traversing relationships compared to traditional databases.
              </p>
            </div>
            <div className="space-y-4">
              <h3 className="text-xl font-bold">Flexible Schema</h3>
              <p className="text-gray-600">
                Graph databases allow for flexible schemas that can evolve over time, making them ideal for domains where data structures may change.
              </p>

              <h3 className="text-xl font-bold">Intuitive Data Modeling</h3>
              <p className="text-gray-600">
                The nodes and relationships in a graph database closely mirror how we naturally think about domains, making data modeling more intuitive.
              </p>

              <h3 className="text-xl font-bold">Powerful Visualization</h3>
              <p className="text-gray-600">
                Graph data naturally lends itself to visualization, allowing users to explore and understand complex relationships visually.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
EOL

  # Create job seekers page
  cat > src/app/job-seekers/page.tsx << 'EOL'
import { JobSeekerCard } from '@/components/job-seekers/JobSeekerCard';

async function getJobSeekers() {
  const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'}/api/job-seekers`, { cache: 'no-store' });

  if (!res.ok) {
    throw new Error('Failed to fetch job seekers');
  }

  return res.json();
}

export default async function JobSeekersPage() {
  const jobSeekers = await getJobSeekers();

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="mb-8 text-3xl font-bold">Job Seekers</h1>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {jobSeekers.map((jobSeeker: any) => (
          <JobSeekerCard key={jobSeeker._id} jobSeeker={jobSeeker} />
        ))}
      </div>
    </div>
  );
}
EOL

  # Create job seeker detail page
  cat > src/app/job-seekers/[id]/page.tsx << 'EOL'
'use client';

import { useEffect, useState } from 'react';
import { JobSeekerCard } from '@/components/job-seekers/JobSeekerCard';
import { GraphVisualization } from '@/components/graph/GraphVisualization';
import { Button } from '@/components/ui/Button';
import Link from 'next/link';

interface PageProps {
  params: {
    id: string;
  };
}

export default function JobSeekerDetailPage({ params }: PageProps) {
  const [jobSeeker, setJobSeeker] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [graphData, setGraphData] = useState<any>({ nodes: [], links: [] });

  useEffect(() => {
    async function fetchJobSeeker() {
      try {
        const res = await fetch(`/api/job-seekers/${params.id}`);

        if (!res.ok) {
          throw new Error('Failed to fetch job seeker');
        }

        const data = await res.json();
        setJobSeeker(data);

        // Create graph data
        const nodes = [
          {
            id: data._id,
            name: data.name,
            type: 'jobSeeker',
            val: 2,
          },
        ];

        const links: any[] = [];

        if (data.skills) {
          data.skills.forEach((skill: any) => {
            nodes.push({
              id: skill._id,
              name: skill.name,
              type: 'skill',
              val: 1,
            });

            links.push({
              source: data._id,
              target: skill._id,
              type: 'has_skill',
              value: skill.proficiency || 1,
            });
          });
        }

        setGraphData({ nodes, links });
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchJobSeeker();
  }, [params.id]);

  if (loading) {
    return (
      <div className="container mx-auto flex items-center justify-center px-4 py-16">
        <div className="text-center">
          <div className="mb-4 h-12 w-12 animate-spin rounded-full border-4 border-blue-200 border-t-blue-600"></div>
          <p>Loading job seeker...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="rounded-lg bg-red-50 p-4 text-red-800">
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-3xl font-bold">{jobSeeker.name}</h1>
        <Link href={`/job-seekers/${params.id}/matches`} passHref>
          <Button>Find Matching Positions</Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        <div>
          <JobSeekerCard jobSeeker={jobSeeker} showActions={false} />
        </div>

        <div className="flex flex-col space-y-4">
          <h2 className="text-2xl font-bold">Skills Graph</h2>
          <div className="rounded-lg border bg-white p-4 shadow-sm">
            <GraphVisualization data={graphData} height={500} />
          </div>
        </div>
      </div>
    </div>
  );
}
EOL

  # Create job seeker matches page
  cat > src/app/job-seekers/[id]/matches/page.tsx << 'EOL'
'use client';

import { useEffect, useState } from 'react';
import { PositionCard } from '@/components/positions/PositionCard';
import { GraphVisualization } from '@/components/graph/GraphVisualization';
import { Button } from '@/components/ui/Button';
import Link from 'next/link';

interface PageProps {
  params: {
    id: string;
  };
}

export default function JobSeekerMatchesPage({ params }: PageProps) {
  const [jobSeeker, setJobSeeker] = useState<any>(null);
  const [matches, setMatches] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [graphData, setGraphData] = useState<any>({ nodes: [], links: [] });

  useEffect(() => {
    async function fetchData() {
      try {
        // Fetch job seeker
        const jobSeekerRes = await fetch(`/api/job-seekers/${params.id}`);

        if (!jobSeekerRes.ok) {
          throw new Error('Failed to fetch job seeker');
        }

        const jobSeekerData = await jobSeekerRes.json();
        setJobSeeker(jobSeekerData);

        // Fetch matches
        const matchesRes = await fetch(`/api/matches?jobSeekerId=${params.id}`);

        if (!matchesRes.ok) {
          throw new Error('Failed to fetch matches');
        }

        const matchesData = await matchesRes.json();
        setMatches(matchesData);

        // Create graph data
        const nodes: any[] = [
          {
            id: jobSeekerData._id,
            name: jobSeekerData.name,
            type: 'jobSeeker',
            val: 2,
          },
        ];

        const links: any[] = [];
        const skillsMap = new Map();

        // Add job seeker skills
        if (jobSeekerData.skills) {
          jobSeekerData.skills.forEach((skill: any) => {
            if (!skillsMap.has(skill._id)) {
              skillsMap.set(skill._id, {
                id: skill._id,
                name: skill.name,
                type: 'skill',
                val: 1,
              });

              nodes.push(skillsMap.get(skill._id));
            }

            links.push({
              source: jobSeekerData._id,
              target: skill._id,
              type: 'has_skill',
              value: skill.proficiency || 1,
            });
          });
        }

        // Add top 3 matches
        matchesData.slice(0, 3).forEach((match: any) => {
          // Add position node
          nodes.push({
            id: match.position._id,
            name: match.position.title,
            type: 'position',
            val: 1.5,
          });

          // Add company node
          nodes.push({
            id: match.company._id,
            name: match.company.name,
            type: 'company',
            val: 1.5,
          });

          // Add hiring authority node
          nodes.push({
            id: match.hiringAuthority._id,
            name: match.hiringAuthority.name,
            type: 'hiringAuthority',
            val: 1.5,
          });

          // Add links
          links.push({
            source: match.company._id,
            target: match.hiringAuthority._id,
            type: 'has_authority',
            value: 1,
          });

          links.push({
            source: match.hiringAuthority._id,
            target: match.position._id,
            type: 'manages_position',
            value: 1,
          });

          // Add matching skills links
          match.matchingSkills.forEach((skill: any) => {
            if (!skillsMap.has(skill.skillId)) {
              skillsMap.set(skill.skillId, {
                id: skill.skillId,
                name: skill.name,
                type: 'skill',
                val: 1,
              });

              nodes.push(skillsMap.get(skill.skillId));
            }

            links.push({
              source: match.position._id,
              target: skill.skillId,
              type: 'requires_skill',
              value: skill.positionImportance || 1,
            });
          });
        });

        setGraphData({ nodes, links });
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [params.id]);

  if (loading) {
    return (
      <div className="container mx-auto flex items-center justify-center px-4 py-16">
        <div className="text-center">
          <div className="mb-4 h-12 w-12 animate-spin rounded-full border-4 border-blue-200 border-t-blue-600"></div>
          <p>Finding matches...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="rounded-lg bg-red-50 p-4 text-red-800">
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-3xl font-bold">Matches for {jobSeeker.name}</h1>
        <Link href={`/job-seekers/${params.id}`} passHref>
          <Button variant="outline">Back to Profile</Button>
        </Link>
      </div>

      <div className="mb-8">
        <h2 className="mb-4 text-2xl font-bold">Connection Graph</h2>
        <div className="rounded-lg border bg-white p-4 shadow-sm">
          <GraphVisualization data={graphData} height={500} />
        </div>
      </div>

      <div>
        <h2 className="mb-4 text-2xl font-bold">Matching Positions</h2>

        {matches.length === 0 ? (
          <div className="rounded-lg bg-yellow-50 p-4 text-yellow-800">
            <p>No matching positions found.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {matches.map((match: any) => (
              <div key={match.position._id} className="flex flex-col">
                <div className="mb-2 rounded-t-lg bg-blue-100 p-2 text-blue-800">
                  <p className="font-medium">Match Score: {match.matchPercentage.toFixed(1)}%</p>
                </div>
                <PositionCard position={match.position} />
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
EOL

  print_success "Next.js pages created successfully."
}

# Create README.md with documentation
create_readme() {
  print_section "Creating README.md Documentation"

  cat > README.md << 'EOL'
# Candid Connections

A Next.js application that uses graph database technology to match job seekers with hiring authorities based on skills and requirements.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Technology Stack](#technology-stack)
- [Project Architecture](#project-architecture)
- [Database Architecture](#database-architecture)
- [Getting Started](#getting-started)
- [Development](#development)
- [Deployment](#deployment)
- [Why Graph Databases?](#why-graph-databases)

## Overview

Candid Connections is a platform that connects job seekers with the right hiring authorities within companies by analyzing the relationships between skills, requirements, and organizational hierarchies. The application uses a graph database (ArangoDB) to store and query these relationships, providing powerful matching capabilities and visualizations.

## Features

- Job seeker profiles with skills and experience
- Company profiles with organizational hierarchies
- Position listings with required skills and experience
- Matching algorithm based on graph relationships
- Interactive graph visualizations of connections
- CRUD operations for all entities
- Responsive UI with Tailwind CSS

## Technology Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: ArangoDB (graph database)
- **Visualization**: React Force Graph
- **Deployment**: Docker, AWS Amplify (optional)

## Project Architecture

```mermaid
graph TD
    Client[Client Browser] --> NextJS[Next.js Application]
    NextJS --> AppRouter[App Router]
    AppRouter --> Pages[Pages]
    AppRouter --> APIRoutes[API Routes]

    Pages --> Components[React Components]
    Components --> UI[UI Components]
    Components --> GraphViz[Graph Visualization]

    APIRoutes --> DBConnection[Database Connection]
    DBConnection --> ArangoDB[ArangoDB]

    ArangoDB --> DocumentCollections[Document Collections]
    ArangoDB --> EdgeCollections[Edge Collections]

    DocumentCollections --> JobSeekers[Job Seekers]
    DocumentCollections --> Companies[Companies]
    DocumentCollections --> HiringAuthorities[Hiring Authorities]
    DocumentCollections --> Positions[Positions]
    DocumentCollections --> Skills[Skills]

    EdgeCollections --> SeekerSkills[Seeker-Skills]
    EdgeCollections --> PositionSkills[Position-Skills]
    EdgeCollections --> CompanyHiringAuthorities[Company-HiringAuthorities]
    EdgeCollections --> HiringAuthorityPositions[HiringAuthority-Positions]
```

## Database Architecture

```mermaid
erDiagram
    JobSeeker ||--o{ SeekerSkill : has
    Skill ||--o{ SeekerSkill : belongs_to
    Skill ||--o{ PositionSkill : belongs_to
    Position ||--o{ PositionSkill : requires
    HiringAuthority ||--o{ Position : manages
    Company ||--o{ HiringAuthority : employs

    JobSeeker {
        string _id
        string name
        string email
        string title
        number experience
        string education
        string location
        string bio
    }

    Company {
        string _id
        string name
        string industry
        number size
        string location
        string description
        string website
    }

    HiringAuthority {
        string _id
        string name
        string email
        string title
        string level
        number directReports
        string companyId
    }

    Position {
        string _id
        string title
        string description
        string location
        boolean remote
        object salary
        number requiredExperience
        string hiringAuthorityId
    }

    Skill {
        string _id
        string name
        string category
        string level
    }

    SeekerSkill {
        string _from
        string _to
        number proficiency
        number yearsOfExperience
    }

    PositionSkill {
        string _from
        string _to
        number importance
        boolean required
    }

    CompanyHiringAuthority {
        string _from
        string _to
        string relationship
    }

    HiringAuthorityPosition {
        string _from
        string _to
        string relationship
    }
```

## Getting Started

### Prerequisites

- Node.js 18 or higher
- npm or yarn
- Docker (for running ArangoDB locally)

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/candid-connections.git
   cd candid-connections
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start ArangoDB using Docker:
   ```bash
   cd docker
   docker-compose up -d
   ```

4. Initialize the database:
   ```bash
   node scripts/init-db.js
   ```

5. Seed the database with sample data:
   ```bash
   node scripts/seed-data.js
   ```

6. Start the development server:
   ```bash
   npm run dev
   ```

7. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Development

### Project Structure

```
candid-connections/
├── docker/
│   └── docker-compose.yml
├── scripts/
│   ├── init-db.js
│   └── seed-data.js
├── src/
│   ├── app/
│   │   ├── api/
│   │   │   ├── job-seekers/
│   │   │   ├── companies/
│   │   │   ├── hiring-authorities/
│   │   │   ├── positions/
│   │   │   ├── skills/
│   │   │   └── matches/
│   │   ├── job-seekers/
│   │   ├── companies/
│   │   ├── positions/
│   │   ├── matches/
│   │   ├── layout.tsx
│   │   └── page.tsx
│   ├── components/
│   │   ├── ui/
│   │   ├── job-seekers/
│   │   ├── companies/
│   │   ├── hiring-authorities/
│   │   ├── positions/
│   │   └── graph/
│   └── lib/
│       └── db/
│           └── connection.ts
├── .env
├── .env.local
├── .gitignore
├── next.config.js
├── package.json
├── README.md
└── tsconfig.json
```

### Environment Variables

Create a `.env.local` file with the following variables:

```
NEXT_PUBLIC_API_URL=http://localhost:3000
ARANGO_URL=http://localhost:8529
ARANGO_USERNAME=root
ARANGO_PASSWORD=rootpassword
ARANGO_DB_NAME=candid_connections
```

## Deployment

### AWS Amplify Deployment

1. Install the AWS Amplify CLI:
   ```bash
   npm install -g @aws-amplify/cli
   ```

2. Initialize Amplify:
   ```bash
   amplify init
   ```

3. Add hosting:
   ```bash
   amplify add hosting
   ```

4. Publish:
   ```bash
   amplify publish
   ```

### Docker Deployment

1. Build the Docker image:
   ```bash
   docker build -t candid-connections .
   ```

2. Run the container:
   ```bash
   docker run -p 3000:3000 candid-connections
   ```

## Why Graph Databases?

### Benefits of Using ArangoDB for This Application

1. **Natural Representation of Relationships**

   Graph databases like ArangoDB naturally represent the relationships between job seekers, skills, companies, hiring authorities, and positions. These relationships are first-class citizens in the database, making it easy to traverse and query complex networks of connections.

2. **Efficient Matching Algorithms**

   Finding matches between job seekers and positions requires traversing multiple relationships (job seeker → skills → positions → hiring authorities → companies). Graph databases excel at these types of traversals, making the matching process efficient and performant.

3. **Flexible Schema**

   As the application evolves, we may need to add new types of relationships or attributes. Graph databases provide schema flexibility that allows the data model to evolve without major migrations.

4. **Multi-Model Capabilities**

   ArangoDB is a multi-model database that supports document, key-value, and graph data models. This allows us to use the most appropriate model for each part of our application while maintaining a single database.

5. **Powerful Query Language**

   ArangoDB's AQL (ArangoDB Query Language) provides powerful graph traversal capabilities that make it easy to express complex matching queries that would be difficult or inefficient in a traditional relational database.

6. **Visualization-Friendly Data**

   Graph data naturally lends itself to visualization, making it easier to create interactive visualizations of the connections between entities in our application.

### Graph Database Concepts Used in This Application

1. **Nodes and Edges**

   In our application, entities like job seekers, companies, and skills are represented as nodes (documents), while relationships like "has_skill" or "employs" are represented as edges.

2. **Graph Traversals**

   The matching algorithm uses graph traversals to find paths between job seekers and positions through shared skills.

3. **Weighted Relationships**

   Edges in our graph have weights (e.g., skill proficiency, skill importance) that are used to calculate match scores.

4. **Centrality and Importance**

   The visualization component uses concepts like node centrality to determine the size and position of nodes in the graph visualization.

### Comparison with Traditional Relational Databases

For a job matching application like Candid Connections, a traditional relational database would require:

1. Complex joins across multiple tables to find matches
2. Difficulty representing hierarchical structures like company organization
3. Less efficient queries for finding paths between entities
4. More complex schema changes as the application evolves

Graph databases address these challenges by making relationships first-class citizens and providing efficient traversal capabilities.
EOL

  print_success "README.md created successfully."
}

# Create environment configuration
create_env_config() {
  print_section "Creating Environment Configuration"

  cat > .env << 'EOL'
NEXT_PUBLIC_API_URL=http://localhost:3000
ARANGO_URL=http://localhost:8529
ARANGO_USERNAME=root
ARANGO_PASSWORD=rootpassword
ARANGO_DB_NAME=candid_connections
EOL

  print_success "Environment configuration created."
}

# Create AWS deployment configuration
create_aws_config() {
  print_section "Creating AWS Deployment Configuration"

  mkdir -p amplify

  cat > amplify.yml << 'EOL'
version: 1
frontend:
  phases:
    preBuild:
      commands:
        - npm ci
    build:
      commands:
        - npm run build
  artifacts:
    baseDirectory: .next
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
EOL

  print_success "AWS deployment configuration created."
}

# Create Docker configuration
create_docker_config() {
  print_section "Creating Docker Configuration"

  cat > Dockerfile << 'EOL'
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
WORKDIR /app

# Install dependencies
COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Environment variables must be present at build time
ARG NEXT_PUBLIC_API_URL
ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}

# Build the application
RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

# Create a non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy the build output and necessary files
COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Set the correct permissions
USER nextjs

# Expose the port
EXPOSE 3000

# Environment variables
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

# Start the application
CMD ["node", "server.js"]
EOL

  print_success "Docker configuration created."
}

# Run the application setup
run_app_setup() {
  print_section "Running Application Setup"

  # Make the script executable
  chmod +x scripts/init-db.js
  chmod +x scripts/seed-data.js

  print_success "Application setup completed."
}

# Main function to run all steps
main() {
  print_section "Generating Candid Connections Application"

  # Check dependencies
  check_dependencies

  # Create Next.js application
  create_nextjs_app

  # Install additional dependencies
  install_dependencies

  # Create Docker Compose file for ArangoDB
  create_docker_compose

  # Create database setup scripts
  create_db_setup_scripts

  # Create Next.js API routes
  create_api_routes

  # Create React components for the UI
  create_ui_components

  # Create Next.js pages
  create_nextjs_pages

  # Create README.md with documentation
  create_readme

  # Create environment configuration
  create_env_config

  # Create AWS deployment configuration
  create_aws_config

  # Create Docker configuration
  create_docker_config

  # Run the application setup
  run_app_setup

  print_section "Application Generation Complete"
  echo -e "${GREEN}Candid Connections application has been successfully generated!${NC}"
  echo -e "${YELLOW}To start the application:${NC}"
  echo -e "1. Start ArangoDB: ${BLUE}cd docker && docker-compose up -d${NC}"
  echo -e "2. Initialize the database: ${BLUE}node scripts/init-db.js${NC}"
  echo -e "3. Seed the database: ${BLUE}node scripts/seed-data.js${NC}"
  echo -e "4. Start the development server: ${BLUE}npm run dev${NC}"
  echo -e "5. Open ${BLUE}http://localhost:3000${NC} in your browser"
  echo -e "\n${YELLOW}For more information, see the README.md file.${NC}"
}

# Run the main function
main "$@"
