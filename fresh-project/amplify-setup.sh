#!/bin/bash

# Colors for output
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
NC="\033[0m" # No Color

echo -e "${YELLOW}Starting AWS Amplify deployment setup...${NC}"

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
  echo -e "${RED}AWS CLI is not installed. Please install it and try again.${NC}"
  exit 1
fi

# Check if Amplify CLI is installed
if ! command -v amplify &> /dev/null; then
  echo -e "${YELLOW}Installing AWS Amplify CLI...${NC}"
  npm install -g @aws-amplify/cli
fi

# Check if user is logged in to AWS
if ! aws sts get-caller-identity &> /dev/null; then
  echo -e "${YELLOW}Please configure AWS CLI with your credentials:${NC}"
  aws configure
fi

# Initialize Amplify
echo -e "${YELLOW}Initializing Amplify...${NC}"
amplify init --yes

# Add hosting
echo -e "${YELLOW}Adding hosting...${NC}"
amplify add hosting --yes

# Push changes to Amplify
echo -e "${YELLOW}Pushing changes to Amplify...${NC}"
amplify push --yes

echo -e "${GREEN}Amplify setup complete!${NC}"
echo -e "${YELLOW}Next steps:${NC}"
echo -e "1. Set up your database (ArangoDB Cloud or EC2)"
echo -e "2. Configure environment variables in the Amplify Console"
echo -e "3. Deploy your application with 'amplify publish'"
