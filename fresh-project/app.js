// Global variables for graph state
let currentGraphData = null;
let currentGraph = null;
let is3DMode = false;
let isEditMode = false;
let selectedNode = null;

// Load dashboard stats
async function loadStats() {
    try {
        const response = await fetch('/api/stats');
        const data = await response.json();

        if (data.success) {
            const stats = data.data;

            // Update navigation card counts
            document.getElementById('job-seekers-count').textContent = stats.jobSeekers;
            document.getElementById('companies-count').textContent = stats.companies;
            document.getElementById('hiring-authorities-count').textContent = stats.hiringAuthorities;
            document.getElementById('positions-count').textContent = stats.positions;
            document.getElementById('skills-count').textContent = stats.skills;
            document.getElementById('connections-count').textContent = stats.connections;

            // Get actual matches count from API
            try {
                const matchesResponse = await fetch('/api/matches');
                const matchesData = await matchesResponse.json();
                if (matchesData.success) {
                    document.getElementById('matches-count').textContent = matchesData.data.length;
                } else {
                    document.getElementById('matches-count').textContent = '0';
                }
            } catch (error) {
                console.error('Error loading matches count:', error);
                document.getElementById('matches-count').textContent = '0';
            }

            // Note: Removed stats grid as it was redundant with navigation cards
        }
    } catch (error) {
        console.error('Error loading stats:', error);
    }
}

// Load job seekers
async function loadJobSeekers() {
    try {
        const response = await fetch('/api/job-seekers');
        const data = await response.json();

        if (data.success) {
            const jobSeekers = data.data;
            document.getElementById('content-area').innerHTML = `
                <div class="p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">👤 Job Seekers (${jobSeekers.length})</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        ${jobSeekers.map(seeker => `
                            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                                <h3 class="font-semibold text-gray-900">${seeker.name}</h3>
                                <p class="text-sm text-gray-600">${seeker.title}</p>
                                <p class="text-sm text-gray-500">${seeker.location}</p>
                                <p class="text-xs text-gray-400 mt-2">Experience: ${seeker.experience}</p>
                                <div class="mt-3 flex space-x-2">
                                    <button onclick="loadGraphVisualization('${seeker._id}', 'jobSeeker')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">
                                        🌐 View Graph
                                    </button>
                                    <button onclick="loadMatchesForSeeker('${seeker._id}')" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">
                                        🎯 View Matches
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading job seekers:', error);
    }
}

// Load companies
async function loadCompanies() {
    try {
        const [companiesResponse, hiringAuthoritiesResponse, positionsResponse] = await Promise.all([
            fetch('/api/companies'),
            fetch('/api/hiring-authorities'),
            fetch('/api/positions')
        ]);

        const [companiesData, hiringAuthoritiesData, positionsData] = await Promise.all([
            companiesResponse.json(),
            hiringAuthoritiesResponse.json(),
            positionsResponse.json()
        ]);

        if (companiesData.success && hiringAuthoritiesData.success && positionsData.success) {
            const companies = companiesData.data;
            const hiringAuthorities = hiringAuthoritiesData.data;
            const positions = positionsData.data;

            document.getElementById('content-area').innerHTML = `
                <div class="p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">🏢 Companies (${companies.length})</h2>
                    <div class="space-y-6">
                        ${companies.map(company => {
                            // Find hiring authorities for this company
                            const companyAuthorities = hiringAuthorities.filter(auth =>
                                auth.company === company._key || auth.company === company.name || auth.company === company._id
                            );

                            // Find positions for this company
                            const companyPositions = positions.filter(pos =>
                                pos.company === company._key || pos.company === company.name || pos.company === company._id
                            );

                            return `
                                <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                                    <!-- Company Header -->
                                    <div class="flex justify-between items-start mb-4">
                                        <div>
                                            <h3 class="font-semibold text-gray-900 text-xl">${company.name}</h3>
                                            <p class="text-sm text-gray-600">${company.industry}</p>
                                            <p class="text-sm text-gray-500">${company.location}</p>
                                        </div>
                                        <div class="text-right">
                                            <span class="text-xs bg-teal-100 text-teal-800 px-2 py-1 rounded">${company.size}</span>
                                            <p class="text-xs text-gray-500 mt-1">${company.employeeCount} employees</p>
                                        </div>
                                    </div>

                                    <p class="text-sm text-gray-600 mb-4">${company.description}</p>

                                    <!-- Company Statistics -->
                                    <div class="grid grid-cols-3 gap-4 mb-4 p-3 bg-gray-50 rounded-lg">
                                        <div class="text-center">
                                            <div class="text-lg font-bold text-orange-600">${companyAuthorities.length}</div>
                                            <div class="text-xs text-gray-600">Hiring Authorities</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-lg font-bold text-violet-600">${companyPositions.length}</div>
                                            <div class="text-xs text-gray-600">Open Positions</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-lg font-bold text-emerald-600">${Math.floor(companyPositions.length * 2.3)}</div>
                                            <div class="text-xs text-gray-600">Potential Matches</div>
                                        </div>
                                    </div>

                                    <!-- Hiring Authorities Section -->
                                    ${companyAuthorities.length > 0 ? `
                                        <div class="mb-4">
                                            <h4 class="font-medium text-gray-900 mb-2">👔 Hiring Authorities</h4>
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                                                ${companyAuthorities.map(auth => `
                                                    <div class="bg-orange-50 border border-orange-200 rounded p-3">
                                                        <div class="font-medium text-orange-900">${auth.name}</div>
                                                        <div class="text-sm text-orange-700">${auth.title}</div>
                                                        <div class="text-xs text-orange-600">${auth.department}</div>
                                                        <button onclick="loadHiringAuthorityMatches('${auth._id}')" class="mt-2 px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs hover:bg-orange-200">
                                                            View Matches
                                                        </button>
                                                    </div>
                                                `).join('')}
                                            </div>
                                        </div>
                                    ` : ''}

                                    <!-- Positions Section -->
                                    ${companyPositions.length > 0 ? `
                                        <div class="mb-4">
                                            <h4 class="font-medium text-gray-900 mb-2">💼 Open Positions</h4>
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                                                ${companyPositions.map(pos => `
                                                    <div class="bg-violet-50 border border-violet-200 rounded p-3">
                                                        <div class="font-medium text-violet-900">${pos.title}</div>
                                                        <div class="text-sm text-violet-700">${pos.location}</div>
                                                        <div class="text-xs text-violet-600">${pos.salary || 'Salary not specified'}</div>
                                                        <button onclick="loadPositionDetails('${pos._id}')" class="mt-2 px-2 py-1 bg-violet-100 text-violet-800 rounded text-xs hover:bg-violet-200">
                                                            View Details
                                                        </button>
                                                    </div>
                                                `).join('')}
                                            </div>
                                        </div>
                                    ` : ''}

                                    <!-- Action Buttons -->
                                    <div class="flex space-x-2 pt-3 border-t border-gray-200">
                                        <button onclick="loadGraphVisualization('${company._id}', 'company')" class="px-3 py-2 bg-teal-100 text-teal-800 rounded text-sm hover:bg-teal-200">
                                            🌐 View Company Graph
                                        </button>
                                        <button onclick="loadCompanyHierarchy('${company._id}')" class="px-3 py-2 bg-blue-100 text-blue-800 rounded text-sm hover:bg-blue-200">
                                            🏗️ View Hierarchy
                                        </button>
                                        <button onclick="loadMatchesForCompany('${company._id}')" class="px-3 py-2 bg-emerald-100 text-emerald-800 rounded text-sm hover:bg-emerald-200">
                                            🎯 View All Candidates
                                        </button>
                                    </div>
                                </div>
                            `;
                        }).join('')}
                    </div>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading companies:', error);
        document.getElementById('content-area').innerHTML = `
            <div class="p-6">
                <h2 class="text-2xl font-bold text-red-600 mb-4">❌ Error Loading Companies</h2>
                <p class="text-gray-600">There was an error loading the companies data. Please try again.</p>
            </div>
        `;
    }
}

// Load hiring authorities
async function loadHiringAuthorities() {
    try {
        const response = await fetch('/api/hiring-authorities');
        const data = await response.json();

        if (data.success) {
            const hiringAuthorities = data.data;
            document.getElementById('content-area').innerHTML = `
                <div class="p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">👔 Hiring Authorities (${hiringAuthorities.length})</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        ${hiringAuthorities.map(authority => `
                            <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                                <div class="flex justify-between items-start mb-4">
                                    <div>
                                        <h3 class="font-semibold text-gray-900 text-lg">${authority.name}</h3>
                                        <p class="text-sm text-gray-600">${authority.title}</p>
                                        <p class="text-sm text-gray-500">${authority.department}</p>
                                    </div>
                                    <div class="text-right">
                                        <span class="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">${authority.level}</span>
                                    </div>
                                </div>
                                <div class="mb-4">
                                    <p class="text-sm text-gray-600"><strong>Hiring Scope:</strong> ${authority.hiringScope}</p>
                                    <p class="text-sm text-gray-500"><strong>Email:</strong> ${authority.email}</p>
                                </div>
                                <div class="flex space-x-2">
                                    <button onclick="loadHiringAuthorityMatches('${authority._id}')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">
                                        🎯 View Best Matches
                                    </button>
                                    <button onclick="loadGraphVisualization('${authority._id}', 'hiringAuthority')" class="px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs hover:bg-orange-200">
                                        🌐 View Graph
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading hiring authorities:', error);
    }
}

// Load hiring authority matches
async function loadHiringAuthorityMatches(authorityId) {
    try {
        const response = await fetch(`/api/hiring-matches/${authorityId}`);
        const data = await response.json();

        if (data.success) {
            const { authority, positions, matches } = data.data;

            document.getElementById('content-area').innerHTML = `
                <div class="p-6">
                    <div class="mb-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">🎯 Best Matches for ${authority.name}</h2>
                        <p class="text-gray-600">${authority.title} • ${authority.department}</p>
                        <p class="text-sm text-gray-500">Managing ${positions.length} positions</p>
                    </div>

                    <div class="space-y-4">
                        ${matches.map(match => `
                            <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                                <div class="flex justify-between items-start mb-4">
                                    <div>
                                        <h3 class="font-semibold text-gray-900">${match.jobSeeker.name}</h3>
                                        <p class="text-sm text-gray-600">${match.jobSeeker.title}</p>
                                        <p class="text-sm text-gray-500">${match.jobSeeker.location}</p>
                                    </div>
                                    <div class="text-right">
                                        <span class="text-2xl font-bold ${match.score >= 80 ? 'text-green-600' : match.score >= 60 ? 'text-yellow-600' : 'text-red-600'}">${match.score}%</span>
                                        <p class="text-xs text-gray-500">Match Score</p>
                                    </div>
                                </div>
                                <div class="border-t pt-4">
                                    <h4 class="font-medium text-gray-900">${match.position.title}</h4>
                                    <p class="text-sm text-gray-600">${match.company.name}</p>
                                    <p class="text-sm text-gray-500">${match.position.location} • ${match.position.salary}</p>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading hiring authority matches:', error);
    }
}

// Placeholder functions for other features
function loadPositions() {
    document.getElementById('content-area').innerHTML = `
        <div class="p-6">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">💼 Positions</h2>
            <p class="text-gray-600">Position listings coming soon...</p>
        </div>
    `;
}

async function loadSkills() {
    try {
        // Show loading state
        document.getElementById('content-area').innerHTML = `
            <div class="p-6">
                <h2 class="text-2xl font-bold text-gray-900 mb-4">⚡ Loading Skills Analysis...</h2>
                <div class="animate-pulse">
                    <div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                    <div class="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
            </div>
        `;

        const [skillsResponse, jobSeekersResponse, positionsResponse] = await Promise.all([
            fetch('/api/skills'),
            fetch('/api/job-seekers'),
            fetch('/api/positions')
        ]);

        const [skillsData, jobSeekersData, positionsData] = await Promise.all([
            skillsResponse.json(),
            jobSeekersResponse.json(),
            positionsResponse.json()
        ]);

        if (skillsData.success && jobSeekersData.success && positionsData.success) {
            const skills = skillsData.data;
            const jobSeekers = jobSeekersData.data;
            const positions = positionsData.data;

            // Analyze skills demand and availability
            const skillsAnalysis = skills.map(skill => {
                // Mock analysis - in real implementation, this would query the graph database
                const demand = Math.floor(Math.random() * 15) + 1; // Random demand 1-15
                const availability = Math.floor(Math.random() * 20) + 1; // Random availability 1-20
                const demandLevel = demand > 10 ? 'High' : demand > 5 ? 'Medium' : 'Low';
                const availabilityLevel = availability > 15 ? 'High' : availability > 8 ? 'Medium' : 'Low';
                const marketGap = demand - availability;
                const gapStatus = marketGap > 5 ? 'Critical Shortage' : marketGap > 0 ? 'Shortage' : marketGap < -5 ? 'Oversupply' : 'Balanced';

                return {
                    ...skill,
                    demand,
                    availability,
                    demandLevel,
                    availabilityLevel,
                    marketGap,
                    gapStatus
                };
            });

            // Sort by market gap (highest shortage first)
            skillsAnalysis.sort((a, b) => b.marketGap - a.marketGap);

            document.getElementById('content-area').innerHTML = `
                <div class="p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">⚡ Skills Demand & Availability Analysis</h2>

                    <!-- Summary Statistics -->
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
                        <div class="bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg p-4 text-white">
                            <h3 class="text-lg font-semibold mb-2">Total Skills</h3>
                            <p class="text-2xl font-bold">${skills.length}</p>
                            <p class="text-sm opacity-90">In our database</p>
                        </div>
                        <div class="bg-gradient-to-r from-red-500 to-red-600 rounded-lg p-4 text-white">
                            <h3 class="text-lg font-semibold mb-2">Critical Shortages</h3>
                            <p class="text-2xl font-bold">${skillsAnalysis.filter(s => s.gapStatus === 'Critical Shortage').length}</p>
                            <p class="text-sm opacity-90">High demand, low supply</p>
                        </div>
                        <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white">
                            <h3 class="text-lg font-semibold mb-2">Balanced Markets</h3>
                            <p class="text-2xl font-bold">${skillsAnalysis.filter(s => s.gapStatus === 'Balanced').length}</p>
                            <p class="text-sm opacity-90">Supply meets demand</p>
                        </div>
                        <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
                            <h3 class="text-lg font-semibold mb-2">High Availability</h3>
                            <p class="text-2xl font-bold">${skillsAnalysis.filter(s => s.availabilityLevel === 'High').length}</p>
                            <p class="text-sm opacity-90">Abundant talent pool</p>
                        </div>
                    </div>

                    <!-- Skills Analysis Table -->
                    <div class="bg-white rounded-lg shadow overflow-hidden">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">Skills Market Analysis</h3>
                            <p class="text-sm text-gray-600">Demand vs. availability analysis for all skills</p>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Skill</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Demand</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Availability</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Market Gap</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    ${skillsAnalysis.map(skill => `
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="font-medium text-gray-900">${skill.name}</div>
                                                <div class="text-sm text-gray-500">${skill.description || 'No description'}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                    ${skill.category || 'General'}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <div class="text-sm font-medium text-gray-900">${skill.demand}</div>
                                                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                                                        skill.demandLevel === 'High' ? 'bg-red-100 text-red-800' :
                                                        skill.demandLevel === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                                                        'bg-green-100 text-green-800'
                                                    }">
                                                        ${skill.demandLevel}
                                                    </span>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <div class="text-sm font-medium text-gray-900">${skill.availability}</div>
                                                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                                                        skill.availabilityLevel === 'High' ? 'bg-green-100 text-green-800' :
                                                        skill.availabilityLevel === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                                                        'bg-red-100 text-red-800'
                                                    }">
                                                        ${skill.availabilityLevel}
                                                    </span>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium ${skill.marketGap > 0 ? 'text-red-600' : skill.marketGap < 0 ? 'text-blue-600' : 'text-green-600'}">
                                                    ${skill.marketGap > 0 ? '+' : ''}${skill.marketGap}
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                    skill.gapStatus === 'Critical Shortage' ? 'bg-red-100 text-red-800' :
                                                    skill.gapStatus === 'Shortage' ? 'bg-orange-100 text-orange-800' :
                                                    skill.gapStatus === 'Balanced' ? 'bg-green-100 text-green-800' :
                                                    'bg-blue-100 text-blue-800'
                                                }">
                                                    ${skill.gapStatus}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                                <button onclick="loadGraphVisualization('${skill._id}', 'skill')" class="text-amber-600 hover:text-amber-900">
                                                    🌐 Graph
                                                </button>
                                                <button onclick="loadSkillDetails('${skill._id}')" class="text-blue-600 hover:text-blue-900">
                                                    📊 Details
                                                </button>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Market Insights -->
                    <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-red-900 mb-4">🚨 Critical Skill Shortages</h3>
                            <div class="space-y-2">
                                ${skillsAnalysis.filter(s => s.gapStatus === 'Critical Shortage').slice(0, 5).map(skill => `
                                    <div class="flex justify-between items-center">
                                        <span class="text-red-800">${skill.name}</span>
                                        <span class="text-red-600 font-medium">Gap: +${skill.marketGap}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>

                        <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-green-900 mb-4">✅ High Availability Skills</h3>
                            <div class="space-y-2">
                                ${skillsAnalysis.filter(s => s.availabilityLevel === 'High').slice(0, 5).map(skill => `
                                    <div class="flex justify-between items-center">
                                        <span class="text-green-800">${skill.name}</span>
                                        <span class="text-green-600 font-medium">${skill.availability} available</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading skills analysis:', error);
        document.getElementById('content-area').innerHTML = `
            <div class="p-6">
                <h2 class="text-2xl font-bold text-red-600 mb-4">❌ Error Loading Skills Analysis</h2>
                <p class="text-gray-600">There was an error loading the skills analysis. Please try again.</p>
            </div>
        `;
    }
}

async function loadMatches() {
    document.getElementById('content-area').innerHTML = `
        <div class="p-6">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">🎯 Loading Job Matches...</h2>
            <div class="animate-pulse">
                <div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                <div class="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
        </div>
    `;

    try {
        const response = await fetch('/api/matches');
        const data = await response.json();

        if (data.success) {
            const matches = data.data;
            document.getElementById('content-area').innerHTML = `
                <div class="p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">🎯 Job Matches (${matches.length})</h2>
                    <div class="space-y-4">
                        ${matches.map(match => `
                            <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                                <div class="flex justify-between items-start mb-4">
                                    <div>
                                        <h3 class="font-semibold text-gray-900">${match.jobSeeker.name}</h3>
                                        <p class="text-sm text-gray-600">${match.jobSeeker.title}</p>
                                        <p class="text-sm text-gray-500">${match.jobSeeker.location}</p>
                                    </div>
                                    <div class="text-right">
                                        <span class="text-2xl font-bold ${match.score >= 80 ? 'text-green-600' : match.score >= 60 ? 'text-yellow-600' : 'text-red-600'}">${match.score}%</span>
                                        <p class="text-xs text-gray-500">Match Score</p>
                                    </div>
                                </div>
                                <div class="border-t pt-4">
                                    <h4 class="font-medium text-gray-900">${match.position.title}</h4>
                                    <p class="text-sm text-gray-600">${match.company.name}</p>
                                    <p class="text-sm text-gray-500">${match.position.location} • ${match.position.salary}</p>
                                </div>
                                <div class="mt-4 pt-4 border-t">
                                    <p class="text-sm text-gray-600">
                                        <span class="font-medium">Contact:</span> ${match.hiringAuthority ? match.hiringAuthority.name : 'N/A'}
                                        ${match.hiringAuthority ? '(' + match.hiringAuthority.title + ')' : ''}
                                    </p>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading matches:', error);
        document.getElementById('content-area').innerHTML = `
            <div class="p-6">
                <h2 class="text-2xl font-bold text-red-600 mb-4">❌ Error Loading Matches</h2>
                <p class="text-gray-600">There was an error loading the job matches. Please try again.</p>
            </div>
        `;
    }
}

// Load graph visualization
async function loadGraphVisualization(focus = null, mode = null) {
    // Handle the mode parameter for direct 2D/3D calls
    if (mode === '2d') {
        is3DMode = false;
    } else if (mode === '3d') {
        is3DMode = true;
    }
    document.getElementById('content-area').innerHTML = `
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-900">🌐 Graph Database Visualization</h2>
                <div class="flex space-x-2">
                    <button onclick="loadGraphVisualization(null, 'overview')" class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded hover:bg-blue-200">
                        All Entities
                    </button>
                    <button onclick="toggleVisualizationMode()" id="mode-toggle" class="px-3 py-1 text-sm bg-purple-100 text-purple-800 rounded hover:bg-purple-200">
                        Switch to 3D
                    </button>
                    <button onclick="toggleEditMode()" id="edit-toggle" class="px-3 py-1 text-sm bg-green-100 text-green-800 rounded hover:bg-green-200">
                        Enable Editing
                    </button>
                </div>
            </div>

            <!-- Global Filtering Controls -->
            <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                <h3 class="font-semibold mb-3">Global Filters & Views:</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-2">
                    <button onclick="loadGraphVisualization('jobSeekers')" class="px-3 py-2 bg-blue-100 text-blue-800 rounded text-sm hover:bg-blue-200">
                        👤 Job Seekers
                    </button>
                    <button onclick="loadGraphVisualization('companies')" class="px-3 py-2 bg-teal-100 text-teal-800 rounded text-sm hover:bg-teal-200">
                        🏢 Companies
                    </button>
                    <button onclick="loadGraphVisualization('hiringAuthorities')" class="px-3 py-2 bg-orange-100 text-orange-800 rounded text-sm hover:bg-orange-200">
                        👔 Authorities
                    </button>
                    <button onclick="loadGraphVisualization('positions')" class="px-3 py-2 bg-violet-100 text-violet-800 rounded text-sm hover:bg-violet-200">
                        💼 Positions
                    </button>
                    <button onclick="loadGraphVisualization('skills')" class="px-3 py-2 bg-amber-100 text-amber-800 rounded text-sm hover:bg-amber-200">
                        ⚡ Skills
                    </button>
                    <button onclick="loadGraphVisualization('matches')" class="px-3 py-2 bg-emerald-100 text-emerald-800 rounded text-sm hover:bg-emerald-200">
                        🎯 Matches
                    </button>
                    <button onclick="loadGraphVisualization('connections')" class="px-3 py-2 bg-indigo-100 text-indigo-800 rounded text-sm hover:bg-indigo-200">
                        🔗 Connections
                    </button>
                    <button onclick="loadGraphVisualization('overview')" class="px-3 py-2 bg-gray-100 text-gray-800 rounded text-sm hover:bg-gray-200">
                        🌐 All Data
                    </button>
                </div>
            </div>

            <!-- Legend -->
            <div class="mb-4 p-4 bg-gray-50 rounded-lg">
                <h3 class="font-semibold mb-3">Legend:</h3>
                <div class="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-blue-500 rounded-full mr-2"></div>
                        <span>Job Seekers</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-green-500 rounded-full mr-2"></div>
                        <span>Companies</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-orange-500 rounded-full mr-2"></div>
                        <span>Hiring Authorities</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-purple-500 rounded-full mr-2"></div>
                        <span>Positions</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-red-500 rounded-full mr-2"></div>
                        <span>Skills</span>
                    </div>
                </div>
            </div>

            <!-- Graph Container -->
            <div class="relative rounded-lg overflow-hidden" style="height: 700px; margin: 7% auto; width: 86%;">
                <div id="graph-container" style="width: 100%; height: 100%; background: transparent;"></div>

                <!-- Minimized HUD Controls -->
                <div id="graph-hud" class="absolute top-4 right-4 bg-black bg-opacity-10 backdrop-blur-sm rounded-lg transition-all duration-300">
                    <!-- Minimized State -->
                    <div id="hud-minimized" class="p-2">
                        <button onclick="expandHUD()" class="text-gray-600 hover:text-gray-800 text-sm">⚙️</button>
                    </div>

                    <!-- Expanded State -->
                    <div id="hud-expanded" class="p-3 space-y-2 text-xs" style="display: none;">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-gray-700 font-medium">Controls</span>
                            <button onclick="minimizeHUD()" class="text-gray-500 hover:text-gray-700">×</button>
                        </div>

                        <!-- Essential Controls Only -->
                        <div class="flex space-x-1">
                            <button onclick="resetGraphView()" class="px-2 py-1 bg-white bg-opacity-60 text-gray-700 rounded text-xs hover:bg-opacity-80 transition-all" title="Reset view to default position">
                                🎯
                            </button>
                            <button onclick="toggleVisualizationMode()" id="hud-mode-toggle" class="px-2 py-1 bg-white bg-opacity-60 text-gray-700 rounded text-xs hover:bg-opacity-80 transition-all" title="Switch between 2D and 3D view">
                                ${is3DMode ? '📐' : '🌐'}
                            </button>
                            <button onclick="fitToView()" class="px-2 py-1 bg-white bg-opacity-60 text-gray-700 rounded text-xs hover:bg-opacity-80 transition-all" title="Fit all content to view">
                                🔍
                            </button>
                        </div>

                        <!-- Info Display -->
                        <div class="text-gray-600 text-xs border-t border-gray-300 pt-2">
                            <div id="hud-zoom-level">Zoom: 100%</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Node Details Panel -->
            <div id="node-details" class="mt-4 p-4 bg-blue-50 rounded-lg" style="display: none;">
                <h3 class="font-semibold text-blue-900 mb-2">Node Details</h3>
                <div id="node-details-content"></div>
            </div>
        </div>

        <!-- CRUD Modal -->
        <div id="crud-modal" class="fixed inset-0 modal-overlay z-50" style="display: none;">
            <div class="flex items-center justify-center min-h-screen p-4">
                <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 id="modal-title" class="text-lg font-semibold text-gray-900">Edit Node</h3>
                            <button onclick="closeCrudModal()" class="text-gray-400 hover:text-gray-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        <form id="crud-form">
                            <div id="form-fields" class="space-y-4">
                                <!-- Dynamic form fields will be inserted here -->
                            </div>
                            <div class="flex justify-end space-x-3 mt-6">
                                <button type="button" onclick="closeCrudModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">
                                    Cancel
                                </button>
                                <button type="button" onclick="deleteNode()" id="delete-btn" class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700">
                                    Delete
                                </button>
                                <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                                    Save Changes
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Load graph data and render
    await loadAndRenderGraph(focus, focus || 'overview');
}

async function loadAndRenderGraph(focus = null, type = 'overview') {
    try {
        const url = focus ? `/api/graph?focus=${focus}&type=${type}` : '/api/graph?type=overview';
        const response = await fetch(url);
        const data = await response.json();

        if (data.success) {
            renderGraph(data.data, focus);
        } else {
            console.error('Failed to load graph data:', data.error);
        }
    } catch (error) {
        console.error('Error loading graph:', error);
    }
}

function renderGraph(graphData, focusType = null) {
    currentGraphData = graphData;
    const container = document.getElementById('graph-container');
    container.innerHTML = ''; // Clear previous graph

    // Update HUD info
    updateHUDInfo(graphData);

    // Establish visual hierarchy based on focus
    const enhancedGraphData = establishVisualHierarchy(graphData, focusType);

    if (is3DMode) {
        render3DGraph(enhancedGraphData, container, focusType);
    } else {
        render2DGraph(enhancedGraphData, container, focusType);
    }
}

// Establish visual hierarchy with focus node taking precedence
function establishVisualHierarchy(graphData, focusType) {
    const enhanced = JSON.parse(JSON.stringify(graphData)); // Deep clone

    // Define hierarchy levels based on focus
    const getHierarchyLevel = (node, focus) => {
        if (!focus || focus === 'overview') {
            // Default hierarchy: companies -> authorities -> positions -> seekers -> skills
            const hierarchy = {
                'company': 1,
                'hiringAuthority': 2,
                'position': 3,
                'jobSeeker': 4,
                'skill': 5
            };
            return hierarchy[node.type] || 3;
        }

        // Focus-based hierarchy
        if (node.type === focus || (focus === 'matches' && node.type === 'jobSeeker')) {
            return 1; // Origin nodes
        } else if (focus === 'companies' && node.type === 'hiringAuthority') {
            return 2; // Direct connections
        } else if (focus === 'hiringAuthorities' && node.type === 'position') {
            return 2;
        } else if (focus === 'positions' && node.type === 'skill') {
            return 2;
        }

        return 3; // Secondary nodes
    };

    // Enhance nodes with hierarchy and visual properties
    enhanced.nodes = enhanced.nodes.map(node => ({
        ...node,
        hierarchyLevel: getHierarchyLevel(node, focusType),
        isOrigin: getHierarchyLevel(node, focusType) === 1,
        size: getNodeSize(node.type, getHierarchyLevel(node, focusType)),
        fontSize: getFontSize(node.type, 1, getHierarchyLevel(node, focusType)),
        fontWeight: getFontWeight(node.type, getHierarchyLevel(node, focusType))
    }));

    return enhanced;
}

// Enhanced node sizing based on hierarchy
function getNodeSize(nodeType, hierarchyLevel) {
    const baseSizes = {
        'company': 12,
        'hiringAuthority': 10,
        'position': 8,
        'jobSeeker': 8,
        'skill': 6
    };

    const baseSize = baseSizes[nodeType] || 8;
    const hierarchyMultiplier = hierarchyLevel === 1 ? 1.5 : hierarchyLevel === 2 ? 1.2 : 1;

    return baseSize * hierarchyMultiplier;
}

// Update HUD information display
function updateHUDInfo(graphData) {
    const nodeCount = graphData.nodes ? graphData.nodes.length : 0;
    const edgeCount = graphData.links ? graphData.links.length : 0;

    const nodeCountEl = document.getElementById('hud-node-count');
    const edgeCountEl = document.getElementById('hud-edge-count');

    if (nodeCountEl) nodeCountEl.textContent = `Nodes: ${nodeCount}`;
    if (edgeCountEl) edgeCountEl.textContent = `Edges: ${edgeCount}`;
}

function render2DGraph(graphData, container) {
    // Create SVG
    const width = container.clientWidth;
    const height = container.clientHeight;

    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', width);
    svg.setAttribute('height', height);
    svg.style.background = 'transparent';
    svg.setAttribute('viewBox', `0 0 ${width} ${height}`);

    // Create main group for pan/zoom transformations
    const mainGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    svg.appendChild(mainGroup);
    container.appendChild(svg);

    // Enhanced font sizing system with hierarchy support
    const getFontSize = (nodeType, zoomLevel = 1, hierarchyLevel = 3) => {
        const baseSize = getNodeImportance(nodeType);
        const hierarchyBoost = hierarchyLevel === 1 ? 1.4 : hierarchyLevel === 2 ? 1.2 : 1;
        const scaledSize = baseSize * zoomLevel * hierarchyBoost;
        return Math.max(10, Math.min(22, scaledSize));
    };

    const getFontWeight = (nodeType, hierarchyLevel = 3) => {
        const baseWeights = {
            'company': 900,      // Extra bold
            'hiringAuthority': 700, // Bold
            'position': 600,     // Semi-bold
            'jobSeeker': 500,    // Medium
            'skill': 400         // Normal
        };

        const baseWeight = baseWeights[nodeType] || 500;

        // Origin nodes get extra weight boost
        if (hierarchyLevel === 1) {
            return Math.min(900, baseWeight + 200);
        }

        return baseWeight;
    };

    const getNodeImportance = (nodeType) => {
        const importance = {
            'company': 22,       // Largest - origin of visualization
            'hiringAuthority': 18,
            'position': 16,
            'jobSeeker': 14,
            'skill': 10          // Smallest - most distant
        };
        return importance[nodeType] || 14;
    };

    // Enhanced pan and zoom functionality
    let isPanning = false;
    let isDragging = false;
    let draggedNode = null;
    let panStart = { x: 0, y: 0 };
    let currentTransform = { x: 0, y: 0, scale: 1 };

    // Enhanced mouse wheel zoom with governors and content bounds detection
    svg.addEventListener('wheel', (e) => {
        // Check if cursor is within visualization content bounds
        const rect = svg.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;

        // Calculate content bounding box
        const contentBounds = getVisualizationContentBounds(svg);
        const isWithinContent = mouseX >= contentBounds.left &&
                               mouseX <= contentBounds.right &&
                               mouseY >= contentBounds.top &&
                               mouseY <= contentBounds.bottom;

        // If outside content bounds, defer to browser scrolling
        if (!isWithinContent) {
            return; // Let browser handle scrolling
        }

        e.preventDefault();

        // Smooth zoom with easing and governors
        const zoomFactor = e.deltaY > 0 ? 0.92 : 1.08;

        // Apply zoom governors: min 10% (container bounds), max 2000%
        const minScale = Math.max(0.1, Math.min(container.clientWidth, container.clientHeight) / 1000);
        const maxScale = 20; // 2000%
        const newScale = Math.max(minScale, Math.min(maxScale, currentTransform.scale * zoomFactor));

        // Only proceed if scale actually changes
        if (newScale === currentTransform.scale) return;

        // Zoom towards cursor position with easing
        const dx = mouseX - currentTransform.x;
        const dy = mouseY - currentTransform.y;

        currentTransform.x = mouseX - dx * (newScale / currentTransform.scale);
        currentTransform.y = mouseY - dy * (newScale / currentTransform.scale);
        currentTransform.scale = newScale;

        updateTransform();
        updateHUDZoom();
    });

    // Helper function to calculate visualization content bounds
    const getVisualizationContentBounds = (svg) => {
        const nodes = svg.querySelectorAll('g[data-node]');
        if (nodes.length === 0) {
            // Default to container bounds if no nodes
            return {
                left: 0,
                top: 0,
                right: container.clientWidth,
                bottom: container.clientHeight
            };
        }

        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

        nodes.forEach(node => {
            const transform = node.getAttribute('transform');
            const match = transform.match(/translate\(([^,]+),\s*([^)]+)\)/);
            if (match) {
                const x = parseFloat(match[1]);
                const y = parseFloat(match[2]);
                minX = Math.min(minX, x - 50); // Add padding
                minY = Math.min(minY, y - 50);
                maxX = Math.max(maxX, x + 50);
                maxY = Math.max(maxY, y + 50);
            }
        });

        return {
            left: Math.max(0, minX * currentTransform.scale + currentTransform.x),
            top: Math.max(0, minY * currentTransform.scale + currentTransform.y),
            right: Math.min(container.clientWidth, maxX * currentTransform.scale + currentTransform.x),
            bottom: Math.min(container.clientHeight, maxY * currentTransform.scale + currentTransform.y)
        };
    };

    const updateHUDZoom = () => {
        const zoomEl = document.getElementById('hud-zoom-level');
        if (zoomEl) {
            zoomEl.textContent = `Zoom: ${Math.round(currentTransform.scale * 100)}%`;
        }
    };

    // Enhanced mouse interaction with node dragging
    svg.addEventListener('mousedown', (e) => {
        const nodeElement = e.target.closest('g[data-node]');

        if (e.button === 0) { // Left click
            if (nodeElement) {
                // Start node dragging
                isDragging = true;
                draggedNode = nodeElement;
                svg.style.cursor = 'grabbing';
                e.preventDefault();
            } else {
                // Start panning
                isPanning = true;
                panStart.x = e.clientX - currentTransform.x;
                panStart.y = e.clientY - currentTransform.y;
                svg.style.cursor = 'grabbing';
            }
        }
    });

    svg.addEventListener('mousemove', (e) => {
        if (isDragging && draggedNode) {
            // Drag node
            const rect = svg.getBoundingClientRect();
            const x = (e.clientX - rect.left - currentTransform.x) / currentTransform.scale;
            const y = (e.clientY - rect.top - currentTransform.y) / currentTransform.scale;

            draggedNode.setAttribute('transform', `translate(${x}, ${y})`);

            // Update connected edges if needed
            updateNodeEdges(draggedNode, x, y);

        } else if (isPanning) {
            // Pan view
            currentTransform.x = e.clientX - panStart.x;
            currentTransform.y = e.clientY - panStart.y;
            updateTransform();
        }
    });

    svg.addEventListener('mouseup', () => {
        isPanning = false;
        isDragging = false;
        draggedNode = null;
        svg.style.cursor = 'default';
    });

    svg.addEventListener('mouseleave', () => {
        isPanning = false;
        isDragging = false;
        draggedNode = null;
        svg.style.cursor = 'default';
    });

    // Helper function to update node edges when dragging
    const updateNodeEdges = (nodeElement, x, y) => {
        const nodeId = nodeElement.getAttribute('data-node-id');
        const edges = svg.querySelectorAll(`line[data-source="${nodeId}"], line[data-target="${nodeId}"]`);

        edges.forEach(edge => {
            if (edge.getAttribute('data-source') === nodeId) {
                edge.setAttribute('x1', x);
                edge.setAttribute('y1', y);
            }
            if (edge.getAttribute('data-target') === nodeId) {
                edge.setAttribute('x2', x);
                edge.setAttribute('y2', y);
            }
        });
    };

    function updateTransform() {
        mainGroup.setAttribute('transform',
            `translate(${currentTransform.x}, ${currentTransform.y}) scale(${currentTransform.scale})`
        );
    }

    // Simple force simulation using basic positioning
    const nodes = graphData.nodes.map(d => ({...d}));
    const links = graphData.links.map(d => ({...d}));

    // Position nodes in a circle
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) / 3;

    if (nodes.length === 1) {
        nodes[0].x = centerX;
        nodes[0].y = centerY;
    } else {
        nodes.forEach((node, i) => {
            const angle = (i / nodes.length) * 2 * Math.PI;
            node.x = centerX + Math.cos(angle) * radius;
            node.y = centerY + Math.sin(angle) * radius;
        });
    }

    // Draw links
    links.forEach((link, index) => {
        const sourceNode = nodes.find(n => n.id === link.source);
        const targetNode = nodes.find(n => n.id === link.target);

        if (sourceNode && targetNode) {
            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            line.setAttribute('x1', sourceNode.x);
            line.setAttribute('y1', sourceNode.y);
            line.setAttribute('x2', targetNode.x);
            line.setAttribute('y2', targetNode.y);
            line.setAttribute('stroke', link.color || '#94A3B8');
            line.setAttribute('stroke-width', '2');
            line.setAttribute('opacity', '0.6');
            line.setAttribute('data-link', `${link.source}-${link.target}`);
            mainGroup.appendChild(line);

            // Add link label
            if (link.label) {
                const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                text.setAttribute('x', (sourceNode.x + targetNode.x) / 2);
                text.setAttribute('y', (sourceNode.y + targetNode.y) / 2);
                text.setAttribute('text-anchor', 'middle');
                text.setAttribute('font-size', '10');
                text.setAttribute('fill', '#6B7280');
                text.textContent = link.label;
                mainGroup.appendChild(text);
            }
        }
    });

    // Draw nodes
    nodes.forEach(node => {
        const group = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        group.style.cursor = 'pointer';
        group.setAttribute('data-node', node.id);

        const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        circle.setAttribute('cx', node.x);
        circle.setAttribute('cy', node.y);
        circle.setAttribute('r', node.size || 8);
        circle.setAttribute('fill', node.color);
        circle.setAttribute('stroke', '#fff');
        circle.setAttribute('stroke-width', '2');

        // Enhanced text label with transparent background and font weights
        const fontSize = getFontSize(node.type, currentTransform.scale);
        const fontWeight = getFontWeight(node.type);

        // Background rectangle for better readability (transparent)
        const labelBg = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        const textWidth = (node.name.length > 15 ? node.name.substring(0, 15) + '...' : node.name).length * fontSize * 0.6;

        labelBg.setAttribute('x', node.x - textWidth / 2);
        labelBg.setAttribute('y', node.y + (node.size || 8) + 10);
        labelBg.setAttribute('width', textWidth);
        labelBg.setAttribute('height', fontSize + 4);
        labelBg.setAttribute('fill', 'rgba(255, 255, 255, 0.1)');
        labelBg.setAttribute('rx', '2');
        labelBg.style.pointerEvents = 'none';

        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.setAttribute('x', node.x);
        text.setAttribute('y', node.y + (node.size || 8) + 15 + fontSize / 2);
        text.setAttribute('text-anchor', 'middle');
        text.setAttribute('font-size', fontSize);
        text.setAttribute('font-weight', fontWeight);
        text.setAttribute('font-family', 'Inter, Arial, sans-serif');
        text.setAttribute('fill', '#000000'); // Black text as requested
        text.textContent = node.name.length > 15 ? node.name.substring(0, 15) + '...' : node.name;
        text.style.pointerEvents = 'none';

        group.appendChild(circle);
        group.appendChild(labelBg);
        group.appendChild(text);

        // Add click handler
        group.addEventListener('click', () => {
            if (isEditMode) {
                openCrudModal(node);
            } else {
                showNodeDetails(node);
            }
        });

        mainGroup.appendChild(group);
    });
}

function render3DGraph(graphData, container) {
    // Enhanced 3D force graph with improved font sizing and transparent background
    const Graph = ForceGraph3D()(container)
        .graphData(graphData)
        .width(container.clientWidth)
        .height(container.clientHeight)
        .backgroundColor('rgba(0,0,0,0)') // Transparent background
        .nodeLabel('name')
        .nodeColor(node => node.color)
        .nodeVal(node => node.size)
        .nodeThreeObject(node => {
            // Create a group for node and label
            const group = new THREE.Group();

            // Enhanced sphere for node with better sizing
            const nodeSize = getNodeImportance(node.type) / 2; // Scale down for 3D
            const sphereGeometry = new THREE.SphereGeometry(nodeSize);
            const sphereMaterial = new THREE.MeshLambertMaterial({
                color: node.color,
                transparent: true,
                opacity: 0.9
            });
            const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
            group.add(sphere);

            // Enhanced text label with font weight and transparent background
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            canvas.width = 512;
            canvas.height = 128;

            // Clear canvas with transparent background
            context.clearRect(0, 0, canvas.width, canvas.height);

            // Set font with weight hierarchy
            const fontSize = getFontSize(node.type, 1) * 2; // Scale up for 3D visibility
            const fontWeight = getFontWeight(node.type);
            context.font = `${fontWeight} ${fontSize}px Inter, Arial, sans-serif`;
            context.fillStyle = '#000000'; // Black text as requested
            context.textAlign = 'center';
            context.textBaseline = 'middle';

            // Add subtle transparent background for readability
            const text = node.name.length > 15 ? node.name.substring(0, 15) + '...' : node.name;
            const textMetrics = context.measureText(text);
            const textWidth = textMetrics.width;
            const textHeight = fontSize;

            // Transparent background rectangle
            context.fillStyle = 'rgba(255, 255, 255, 0.1)';
            context.fillRect(
                (canvas.width - textWidth) / 2 - 4,
                (canvas.height - textHeight) / 2 - 2,
                textWidth + 8,
                textHeight + 4
            );

            // Draw text
            context.fillStyle = '#000000';
            context.fillText(text, canvas.width / 2, canvas.height / 2);

            const texture = new THREE.CanvasTexture(canvas);
            texture.transparent = true;
            const spriteMaterial = new THREE.SpriteMaterial({
                map: texture,
                transparent: true,
                alphaTest: 0.1
            });
            const sprite = new THREE.Sprite(spriteMaterial);

            // Scale sprite based on node importance and distance
            const spriteScale = Math.max(20, Math.min(60, nodeSize * 4));
            sprite.scale.set(spriteScale, spriteScale / 4, 1);
            sprite.position.set(0, nodeSize + 15, 0);
            group.add(sprite);

            return group;
        })
        .linkLabel('label')
        .linkColor(link => link.color)
        .linkWidth(2)
        .linkOpacity(0.6)
        .onNodeClick((node, event) => {
            // Animate camera to optimal position for selected node
            animateCameraToNode(node, Graph);

            if (isEditMode) {
                openCrudModal(node);
            } else {
                showNodeDetails(node);
            }
        })
        .enableNodeDrag(true) // Always enable node dragging
        .showNavInfo(false)
        .d3Force('charge', d3.forceCollide().radius(50)) // Expand space between nodes
        .onEngineStop(() => {
            // Ensure graph fits within container with expanded space
            Graph.zoomToFit(600, 200);
        });

    currentGraph = Graph;

    // Enhanced controls with smooth zoom-to-cursor and improved pan functionality
    const controls = Graph.controls();
    controls.enableDamping = true;
    controls.dampingFactor = 0.05; // Smoother damping
    controls.enablePan = true;
    controls.enableZoom = true;
    controls.enableRotate = true;
    controls.autoRotate = false;

    // Enhanced zoom-to-cursor functionality with easing
    const renderer = Graph.renderer();
    const camera = Graph.camera();

    container.addEventListener('wheel', (event) => {
        // Check if cursor is within 3D visualization content bounds
        const rect = container.getBoundingClientRect();
        const mouseX = event.clientX - rect.left;
        const mouseY = event.clientY - rect.top;

        // Simple bounds check for 3D container
        const isWithinContent = mouseX >= 0 && mouseX <= rect.width &&
                               mouseY >= 0 && mouseY <= rect.height;

        // If outside content bounds, defer to browser scrolling
        if (!isWithinContent) {
            return; // Let browser handle scrolling
        }

        event.preventDefault();

        const mouse = new THREE.Vector2();
        mouse.x = (mouseX / rect.width) * 2 - 1;
        mouse.y = -(mouseY / rect.height) * 2 + 1;

        const raycaster = new THREE.Raycaster();
        raycaster.setFromCamera(mouse, camera);

        // Get current camera distance for zoom governors
        const currentDistance = camera.position.length();

        // Apply zoom governors: min distance 50 (max zoom ~2000%), max distance 2000 (min zoom ~5%)
        const minDistance = 50;   // Maximum zoom in
        const maxDistance = 2000; // Maximum zoom out

        // Smooth zoom with easing - scroll up zooms into deepest 3D coordinate
        const zoomFactor = event.deltaY > 0 ? 1.08 : 0.92;
        const direction = new THREE.Vector3();
        camera.getWorldDirection(direction);

        // Calculate new distance and apply governors
        const zoomDistance = (zoomFactor - 1) * 30;
        const newDistance = currentDistance + zoomDistance;

        // Only proceed if within zoom bounds
        if (newDistance < minDistance || newDistance > maxDistance) {
            return; // Hit zoom limit
        }

        // Enhanced zoom towards cursor with depth consideration
        camera.position.addScaledVector(direction, zoomDistance);

        // Update HUD zoom level with proper calculation
        const distance = camera.position.length();
        const zoomLevel = Math.max(5, Math.min(2000, 10000 / distance));
        const zoomEl = document.getElementById('hud-zoom-level');
        if (zoomEl) {
            zoomEl.textContent = `Zoom: ${Math.round(zoomLevel)}%`;
        }

        controls.update();
    });
}

function animateCameraToNode(node, graph) {
    const camera = graph.camera();
    const controls = graph.controls();

    // Calculate optimal camera position
    const nodePosition = new THREE.Vector3(node.x || 0, node.y || 0, node.z || 0);
    const distance = 200; // Optimal viewing distance
    const angle = Math.PI / 4; // 45-degree angle for best view

    // Calculate camera target position
    const targetPosition = new THREE.Vector3(
        nodePosition.x + distance * Math.cos(angle),
        nodePosition.y + distance * Math.sin(angle),
        nodePosition.z + distance
    );

    // Animate camera movement with ease-out
    const startPosition = camera.position.clone();
    const startTarget = controls.target.clone();
    const duration = 1500; // 1.5 seconds
    const startTime = Date.now();

    function animate() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Ease-out cubic function
        const easeOut = 1 - Math.pow(1 - progress, 3);

        // Interpolate camera position
        camera.position.lerpVectors(startPosition, targetPosition, easeOut);
        controls.target.lerpVectors(startTarget, nodePosition, easeOut);

        controls.update();

        if (progress < 1) {
            requestAnimationFrame(animate);
        }
    }

    animate();
}

function toggleVisualizationMode() {
    is3DMode = !is3DMode;
    const toggleBtn = document.getElementById('mode-toggle');
    const hudToggleBtn = document.getElementById('hud-mode-toggle');

    const newText = is3DMode ? 'Switch to 2D' : 'Switch to 3D';
    const hudText = is3DMode ? '📐 Switch to 2D' : '🌐 Switch to 3D';

    if (toggleBtn) toggleBtn.textContent = newText;
    if (hudToggleBtn) hudToggleBtn.textContent = hudText;

    // Show/hide 3D specific controls
    const hud3DControls = document.getElementById('hud-3d-controls');
    if (hud3DControls) {
        hud3DControls.style.display = is3DMode ? 'block' : 'none';
    }

    if (currentGraphData) {
        renderGraph(currentGraphData);
    }
}

function toggleEditMode() {
    isEditMode = !isEditMode;
    const editBtn = document.getElementById('edit-toggle');
    const hudEditBtn = document.getElementById('hud-edit-toggle');

    const newText = isEditMode ? 'Disable Editing' : 'Enable Editing';
    const hudText = isEditMode ? '✏️ Disable Edit' : '✏️ Enable Edit';

    if (editBtn) {
        editBtn.textContent = newText;
        editBtn.className = isEditMode ?
            'px-3 py-1 text-sm bg-red-100 text-red-800 rounded hover:bg-red-200' :
            'px-3 py-1 text-sm bg-green-100 text-green-800 rounded hover:bg-green-200';
    }

    if (hudEditBtn) {
        hudEditBtn.textContent = hudText;
    }

    if (currentGraphData) {
        renderGraph(currentGraphData);
    }
}

function showNodeDetails(node) {
    const detailsPanel = document.getElementById('node-details');
    const detailsContent = document.getElementById('node-details-content');

    detailsContent.innerHTML = `
        <div class="space-y-2">
            <p><strong>Name:</strong> ${node.name}</p>
            <p><strong>Type:</strong> ${node.type}</p>
            <p><strong>ID:</strong> ${node.id}</p>
        </div>
    `;

    detailsPanel.style.display = 'block';
}

function openCrudModal(node) {
    selectedNode = node;
    const modal = document.getElementById('crud-modal');
    const title = document.getElementById('modal-title');
    const formFields = document.getElementById('form-fields');

    title.textContent = `Edit ${node.type}: ${node.name}`;

    // Generate form fields based on node type
    formFields.innerHTML = `
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
            <input type="text" id="node-name" value="${node.name}" class="w-full p-2 border border-gray-300 rounded-md">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Type</label>
            <input type="text" id="node-type" value="${node.type}" class="w-full p-2 border border-gray-300 rounded-md" readonly>
        </div>
    `;

    modal.style.display = 'block';
}

function closeCrudModal() {
    const modal = document.getElementById('crud-modal');
    modal.style.display = 'none';
    selectedNode = null;
}

// Enhanced HUD functions
function expandHUD() {
    const minimized = document.getElementById('hud-minimized');
    const expanded = document.getElementById('hud-expanded');
    if (minimized && expanded) {
        minimized.style.display = 'none';
        expanded.style.display = 'block';
    }
}

function minimizeHUD() {
    const minimized = document.getElementById('hud-minimized');
    const expanded = document.getElementById('hud-expanded');
    if (minimized && expanded) {
        minimized.style.display = 'block';
        expanded.style.display = 'none';
    }
}

function resetGraphView() {
    if (currentGraph) {
        if (is3DMode) {
            currentGraph.zoomToFit(600, 200);
        } else {
            // Reset 2D view
            const svg = document.querySelector('#graph-container svg');
            if (svg) {
                const mainGroup = svg.querySelector('g');
                if (mainGroup) {
                    mainGroup.setAttribute('transform', 'translate(0, 0) scale(1)');
                    currentTransform = { x: 0, y: 0, scale: 1 };
                    updateHUDZoom();
                }
            }
        }
    }
}

function centerGraph() {
    if (currentGraph) {
        if (is3DMode) {
            currentGraph.centerAt(0, 0, 0);
        } else {
            // Center 2D view
            const container = document.getElementById('graph-container');
            const svg = container.querySelector('svg');
            if (svg) {
                const mainGroup = svg.querySelector('g');
                if (mainGroup) {
                    const centerX = container.clientWidth / 2;
                    const centerY = container.clientHeight / 2;
                    mainGroup.setAttribute('transform', `translate(${centerX}, ${centerY}) scale(1)`);
                    currentTransform = { x: centerX, y: centerY, scale: 1 };
                    updateHUDZoom();
                }
            }
        }
    }
}

function fitToView() {
    if (currentGraph) {
        if (is3DMode) {
            currentGraph.zoomToFit(400, 100);
        } else {
            // Fit 2D view to show all nodes
            resetGraphView();
        }
    }
}

function expandSpace() {
    if (currentGraph && is3DMode) {
        currentGraph.d3Force('charge', d3.forceCollide().radius(80));
        currentGraph.d3ReheatSimulation();
    }
}

function contractSpace() {
    if (currentGraph && is3DMode) {
        currentGraph.d3Force('charge', d3.forceCollide().radius(30));
        currentGraph.d3ReheatSimulation();
    }
}

// Load connections overview page
async function loadConnections() {
    try {
        const [statsResponse, graphResponse] = await Promise.all([
            fetch('/api/stats'),
            fetch('/api/graph?type=overview')
        ]);

        const [statsData, graphData] = await Promise.all([
            statsResponse.json(),
            graphResponse.json()
        ]);

        if (statsData.success && graphData.success) {
            const stats = statsData.data;
            const { nodes, links } = graphData.data;

            // Analyze connections
            const connectionTypes = {
                'hasSkill': { count: 0, label: 'Job Seeker → Skills', color: 'blue' },
                'requiresSkill': { count: 0, label: 'Position → Skills', color: 'purple' },
                'manages': { count: 0, label: 'Authority → Positions', color: 'orange' },
                'employs': { count: 0, label: 'Company → Authorities', color: 'green' },
                'matches': { count: 0, label: 'Job Matches', color: 'red' }
            };

            links.forEach(link => {
                if (connectionTypes[link.type]) {
                    connectionTypes[link.type].count++;
                }
            });

            document.getElementById('content-area').innerHTML = `
                <div class="p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">🔗 Connections Overview</h2>

                    <!-- Summary Stats -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
                            <h3 class="text-lg font-semibold mb-2">Total Entities</h3>
                            <p class="text-3xl font-bold">${nodes.length}</p>
                            <p class="text-sm opacity-90">Across all categories</p>
                        </div>
                        <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
                            <h3 class="text-lg font-semibold mb-2">Total Connections</h3>
                            <p class="text-3xl font-bold">${links.length}</p>
                            <p class="text-sm opacity-90">Relationship mappings</p>
                        </div>
                        <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
                            <h3 class="text-lg font-semibold mb-2">Network Density</h3>
                            <p class="text-3xl font-bold">${Math.round((links.length / (nodes.length * (nodes.length - 1))) * 10000) / 100}%</p>
                            <p class="text-sm opacity-90">Connection ratio</p>
                        </div>
                    </div>

                    <!-- Connection Types -->
                    <div class="mb-8">
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Connection Types</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            ${Object.entries(connectionTypes).map(([type, info]) => `
                                <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                                    <div class="flex items-center justify-between mb-2">
                                        <h4 class="font-medium text-gray-900">${info.label}</h4>
                                        <span class="text-lg font-bold text-${info.color}-600">${info.count}</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-${info.color}-600 h-2 rounded-full" style="width: ${Math.min((info.count / Math.max(...Object.values(connectionTypes).map(c => c.count))) * 100, 100)}%"></div>
                                    </div>
                                    <div class="mt-2 flex space-x-2">
                                        <button onclick="loadGraphVisualization('${type}', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">
                                            2D View
                                        </button>
                                        <button onclick="loadGraphVisualization('${type}', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200">
                                            3D View
                                        </button>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <!-- Entity Categories -->
                    <div class="mb-8">
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Entity Categories</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="text-2xl">👤</div>
                                    <span class="text-xl font-bold text-blue-600">${stats.jobSeekers}</span>
                                </div>
                                <h4 class="font-semibold text-blue-900">Job Seekers</h4>
                                <p class="text-sm text-blue-700">Active candidates</p>
                                <div class="mt-3 flex space-x-1">
                                    <button onclick="loadJobSeekers()" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">View All</button>
                                    <button onclick="loadGraphVisualization('jobSeekers', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">2D</button>
                                    <button onclick="loadGraphVisualization('jobSeekers', '3d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">3D</button>
                                </div>
                            </div>

                            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="text-2xl">🏢</div>
                                    <span class="text-xl font-bold text-green-600">${stats.companies}</span>
                                </div>
                                <h4 class="font-semibold text-green-900">Companies</h4>
                                <p class="text-sm text-green-700">Hiring organizations</p>
                                <div class="mt-3 flex space-x-1">
                                    <button onclick="loadCompanies()" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">View All</button>
                                    <button onclick="loadGraphVisualization('companies', '2d')" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">2D</button>
                                    <button onclick="loadGraphVisualization('companies', '3d')" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">3D</button>
                                </div>
                            </div>

                            <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="text-2xl">👔</div>
                                    <span class="text-xl font-bold text-orange-600">${stats.hiringAuthorities}</span>
                                </div>
                                <h4 class="font-semibold text-orange-900">Hiring Authorities</h4>
                                <p class="text-sm text-orange-700">Decision makers</p>
                                <div class="mt-3 flex space-x-1">
                                    <button onclick="loadHiringAuthorities()" class="px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs hover:bg-orange-200">View All</button>
                                    <button onclick="loadGraphVisualization('hiringAuthorities', '2d')" class="px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs hover:bg-orange-200">2D</button>
                                    <button onclick="loadGraphVisualization('hiringAuthorities', '3d')" class="px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs hover:bg-orange-200">3D</button>
                                </div>
                            </div>

                            <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="text-2xl">💼</div>
                                    <span class="text-xl font-bold text-purple-600">${stats.positions}</span>
                                </div>
                                <h4 class="font-semibold text-purple-900">Positions</h4>
                                <p class="text-sm text-purple-700">Open opportunities</p>
                                <div class="mt-3 flex space-x-1">
                                    <button onclick="loadPositions()" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200">View All</button>
                                    <button onclick="loadGraphVisualization('positions', '2d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200">2D</button>
                                    <button onclick="loadGraphVisualization('positions', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200">3D</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <button onclick="loadMatches()" class="p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 text-left">
                                <div class="text-2xl mb-2">🎯</div>
                                <h4 class="font-semibold text-gray-900">View All Matches</h4>
                                <p class="text-sm text-gray-600">See intelligent job matching results</p>
                            </button>
                            <button onclick="loadSkills()" class="p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 text-left">
                                <div class="text-2xl mb-2">⚡</div>
                                <h4 class="font-semibold text-gray-900">Skills Analysis</h4>
                                <p class="text-sm text-gray-600">Analyze skills demand and availability</p>
                            </button>
                            <button onclick="loadGraphVisualization('overview', '3d')" class="p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 text-left">
                                <div class="text-2xl mb-2">🌐</div>
                                <h4 class="font-semibold text-gray-900">3D Network View</h4>
                                <p class="text-sm text-gray-600">Explore the complete network in 3D</p>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading connections:', error);
        document.getElementById('content-area').innerHTML = `
            <div class="p-6">
                <h2 class="text-2xl font-bold text-red-600 mb-4">❌ Error Loading Connections</h2>
                <p class="text-gray-600">There was an error loading the connections overview. Please try again.</p>
            </div>
        `;
    }
}
