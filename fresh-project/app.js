// Global variables for graph state
let currentGraphData = null;
let currentGraph = null;
let is3DMode = false;
let isEditMode = false;
let selectedNode = null;

// Load dashboard stats
async function loadStats() {
    try {
        const response = await fetch('/api/stats');
        const data = await response.json();

        if (data.success) {
            const stats = data.data;

            // Update navigation card counts
            document.getElementById('job-seekers-count').textContent = stats.jobSeekers;
            document.getElementById('companies-count').textContent = stats.companies;
            document.getElementById('hiring-authorities-count').textContent = stats.hiringAuthorities;
            document.getElementById('positions-count').textContent = stats.positions;
            document.getElementById('skills-count').textContent = stats.skills;
            document.getElementById('connections-count').textContent = stats.connections;

            // Calculate estimated matches (mock calculation for now)
            const estimatedMatches = Math.floor(stats.jobSeekers * stats.positions * 0.15);
            document.getElementById('matches-count').textContent = estimatedMatches;

            // Calculate total graph nodes
            const totalNodes = stats.jobSeekers + stats.companies + stats.hiringAuthorities + stats.positions + stats.skills;
            document.getElementById('graph-nodes-count').textContent = totalNodes;

            // Update stats grid
            document.getElementById('stats-grid').innerHTML = `
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="text-3xl">👤</div>
                            <div class="ml-5">
                                <dt class="text-sm font-medium text-gray-500">Job Seekers</dt>
                                <dd class="text-lg font-medium text-gray-900">${stats.jobSeekers}</dd>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="text-3xl">🏢</div>
                            <div class="ml-5">
                                <dt class="text-sm font-medium text-gray-500">Companies</dt>
                                <dd class="text-lg font-medium text-gray-900">${stats.companies}</dd>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="text-3xl">👔</div>
                            <div class="ml-5">
                                <dt class="text-sm font-medium text-gray-500">Hiring Authorities</dt>
                                <dd class="text-lg font-medium text-gray-900">${stats.hiringAuthorities}</dd>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="text-3xl">💼</div>
                            <div class="ml-5">
                                <dt class="text-sm font-medium text-gray-500">Positions</dt>
                                <dd class="text-lg font-medium text-gray-900">${stats.positions}</dd>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="text-3xl">⚡</div>
                            <div class="ml-5">
                                <dt class="text-sm font-medium text-gray-500">Skills</dt>
                                <dd class="text-lg font-medium text-gray-900">${stats.skills}</dd>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="text-3xl">🔗</div>
                            <div class="ml-5">
                                <dt class="text-sm font-medium text-gray-500">Connections</dt>
                                <dd class="text-lg font-medium text-gray-900">${stats.connections}</dd>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading stats:', error);
    }
}

// Load job seekers
async function loadJobSeekers() {
    try {
        const response = await fetch('/api/job-seekers');
        const data = await response.json();

        if (data.success) {
            const jobSeekers = data.data;
            document.getElementById('content-area').innerHTML = `
                <div class="p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">👤 Job Seekers (${jobSeekers.length})</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        ${jobSeekers.map(seeker => `
                            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                                <h3 class="font-semibold text-gray-900">${seeker.name}</h3>
                                <p class="text-sm text-gray-600">${seeker.title}</p>
                                <p class="text-sm text-gray-500">${seeker.location}</p>
                                <p class="text-xs text-gray-400 mt-2">Experience: ${seeker.experience}</p>
                                <div class="mt-3 flex space-x-2">
                                    <button onclick="loadGraphVisualization('${seeker._id}', 'jobSeeker')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">
                                        🌐 View Graph
                                    </button>
                                    <button onclick="loadMatchesForSeeker('${seeker._id}')" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">
                                        🎯 View Matches
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading job seekers:', error);
    }
}

// Load companies
async function loadCompanies() {
    try {
        const response = await fetch('/api/companies');
        const data = await response.json();

        if (data.success) {
            const companies = data.data;
            document.getElementById('content-area').innerHTML = `
                <div class="p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">🏢 Companies (${companies.length})</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        ${companies.map(company => `
                            <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                                <h3 class="font-semibold text-gray-900 text-lg">${company.name}</h3>
                                <p class="text-sm text-gray-600 mb-2">${company.industry}</p>
                                <p class="text-sm text-gray-500 mb-2">${company.location}</p>
                                <div class="flex justify-between items-center mb-3">
                                    <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">${company.size}</span>
                                    <span class="text-xs text-gray-500">${company.employeeCount} employees</span>
                                </div>
                                <p class="text-sm text-gray-600 mb-3">${company.description}</p>
                                <div class="flex space-x-2">
                                    <button onclick="loadGraphVisualization('${company._id}', 'company')" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">
                                        🌐 View Graph
                                    </button>
                                    <button onclick="loadMatchesForCompany('${company._id}')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200">
                                        🎯 View Candidates
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading companies:', error);
    }
}

// Load hiring authorities
async function loadHiringAuthorities() {
    try {
        const response = await fetch('/api/hiring-authorities');
        const data = await response.json();

        if (data.success) {
            const hiringAuthorities = data.data;
            document.getElementById('content-area').innerHTML = `
                <div class="p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">👔 Hiring Authorities (${hiringAuthorities.length})</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        ${hiringAuthorities.map(authority => `
                            <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                                <div class="flex justify-between items-start mb-4">
                                    <div>
                                        <h3 class="font-semibold text-gray-900 text-lg">${authority.name}</h3>
                                        <p class="text-sm text-gray-600">${authority.title}</p>
                                        <p class="text-sm text-gray-500">${authority.department}</p>
                                    </div>
                                    <div class="text-right">
                                        <span class="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">${authority.level}</span>
                                    </div>
                                </div>
                                <div class="mb-4">
                                    <p class="text-sm text-gray-600"><strong>Hiring Scope:</strong> ${authority.hiringScope}</p>
                                    <p class="text-sm text-gray-500"><strong>Email:</strong> ${authority.email}</p>
                                </div>
                                <div class="flex space-x-2">
                                    <button onclick="loadHiringAuthorityMatches('${authority._id}')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">
                                        🎯 View Best Matches
                                    </button>
                                    <button onclick="loadGraphVisualization('${authority._id}', 'hiringAuthority')" class="px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs hover:bg-orange-200">
                                        🌐 View Graph
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading hiring authorities:', error);
    }
}

// Load hiring authority matches
async function loadHiringAuthorityMatches(authorityId) {
    try {
        const response = await fetch(`/api/hiring-matches/${authorityId}`);
        const data = await response.json();

        if (data.success) {
            const { authority, positions, matches } = data.data;

            document.getElementById('content-area').innerHTML = `
                <div class="p-6">
                    <div class="mb-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">🎯 Best Matches for ${authority.name}</h2>
                        <p class="text-gray-600">${authority.title} • ${authority.department}</p>
                        <p class="text-sm text-gray-500">Managing ${positions.length} positions</p>
                    </div>

                    <div class="space-y-4">
                        ${matches.map(match => `
                            <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                                <div class="flex justify-between items-start mb-4">
                                    <div>
                                        <h3 class="font-semibold text-gray-900">${match.jobSeeker.name}</h3>
                                        <p class="text-sm text-gray-600">${match.jobSeeker.title}</p>
                                        <p class="text-sm text-gray-500">${match.jobSeeker.location}</p>
                                    </div>
                                    <div class="text-right">
                                        <span class="text-2xl font-bold ${match.score >= 80 ? 'text-green-600' : match.score >= 60 ? 'text-yellow-600' : 'text-red-600'}">${match.score}%</span>
                                        <p class="text-xs text-gray-500">Match Score</p>
                                    </div>
                                </div>
                                <div class="border-t pt-4">
                                    <h4 class="font-medium text-gray-900">${match.position.title}</h4>
                                    <p class="text-sm text-gray-600">${match.company.name}</p>
                                    <p class="text-sm text-gray-500">${match.position.location} • ${match.position.salary}</p>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading hiring authority matches:', error);
    }
}

// Placeholder functions for other features
function loadPositions() {
    document.getElementById('content-area').innerHTML = `
        <div class="p-6">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">💼 Positions</h2>
            <p class="text-gray-600">Position listings coming soon...</p>
        </div>
    `;
}

function loadSkills() {
    document.getElementById('content-area').innerHTML = `
        <div class="p-6">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">🎯 Skills Analysis</h2>
            <p class="text-gray-600">Skills demand and availability analysis coming soon...</p>
        </div>
    `;
}

async function loadMatches() {
    document.getElementById('content-area').innerHTML = `
        <div class="p-6">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">🎯 Loading Job Matches...</h2>
            <div class="animate-pulse">
                <div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                <div class="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
        </div>
    `;

    try {
        const response = await fetch('/api/matches');
        const data = await response.json();

        if (data.success) {
            const matches = data.data;
            document.getElementById('content-area').innerHTML = `
                <div class="p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">🎯 Job Matches (${matches.length})</h2>
                    <div class="space-y-4">
                        ${matches.map(match => `
                            <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                                <div class="flex justify-between items-start mb-4">
                                    <div>
                                        <h3 class="font-semibold text-gray-900">${match.jobSeeker.name}</h3>
                                        <p class="text-sm text-gray-600">${match.jobSeeker.title}</p>
                                        <p class="text-sm text-gray-500">${match.jobSeeker.location}</p>
                                    </div>
                                    <div class="text-right">
                                        <span class="text-2xl font-bold ${match.score >= 80 ? 'text-green-600' : match.score >= 60 ? 'text-yellow-600' : 'text-red-600'}">${match.score}%</span>
                                        <p class="text-xs text-gray-500">Match Score</p>
                                    </div>
                                </div>
                                <div class="border-t pt-4">
                                    <h4 class="font-medium text-gray-900">${match.position.title}</h4>
                                    <p class="text-sm text-gray-600">${match.company.name}</p>
                                    <p class="text-sm text-gray-500">${match.position.location} • ${match.position.salary}</p>
                                </div>
                                <div class="mt-4 pt-4 border-t">
                                    <p class="text-sm text-gray-600">
                                        <span class="font-medium">Contact:</span> ${match.hiringAuthority ? match.hiringAuthority.name : 'N/A'}
                                        ${match.hiringAuthority ? '(' + match.hiringAuthority.title + ')' : ''}
                                    </p>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading matches:', error);
        document.getElementById('content-area').innerHTML = `
            <div class="p-6">
                <h2 class="text-2xl font-bold text-red-600 mb-4">❌ Error Loading Matches</h2>
                <p class="text-gray-600">There was an error loading the job matches. Please try again.</p>
            </div>
        `;
    }
}

// Load graph visualization
async function loadGraphVisualization(focus = null, mode = null) {
    // Handle the mode parameter for direct 2D/3D calls
    if (mode === '2d') {
        is3DMode = false;
    } else if (mode === '3d') {
        is3DMode = true;
    }
    document.getElementById('content-area').innerHTML = `
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-900">🌐 Graph Visualization</h2>
                <div class="flex space-x-2">
                    <button onclick="loadGraphVisualization(null, 'overview')" class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded hover:bg-blue-200">
                        Overview
                    </button>
                    <button onclick="toggleVisualizationMode()" id="mode-toggle" class="px-3 py-1 text-sm bg-purple-100 text-purple-800 rounded hover:bg-purple-200">
                        Switch to 3D
                    </button>
                    <button onclick="toggleEditMode()" id="edit-toggle" class="px-3 py-1 text-sm bg-green-100 text-green-800 rounded hover:bg-green-200">
                        Enable Editing
                    </button>
                </div>
            </div>

            <!-- Legend -->
            <div class="mb-4 p-4 bg-gray-50 rounded-lg">
                <h3 class="font-semibold mb-3">Legend:</h3>
                <div class="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-blue-500 rounded-full mr-2"></div>
                        <span>Job Seekers</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-green-500 rounded-full mr-2"></div>
                        <span>Companies</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-orange-500 rounded-full mr-2"></div>
                        <span>Hiring Authorities</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-purple-500 rounded-full mr-2"></div>
                        <span>Positions</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-red-500 rounded-full mr-2"></div>
                        <span>Skills</span>
                    </div>
                </div>
            </div>

            <!-- Graph Container -->
            <div class="border border-gray-200 rounded-lg bg-white graph-container" style="height: 600px;">
                <div id="graph-container" style="width: 100%; height: 100%;"></div>
                <div class="graph-controls">
                    <div class="bg-white rounded-lg shadow-lg p-2 space-y-2">
                        <button onclick="resetGraphView()" class="w-full px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs hover:bg-gray-200">
                            Reset View
                        </button>
                        <button onclick="centerGraph()" class="w-full px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">
                            Center Graph
                        </button>
                    </div>
                </div>
            </div>

            <!-- Node Details Panel -->
            <div id="node-details" class="mt-4 p-4 bg-blue-50 rounded-lg" style="display: none;">
                <h3 class="font-semibold text-blue-900 mb-2">Node Details</h3>
                <div id="node-details-content"></div>
            </div>
        </div>

        <!-- CRUD Modal -->
        <div id="crud-modal" class="fixed inset-0 modal-overlay z-50" style="display: none;">
            <div class="flex items-center justify-center min-h-screen p-4">
                <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 id="modal-title" class="text-lg font-semibold text-gray-900">Edit Node</h3>
                            <button onclick="closeCrudModal()" class="text-gray-400 hover:text-gray-600">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        <form id="crud-form">
                            <div id="form-fields" class="space-y-4">
                                <!-- Dynamic form fields will be inserted here -->
                            </div>
                            <div class="flex justify-end space-x-3 mt-6">
                                <button type="button" onclick="closeCrudModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">
                                    Cancel
                                </button>
                                <button type="button" onclick="deleteNode()" id="delete-btn" class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700">
                                    Delete
                                </button>
                                <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                                    Save Changes
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Load graph data and render
    await loadAndRenderGraph(focus, focus || 'overview');
}

async function loadAndRenderGraph(focus = null, type = 'overview') {
    try {
        const url = focus ? `/api/graph?focus=${focus}&type=${type}` : '/api/graph?type=overview';
        const response = await fetch(url);
        const data = await response.json();

        if (data.success) {
            renderGraph(data.data);
        } else {
            console.error('Failed to load graph data:', data.error);
        }
    } catch (error) {
        console.error('Error loading graph:', error);
    }
}

function renderGraph(graphData) {
    currentGraphData = graphData;
    const container = document.getElementById('graph-container');
    container.innerHTML = ''; // Clear previous graph

    if (is3DMode) {
        render3DGraph(graphData, container);
    } else {
        render2DGraph(graphData, container);
    }
}

function render2DGraph(graphData, container) {
    // Create SVG
    const width = container.clientWidth;
    const height = container.clientHeight;

    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', width);
    svg.setAttribute('height', height);
    svg.style.background = '#f8fafc';
    svg.setAttribute('viewBox', `0 0 ${width} ${height}`);

    // Create main group for pan/zoom transformations
    const mainGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    svg.appendChild(mainGroup);
    container.appendChild(svg);

    // Add pan and zoom functionality
    let isPanning = false;
    let panStart = { x: 0, y: 0 };
    let currentTransform = { x: 0, y: 0, scale: 1 };

    // Mouse wheel zoom to cursor
    svg.addEventListener('wheel', (e) => {
        e.preventDefault();
        const rect = svg.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;

        const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
        const newScale = currentTransform.scale * zoomFactor;

        // Zoom towards cursor position
        const dx = mouseX - currentTransform.x;
        const dy = mouseY - currentTransform.y;

        currentTransform.x = mouseX - dx * zoomFactor;
        currentTransform.y = mouseY - dy * zoomFactor;
        currentTransform.scale = newScale;

        updateTransform();
    });

    // Pan functionality
    svg.addEventListener('mousedown', (e) => {
        if (e.button === 0 && !e.target.closest('g[data-node]')) { // Left click and not on a node
            isPanning = true;
            panStart.x = e.clientX - currentTransform.x;
            panStart.y = e.clientY - currentTransform.y;
            svg.style.cursor = 'grabbing';
        }
    });

    svg.addEventListener('mousemove', (e) => {
        if (isPanning) {
            currentTransform.x = e.clientX - panStart.x;
            currentTransform.y = e.clientY - panStart.y;
            updateTransform();
        }
    });

    svg.addEventListener('mouseup', () => {
        isPanning = false;
        svg.style.cursor = 'default';
    });

    svg.addEventListener('mouseleave', () => {
        isPanning = false;
        svg.style.cursor = 'default';
    });

    function updateTransform() {
        mainGroup.setAttribute('transform',
            `translate(${currentTransform.x}, ${currentTransform.y}) scale(${currentTransform.scale})`
        );
    }

    // Simple force simulation using basic positioning
    const nodes = graphData.nodes.map(d => ({...d}));
    const links = graphData.links.map(d => ({...d}));

    // Position nodes in a circle
    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) / 3;

    if (nodes.length === 1) {
        nodes[0].x = centerX;
        nodes[0].y = centerY;
    } else {
        nodes.forEach((node, i) => {
            const angle = (i / nodes.length) * 2 * Math.PI;
            node.x = centerX + Math.cos(angle) * radius;
            node.y = centerY + Math.sin(angle) * radius;
        });
    }

    // Draw links
    links.forEach((link, index) => {
        const sourceNode = nodes.find(n => n.id === link.source);
        const targetNode = nodes.find(n => n.id === link.target);

        if (sourceNode && targetNode) {
            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            line.setAttribute('x1', sourceNode.x);
            line.setAttribute('y1', sourceNode.y);
            line.setAttribute('x2', targetNode.x);
            line.setAttribute('y2', targetNode.y);
            line.setAttribute('stroke', link.color || '#94A3B8');
            line.setAttribute('stroke-width', '2');
            line.setAttribute('opacity', '0.6');
            line.setAttribute('data-link', `${link.source}-${link.target}`);
            mainGroup.appendChild(line);

            // Add link label
            if (link.label) {
                const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                text.setAttribute('x', (sourceNode.x + targetNode.x) / 2);
                text.setAttribute('y', (sourceNode.y + targetNode.y) / 2);
                text.setAttribute('text-anchor', 'middle');
                text.setAttribute('font-size', '10');
                text.setAttribute('fill', '#6B7280');
                text.textContent = link.label;
                mainGroup.appendChild(text);
            }
        }
    });

    // Draw nodes
    nodes.forEach(node => {
        const group = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        group.style.cursor = 'pointer';
        group.setAttribute('data-node', node.id);

        const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        circle.setAttribute('cx', node.x);
        circle.setAttribute('cy', node.y);
        circle.setAttribute('r', node.size || 8);
        circle.setAttribute('fill', node.color);
        circle.setAttribute('stroke', '#fff');
        circle.setAttribute('stroke-width', '2');

        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.setAttribute('x', node.x);
        text.setAttribute('y', node.y + (node.size || 8) + 15);
        text.setAttribute('text-anchor', 'middle');
        text.setAttribute('font-size', '12');
        text.setAttribute('font-weight', 'bold');
        text.setAttribute('fill', '#374151');
        text.textContent = node.name.length > 15 ? node.name.substring(0, 15) + '...' : node.name;

        group.appendChild(circle);
        group.appendChild(text);

        // Add click handler
        group.addEventListener('click', () => {
            if (isEditMode) {
                openCrudModal(node);
            } else {
                showNodeDetails(node);
            }
        });

        mainGroup.appendChild(group);
    });
}

function render3DGraph(graphData, container) {
    // Create 3D force graph with constrained size and labels
    const Graph = ForceGraph3D()(container)
        .graphData(graphData)
        .width(container.clientWidth)
        .height(container.clientHeight)
        .nodeLabel('name')
        .nodeColor(node => node.color)
        .nodeVal(node => node.size)
        .nodeThreeObject(node => {
            // Create a group for node and label
            const group = new THREE.Group();

            // Create sphere for node
            const sphereGeometry = new THREE.SphereGeometry(node.size || 8);
            const sphereMaterial = new THREE.MeshLambertMaterial({ color: node.color });
            const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
            group.add(sphere);

            // Create text label
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            canvas.width = 256;
            canvas.height = 64;

            context.fillStyle = '#ffffff';
            context.fillRect(0, 0, canvas.width, canvas.height);
            context.fillStyle = '#000000';
            context.font = '16px Arial';
            context.textAlign = 'center';
            context.fillText(node.name.length > 15 ? node.name.substring(0, 15) + '...' : node.name, 128, 40);

            const texture = new THREE.CanvasTexture(canvas);
            const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
            const sprite = new THREE.Sprite(spriteMaterial);
            sprite.scale.set(40, 10, 1);
            sprite.position.set(0, (node.size || 8) + 15, 0);
            group.add(sprite);

            return group;
        })
        .linkLabel('label')
        .linkColor(link => link.color)
        .linkWidth(2)
        .linkOpacity(0.6)
        .onNodeClick((node, event) => {
            // Animate camera to optimal position for selected node
            animateCameraToNode(node, Graph);

            if (isEditMode) {
                openCrudModal(node);
            } else {
                showNodeDetails(node);
            }
        })
        .enableNodeDrag(isEditMode)
        .showNavInfo(false)
        .onEngineStop(() => {
            // Ensure graph fits within container
            Graph.zoomToFit(400, 100);
        });

    currentGraph = Graph;

    // Enhanced controls with zoom-to-cursor and pan functionality
    const controls = Graph.controls();
    controls.enableDamping = true;
    controls.dampingFactor = 0.1;
    controls.enablePan = true;
    controls.enableZoom = true;
    controls.enableRotate = true;

    // Add zoom-to-cursor functionality
    const renderer = Graph.renderer();
    const camera = Graph.camera();

    container.addEventListener('wheel', (event) => {
        event.preventDefault();

        const rect = container.getBoundingClientRect();
        const mouse = new THREE.Vector2();
        mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

        const raycaster = new THREE.Raycaster();
        raycaster.setFromCamera(mouse, camera);

        const zoomFactor = event.deltaY > 0 ? 1.1 : 0.9;
        const direction = new THREE.Vector3();
        camera.getWorldDirection(direction);

        camera.position.addScaledVector(direction, (zoomFactor - 1) * 50);
        controls.update();
    });
}

function animateCameraToNode(node, graph) {
    const camera = graph.camera();
    const controls = graph.controls();

    // Calculate optimal camera position
    const nodePosition = new THREE.Vector3(node.x || 0, node.y || 0, node.z || 0);
    const distance = 200; // Optimal viewing distance
    const angle = Math.PI / 4; // 45-degree angle for best view

    // Calculate camera target position
    const targetPosition = new THREE.Vector3(
        nodePosition.x + distance * Math.cos(angle),
        nodePosition.y + distance * Math.sin(angle),
        nodePosition.z + distance
    );

    // Animate camera movement with ease-out
    const startPosition = camera.position.clone();
    const startTarget = controls.target.clone();
    const duration = 1500; // 1.5 seconds
    const startTime = Date.now();

    function animate() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Ease-out cubic function
        const easeOut = 1 - Math.pow(1 - progress, 3);

        // Interpolate camera position
        camera.position.lerpVectors(startPosition, targetPosition, easeOut);
        controls.target.lerpVectors(startTarget, nodePosition, easeOut);

        controls.update();

        if (progress < 1) {
            requestAnimationFrame(animate);
        }
    }

    animate();
}

function toggleVisualizationMode() {
    is3DMode = !is3DMode;
    const toggleBtn = document.getElementById('mode-toggle');
    toggleBtn.textContent = is3DMode ? 'Switch to 2D' : 'Switch to 3D';

    if (currentGraphData) {
        renderGraph(currentGraphData);
    }
}

function toggleEditMode() {
    isEditMode = !isEditMode;
    const editBtn = document.getElementById('edit-toggle');
    editBtn.textContent = isEditMode ? 'Disable Editing' : 'Enable Editing';
    editBtn.className = isEditMode ?
        'px-3 py-1 text-sm bg-red-100 text-red-800 rounded hover:bg-red-200' :
        'px-3 py-1 text-sm bg-green-100 text-green-800 rounded hover:bg-green-200';

    if (currentGraphData) {
        renderGraph(currentGraphData);
    }
}

function showNodeDetails(node) {
    const detailsPanel = document.getElementById('node-details');
    const detailsContent = document.getElementById('node-details-content');

    detailsContent.innerHTML = `
        <div class="space-y-2">
            <p><strong>Name:</strong> ${node.name}</p>
            <p><strong>Type:</strong> ${node.type}</p>
            <p><strong>ID:</strong> ${node.id}</p>
        </div>
    `;

    detailsPanel.style.display = 'block';
}

function openCrudModal(node) {
    selectedNode = node;
    const modal = document.getElementById('crud-modal');
    const title = document.getElementById('modal-title');
    const formFields = document.getElementById('form-fields');

    title.textContent = `Edit ${node.type}: ${node.name}`;

    // Generate form fields based on node type
    formFields.innerHTML = `
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
            <input type="text" id="node-name" value="${node.name}" class="w-full p-2 border border-gray-300 rounded-md">
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Type</label>
            <input type="text" id="node-type" value="${node.type}" class="w-full p-2 border border-gray-300 rounded-md" readonly>
        </div>
    `;

    modal.style.display = 'block';
}

function closeCrudModal() {
    const modal = document.getElementById('crud-modal');
    modal.style.display = 'none';
    selectedNode = null;
}

function resetGraphView() {
    if (currentGraph && is3DMode) {
        currentGraph.zoomToFit(400, 100);
    }
}

function centerGraph() {
    if (currentGraph && is3DMode) {
        currentGraph.centerAt(0, 0, 0);
    }
}

// Load connections overview page
async function loadConnections() {
    try {
        const [statsResponse, graphResponse] = await Promise.all([
            fetch('/api/stats'),
            fetch('/api/graph?type=overview')
        ]);

        const [statsData, graphData] = await Promise.all([
            statsResponse.json(),
            graphResponse.json()
        ]);

        if (statsData.success && graphData.success) {
            const stats = statsData.data;
            const { nodes, links } = graphData.data;

            // Analyze connections
            const connectionTypes = {
                'hasSkill': { count: 0, label: 'Job Seeker → Skills', color: 'blue' },
                'requiresSkill': { count: 0, label: 'Position → Skills', color: 'purple' },
                'manages': { count: 0, label: 'Authority → Positions', color: 'orange' },
                'employs': { count: 0, label: 'Company → Authorities', color: 'green' },
                'matches': { count: 0, label: 'Job Matches', color: 'red' }
            };

            links.forEach(link => {
                if (connectionTypes[link.type]) {
                    connectionTypes[link.type].count++;
                }
            });

            document.getElementById('content-area').innerHTML = `
                <div class="p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">🔗 Connections Overview</h2>

                    <!-- Summary Stats -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
                            <h3 class="text-lg font-semibold mb-2">Total Entities</h3>
                            <p class="text-3xl font-bold">${nodes.length}</p>
                            <p class="text-sm opacity-90">Across all categories</p>
                        </div>
                        <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
                            <h3 class="text-lg font-semibold mb-2">Total Connections</h3>
                            <p class="text-3xl font-bold">${links.length}</p>
                            <p class="text-sm opacity-90">Relationship mappings</p>
                        </div>
                        <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
                            <h3 class="text-lg font-semibold mb-2">Network Density</h3>
                            <p class="text-3xl font-bold">${Math.round((links.length / (nodes.length * (nodes.length - 1))) * 10000) / 100}%</p>
                            <p class="text-sm opacity-90">Connection ratio</p>
                        </div>
                    </div>

                    <!-- Connection Types -->
                    <div class="mb-8">
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Connection Types</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            ${Object.entries(connectionTypes).map(([type, info]) => `
                                <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                                    <div class="flex items-center justify-between mb-2">
                                        <h4 class="font-medium text-gray-900">${info.label}</h4>
                                        <span class="text-lg font-bold text-${info.color}-600">${info.count}</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-${info.color}-600 h-2 rounded-full" style="width: ${Math.min((info.count / Math.max(...Object.values(connectionTypes).map(c => c.count))) * 100, 100)}%"></div>
                                    </div>
                                    <div class="mt-2 flex space-x-2">
                                        <button onclick="loadGraphVisualization('${type}', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">
                                            2D View
                                        </button>
                                        <button onclick="loadGraphVisualization('${type}', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200">
                                            3D View
                                        </button>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <!-- Entity Categories -->
                    <div class="mb-8">
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Entity Categories</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="text-2xl">👤</div>
                                    <span class="text-xl font-bold text-blue-600">${stats.jobSeekers}</span>
                                </div>
                                <h4 class="font-semibold text-blue-900">Job Seekers</h4>
                                <p class="text-sm text-blue-700">Active candidates</p>
                                <div class="mt-3 flex space-x-1">
                                    <button onclick="loadJobSeekers()" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">View All</button>
                                    <button onclick="loadGraphVisualization('jobSeekers', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">2D</button>
                                    <button onclick="loadGraphVisualization('jobSeekers', '3d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">3D</button>
                                </div>
                            </div>

                            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="text-2xl">🏢</div>
                                    <span class="text-xl font-bold text-green-600">${stats.companies}</span>
                                </div>
                                <h4 class="font-semibold text-green-900">Companies</h4>
                                <p class="text-sm text-green-700">Hiring organizations</p>
                                <div class="mt-3 flex space-x-1">
                                    <button onclick="loadCompanies()" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">View All</button>
                                    <button onclick="loadGraphVisualization('companies', '2d')" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">2D</button>
                                    <button onclick="loadGraphVisualization('companies', '3d')" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">3D</button>
                                </div>
                            </div>

                            <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="text-2xl">👔</div>
                                    <span class="text-xl font-bold text-orange-600">${stats.hiringAuthorities}</span>
                                </div>
                                <h4 class="font-semibold text-orange-900">Hiring Authorities</h4>
                                <p class="text-sm text-orange-700">Decision makers</p>
                                <div class="mt-3 flex space-x-1">
                                    <button onclick="loadHiringAuthorities()" class="px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs hover:bg-orange-200">View All</button>
                                    <button onclick="loadGraphVisualization('hiringAuthorities', '2d')" class="px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs hover:bg-orange-200">2D</button>
                                    <button onclick="loadGraphVisualization('hiringAuthorities', '3d')" class="px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs hover:bg-orange-200">3D</button>
                                </div>
                            </div>

                            <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="text-2xl">💼</div>
                                    <span class="text-xl font-bold text-purple-600">${stats.positions}</span>
                                </div>
                                <h4 class="font-semibold text-purple-900">Positions</h4>
                                <p class="text-sm text-purple-700">Open opportunities</p>
                                <div class="mt-3 flex space-x-1">
                                    <button onclick="loadPositions()" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200">View All</button>
                                    <button onclick="loadGraphVisualization('positions', '2d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200">2D</button>
                                    <button onclick="loadGraphVisualization('positions', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200">3D</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <button onclick="loadMatches()" class="p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 text-left">
                                <div class="text-2xl mb-2">🎯</div>
                                <h4 class="font-semibold text-gray-900">View All Matches</h4>
                                <p class="text-sm text-gray-600">See intelligent job matching results</p>
                            </button>
                            <button onclick="loadSkills()" class="p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 text-left">
                                <div class="text-2xl mb-2">⚡</div>
                                <h4 class="font-semibold text-gray-900">Skills Analysis</h4>
                                <p class="text-sm text-gray-600">Analyze skills demand and availability</p>
                            </button>
                            <button onclick="loadGraphVisualization('overview', '3d')" class="p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 text-left">
                                <div class="text-2xl mb-2">🌐</div>
                                <h4 class="font-semibold text-gray-900">3D Network View</h4>
                                <p class="text-sm text-gray-600">Explore the complete network in 3D</p>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading connections:', error);
        document.getElementById('content-area').innerHTML = `
            <div class="p-6">
                <h2 class="text-2xl font-bold text-red-600 mb-4">❌ Error Loading Connections</h2>
                <p class="text-gray-600">There was an error loading the connections overview. Please try again.</p>
            </div>
        `;
    }
}
