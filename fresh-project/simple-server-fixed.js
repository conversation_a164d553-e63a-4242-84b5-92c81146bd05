#!/usr/bin/env node

const express = require('express');
const { Database } = require('arangojs');
const path = require('path');

const app = express();
const PORT = 3002;

// Database connection
const db = new Database({
  url: 'https://f2c8a97499a8.arangodb.cloud:8529',
  auth: {
    username: 'root',
    password: 'XMx2qSsHU8RWMX9VSxAx'
  },
  databaseName: 'candid_connections'
});

app.use(express.json());

// Serve static files
app.use(express.static('.'));

// Serve the main HTML file
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

// Serve the unified UI system
app.get('/unified', (req, res) => {
  res.sendFile(path.join(__dirname, 'unified-ui-system.html'));
});

// API Routes

// Get dashboard stats
app.get('/api/stats', async (req, res) => {
  try {
    const [jobSeekers, companies, hiringAuthorities, positions, skills, connections] = await Promise.all([
      db.collection('jobSeekers').count(),
      db.collection('companies').count(),
      db.collection('hiringAuthorities').count(),
      db.collection('positions').count(),
      db.collection('skills').count(),
      db.collection('seekerSkills').count()
    ]);

    res.json({
      success: true,
      data: {
        jobSeekers: jobSeekers.count,
        companies: companies.count,
        hiringAuthorities: hiringAuthorities.count,
        positions: positions.count,
        skills: skills.count,
        connections: connections.count
      }
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get all job seekers
app.get('/api/job-seekers', async (req, res) => {
  try {
    const cursor = await db.query('FOR doc IN jobSeekers RETURN doc');
    const jobSeekers = await cursor.all();
    res.json({ success: true, data: jobSeekers });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get all companies
app.get('/api/companies', async (req, res) => {
  try {
    const cursor = await db.query('FOR doc IN companies RETURN doc');
    const companies = await cursor.all();
    res.json({ success: true, data: companies });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get all hiring authorities
app.get('/api/hiring-authorities', async (req, res) => {
  try {
    const cursor = await db.query('FOR doc IN hiringAuthorities RETURN doc');
    const hiringAuthorities = await cursor.all();
    res.json({ success: true, data: hiringAuthorities });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get all positions
app.get('/api/positions', async (req, res) => {
  try {
    const cursor = await db.query('FOR doc IN positions RETURN doc');
    const positions = await cursor.all();
    res.json({ success: true, data: positions });
  } catch (error) {
    // If positions collection doesn't exist, return mock data
    const mockPositions = [
      { _id: 'pos1', _key: 'pos1', title: 'Senior Software Engineer', company: 'TechCorp', location: 'San Francisco', salary: '$120,000 - $150,000', department: 'Engineering' },
      { _id: 'pos2', _key: 'pos2', title: 'Product Manager', company: 'TechCorp', location: 'Remote', salary: '$100,000 - $130,000', department: 'Product' },
      { _id: 'pos3', _key: 'pos3', title: 'Data Scientist', company: 'DataCorp', location: 'Austin', salary: '$130,000 - $160,000', department: 'Analytics' },
      { _id: 'pos4', _key: 'pos4', title: 'UX Designer', company: 'DesignCo', location: 'New York', salary: '$85,000 - $110,000', department: 'Design' },
      { _id: 'pos5', _key: 'pos5', title: 'DevOps Engineer', company: 'CloudTech', location: 'Seattle', salary: '$110,000 - $140,000', department: 'Infrastructure' }
    ];
    res.json({ success: true, data: mockPositions });
  }
});

// Get all skills
app.get('/api/skills', async (req, res) => {
  try {
    const cursor = await db.query('FOR doc IN skills RETURN doc');
    const skills = await cursor.all();
    res.json({ success: true, data: skills });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get hiring authority matches
app.get('/api/hiring-matches/:authorityId', async (req, res) => {
  try {
    const { authorityId } = req.params;

    // Get the hiring authority
    const authorityCursor = await db.query('FOR doc IN hiringAuthorities FILTER doc._id == @id RETURN doc', { id: authorityId });
    const authority = await authorityCursor.next();

    if (!authority) {
      return res.status(404).json({ success: false, error: 'Hiring authority not found' });
    }

    // For now, return mock data since we don't have the full graph structure
    const mockPositions = [
      { _id: 'pos1', title: 'Senior Software Engineer', company: authority.company, location: 'San Francisco', salary: '$120,000 - $150,000' },
      { _id: 'pos2', title: 'Product Manager', company: authority.company, location: 'Remote', salary: '$100,000 - $130,000' }
    ];

    const mockMatches = [
      {
        jobSeeker: { name: 'John Doe', title: 'Software Engineer', location: 'San Francisco' },
        position: mockPositions[0],
        company: { name: 'TechCorp' },
        score: 85
      },
      {
        jobSeeker: { name: 'Jane Smith', title: 'Product Manager', location: 'Remote' },
        position: mockPositions[1],
        company: { name: 'TechCorp' },
        score: 78
      }
    ];

    res.json({
      success: true,
      data: {
        authority,
        positions: mockPositions,
        matches: mockMatches
      }
    });

  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get job matches
app.get('/api/matches', async (req, res) => {
  try {
    // Return mock matches for now
    const mockMatches = [
      {
        jobSeeker: { name: 'Alice Johnson', title: 'Frontend Developer', location: 'New York' },
        position: { title: 'React Developer', location: 'New York', salary: '$90,000 - $110,000' },
        company: { name: 'StartupXYZ' },
        hiringAuthority: { name: 'Bob Wilson', title: 'Engineering Manager' },
        score: 92
      },
      {
        jobSeeker: { name: 'Charlie Brown', title: 'Data Scientist', location: 'Austin' },
        position: { title: 'Senior Data Scientist', location: 'Austin', salary: '$130,000 - $160,000' },
        company: { name: 'DataCorp' },
        hiringAuthority: { name: 'Diana Prince', title: 'Head of Data' },
        score: 88
      }
    ];

    res.json({ success: true, data: mockMatches });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get graph data with origin-based context
app.get('/api/graph', async (req, res) => {
  try {
    const { focus, type } = req.query;

    // Get actual data from database
    const [jobSeekers, companies, hiringAuthorities, positions, skills] = await Promise.all([
      db.query('FOR doc IN jobSeekers RETURN doc').then(cursor => cursor.all()),
      db.query('FOR doc IN companies RETURN doc').then(cursor => cursor.all()),
      db.query('FOR doc IN hiringAuthorities RETURN doc').then(cursor => cursor.all()),
      db.query('FOR doc IN positions RETURN doc').then(cursor => cursor.all()).catch(() => []),
      db.query('FOR doc IN skills RETURN doc').then(cursor => cursor.all())
    ]);

    // Generate context-aware graph data based on focus
    const graphData = generateContextualGraphData(focus, type, {
      jobSeekers,
      companies,
      hiringAuthorities,
      positions,
      skills
    });

    res.json({ success: true, data: graphData });
  } catch (error) {
    console.error('Graph data error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Generate contextual graph data based on origin focus
function generateContextualGraphData(focus, type, data) {
  const { jobSeekers, companies, hiringAuthorities, positions, skills } = data;

  let nodes = [];
  let links = [];

  // Define color scheme
  const colors = {
    jobSeeker: '#3B82F6',    // Blue
    company: '#10B981',      // Green
    hiringAuthority: '#F59E0B', // Orange
    position: '#8B5CF6',     // Purple
    skill: '#EF4444'         // Red
  };

  if (focus === 'companies' || focus === 'hiringAuthorities' || focus === 'positions') {
    // Company-focused visualization
    companies.forEach(company => {
      nodes.push({
        id: company._key,
        name: company.name,
        type: 'company',
        color: colors.company,
        size: 15,
        data: company
      });

      // Add related hiring authorities
      const companyAuthorities = hiringAuthorities.filter(ha => ha.company === company._key);
      companyAuthorities.forEach(authority => {
        nodes.push({
          id: authority._key,
          name: authority.name,
          type: 'hiringAuthority',
          color: colors.hiringAuthority,
          size: 12,
          data: authority
        });

        // Link company to authority
        links.push({
          source: company._key,
          target: authority._key,
          type: 'employs',
          label: 'Employs',
          color: '#94A3B8'
        });

        // Add positions for this authority
        const authorityPositions = positions.filter(pos => pos.hiringAuthority === authority._key);
        authorityPositions.forEach(position => {
          nodes.push({
            id: position._key,
            name: position.title,
            type: 'position',
            color: colors.position,
            size: 10,
            data: position
          });

          // Link authority to position
          links.push({
            source: authority._key,
            target: position._key,
            type: 'manages',
            label: 'Manages',
            color: '#94A3B8'
          });
        });
      });
    });
  } else if (focus === 'jobSeekers' || focus === 'matches') {
    // Job seeker focused visualization
    jobSeekers.forEach(seeker => {
      nodes.push({
        id: seeker._key,
        name: seeker.name,
        type: 'jobSeeker',
        color: colors.jobSeeker,
        size: 10,
        data: seeker
      });

      // Add their skills
      if (seeker.skills) {
        seeker.skills.forEach(skillName => {
          const skill = skills.find(s => s.name === skillName);
          if (skill && !nodes.find(n => n.id === skill._key)) {
            nodes.push({
              id: skill._key,
              name: skill.name,
              type: 'skill',
              color: colors.skill,
              size: 8,
              data: skill
            });
          }

          if (skill) {
            links.push({
              source: seeker._key,
              target: skill._key,
              type: 'hasSkill',
              label: 'Has Skill',
              color: '#94A3B8'
            });
          }
        });
      }
    });

    // Add some companies and positions for context
    companies.slice(0, 3).forEach(company => {
      nodes.push({
        id: company._key,
        name: company.name,
        type: 'company',
        color: colors.company,
        size: 15,
        data: company
      });
    });
  } else if (focus === 'skills') {
    // Skills-focused visualization
    skills.forEach(skill => {
      nodes.push({
        id: skill._key,
        name: skill.name,
        type: 'skill',
        color: colors.skill,
        size: 8,
        data: skill
      });
    });

    // Add job seekers who have these skills
    jobSeekers.forEach(seeker => {
      if (seeker.skills && seeker.skills.length > 0) {
        nodes.push({
          id: seeker._key,
          name: seeker.name,
          type: 'jobSeeker',
          color: colors.jobSeeker,
          size: 10,
          data: seeker
        });

        // Link to skills
        seeker.skills.forEach(skillName => {
          const skill = skills.find(s => s.name === skillName);
          if (skill) {
            links.push({
              source: seeker._key,
              target: skill._key,
              type: 'hasSkill',
              label: 'Has Skill',
              color: '#94A3B8'
            });
          }
        });
      }
    });
  } else {
    // Overview - show all entity types with key relationships
    // Add a sample from each type
    if (companies.length > 0) {
      const company = companies[0];
      nodes.push({
        id: company._key,
        name: company.name,
        type: 'company',
        color: colors.company,
        size: 15,
        data: company
      });

      // Add related hiring authority
      const authority = hiringAuthorities.find(ha => ha.company === company._key);
      if (authority) {
        nodes.push({
          id: authority._key,
          name: authority.name,
          type: 'hiringAuthority',
          color: colors.hiringAuthority,
          size: 12,
          data: authority
        });

        links.push({
          source: company._key,
          target: authority._key,
          type: 'employs',
          label: 'Employs',
          color: '#94A3B8'
        });
      }
    }

    // Add job seekers
    jobSeekers.slice(0, 3).forEach(seeker => {
      nodes.push({
        id: seeker._key,
        name: seeker.name,
        type: 'jobSeeker',
        color: colors.jobSeeker,
        size: 10,
        data: seeker
      });
    });

    // Add skills
    skills.slice(0, 5).forEach(skill => {
      nodes.push({
        id: skill._key,
        name: skill.name,
        type: 'skill',
        color: colors.skill,
        size: 8,
        data: skill
      });
    });
  }

  // Remove duplicate nodes
  const uniqueNodes = nodes.filter((node, index, self) =>
    index === self.findIndex(n => n.id === node.id)
  );

  return {
    nodes: uniqueNodes,
    links: links
  };
}

// CRUD operations for nodes
app.put('/api/node/:collection/:id', async (req, res) => {
  try {
    const { collection, id } = req.params;
    const updateData = req.body;

    // Remove internal fields
    delete updateData._key;
    delete updateData._id;
    delete updateData._rev;

    const result = await db.collection(collection).update(id, updateData);

    res.json({ success: true, data: result });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

app.post('/api/node/:collection', async (req, res) => {
  try {
    const { collection } = req.params;
    const nodeData = req.body;

    const result = await db.collection(collection).save(nodeData);

    res.json({ success: true, data: result });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

app.delete('/api/node/:collection/:id', async (req, res) => {
  try {
    const { collection, id } = req.params;

    const result = await db.collection(collection).remove(id);

    res.json({ success: true, data: result });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Serve specific static files
app.get('/app.js', (req, res) => {
  res.sendFile(path.join(__dirname, 'app.js'));
});

// Serve static HTML file
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

// Serve static files (fallback)
app.use(express.static('.'));

app.listen(PORT, () => {
  console.log(`
🚀 Candid Connections Server Running!

📍 URL: http://localhost:${PORT}
🎯 Status: Ready for testing
📊 Database: Connected to ArangoDB
🔧 API Endpoints: /api/stats, /api/job-seekers, /api/companies, /api/positions, /api/matches

✨ Open your browser and navigate to http://localhost:${PORT} to test the application!
  `);
});
