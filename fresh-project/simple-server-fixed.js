#!/usr/bin/env node

const express = require('express');
const { Database } = require('arangojs');
const path = require('path');

const app = express();
const PORT = 3002;

// Database connection
const db = new Database({
  url: 'https://f2c8a97499a8.arangodb.cloud:8529',
  auth: {
    username: 'root',
    password: 'XMx2qSsHU8RWMX9VSxAx'
  },
  databaseName: 'candid_connections'
});

app.use(express.json());

// API Routes

// Get dashboard stats
app.get('/api/stats', async (req, res) => {
  try {
    const [jobSeekers, companies, hiringAuthorities, positions, skills, connections] = await Promise.all([
      db.collection('jobSeekers').count(),
      db.collection('companies').count(),
      db.collection('hiringAuthorities').count(),
      db.collection('positions').count(),
      db.collection('skills').count(),
      db.collection('seekerSkills').count()
    ]);

    res.json({
      success: true,
      data: {
        jobSeekers: jobSeekers.count,
        companies: companies.count,
        hiringAuthorities: hiringAuthorities.count,
        positions: positions.count,
        skills: skills.count,
        connections: connections.count
      }
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get all job seekers
app.get('/api/job-seekers', async (req, res) => {
  try {
    const cursor = await db.query('FOR doc IN jobSeekers RETURN doc');
    const jobSeekers = await cursor.all();
    res.json({ success: true, data: jobSeekers });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get all companies
app.get('/api/companies', async (req, res) => {
  try {
    const cursor = await db.query('FOR doc IN companies RETURN doc');
    const companies = await cursor.all();
    res.json({ success: true, data: companies });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get all hiring authorities
app.get('/api/hiring-authorities', async (req, res) => {
  try {
    const cursor = await db.query('FOR doc IN hiringAuthorities RETURN doc');
    const hiringAuthorities = await cursor.all();
    res.json({ success: true, data: hiringAuthorities });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get all positions
app.get('/api/positions', async (req, res) => {
  try {
    const cursor = await db.query('FOR doc IN positions RETURN doc');
    const positions = await cursor.all();
    res.json({ success: true, data: positions });
  } catch (error) {
    // If positions collection doesn't exist, return mock data
    const mockPositions = [
      { _id: 'pos1', _key: 'pos1', title: 'Senior Software Engineer', company: 'TechCorp', location: 'San Francisco', salary: '$120,000 - $150,000', department: 'Engineering' },
      { _id: 'pos2', _key: 'pos2', title: 'Product Manager', company: 'TechCorp', location: 'Remote', salary: '$100,000 - $130,000', department: 'Product' },
      { _id: 'pos3', _key: 'pos3', title: 'Data Scientist', company: 'DataCorp', location: 'Austin', salary: '$130,000 - $160,000', department: 'Analytics' },
      { _id: 'pos4', _key: 'pos4', title: 'UX Designer', company: 'DesignCo', location: 'New York', salary: '$85,000 - $110,000', department: 'Design' },
      { _id: 'pos5', _key: 'pos5', title: 'DevOps Engineer', company: 'CloudTech', location: 'Seattle', salary: '$110,000 - $140,000', department: 'Infrastructure' }
    ];
    res.json({ success: true, data: mockPositions });
  }
});

// Get all skills
app.get('/api/skills', async (req, res) => {
  try {
    const cursor = await db.query('FOR doc IN skills RETURN doc');
    const skills = await cursor.all();
    res.json({ success: true, data: skills });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get hiring authority matches
app.get('/api/hiring-matches/:authorityId', async (req, res) => {
  try {
    const { authorityId } = req.params;

    // Get the hiring authority
    const authorityCursor = await db.query('FOR doc IN hiringAuthorities FILTER doc._id == @id RETURN doc', { id: authorityId });
    const authority = await authorityCursor.next();

    if (!authority) {
      return res.status(404).json({ success: false, error: 'Hiring authority not found' });
    }

    // For now, return mock data since we don't have the full graph structure
    const mockPositions = [
      { _id: 'pos1', title: 'Senior Software Engineer', company: authority.company, location: 'San Francisco', salary: '$120,000 - $150,000' },
      { _id: 'pos2', title: 'Product Manager', company: authority.company, location: 'Remote', salary: '$100,000 - $130,000' }
    ];

    const mockMatches = [
      {
        jobSeeker: { name: 'John Doe', title: 'Software Engineer', location: 'San Francisco' },
        position: mockPositions[0],
        company: { name: 'TechCorp' },
        score: 85
      },
      {
        jobSeeker: { name: 'Jane Smith', title: 'Product Manager', location: 'Remote' },
        position: mockPositions[1],
        company: { name: 'TechCorp' },
        score: 78
      }
    ];

    res.json({
      success: true,
      data: {
        authority,
        positions: mockPositions,
        matches: mockMatches
      }
    });

  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get job matches
app.get('/api/matches', async (req, res) => {
  try {
    // Return mock matches for now
    const mockMatches = [
      {
        jobSeeker: { name: 'Alice Johnson', title: 'Frontend Developer', location: 'New York' },
        position: { title: 'React Developer', location: 'New York', salary: '$90,000 - $110,000' },
        company: { name: 'StartupXYZ' },
        hiringAuthority: { name: 'Bob Wilson', title: 'Engineering Manager' },
        score: 92
      },
      {
        jobSeeker: { name: 'Charlie Brown', title: 'Data Scientist', location: 'Austin' },
        position: { title: 'Senior Data Scientist', location: 'Austin', salary: '$130,000 - $160,000' },
        company: { name: 'DataCorp' },
        hiringAuthority: { name: 'Diana Prince', title: 'Head of Data' },
        score: 88
      }
    ];

    res.json({ success: true, data: mockMatches });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get graph data
app.get('/api/graph', async (req, res) => {
  try {
    const { focus, type } = req.query;

    // Return mock graph data for now
    const mockGraphData = {
      nodes: [
        { id: 'js1', name: 'John Doe', type: 'jobSeeker', color: '#3B82F6', size: 10 },
        { id: 'c1', name: 'TechCorp', type: 'company', color: '#10B981', size: 15 },
        { id: 'ha1', name: 'Alice Manager', type: 'hiringAuthority', color: '#F59E0B', size: 12 },
        { id: 'p1', name: 'Software Engineer', type: 'position', color: '#8B5CF6', size: 10 },
        { id: 's1', name: 'JavaScript', type: 'skill', color: '#EF4444', size: 8 }
      ],
      links: [
        { source: 'js1', target: 's1', type: 'hasSkill', label: 'Expert', color: '#94A3B8' },
        { source: 'p1', target: 's1', type: 'requiresSkill', label: 'Required', color: '#94A3B8' },
        { source: 'ha1', target: 'p1', type: 'manages', label: 'Manages', color: '#94A3B8' },
        { source: 'c1', target: 'ha1', type: 'employs', label: 'Employs', color: '#94A3B8' }
      ]
    };

    res.json({ success: true, data: mockGraphData });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// CRUD operations for nodes
app.put('/api/node/:collection/:id', async (req, res) => {
  try {
    const { collection, id } = req.params;
    const updateData = req.body;

    // Remove internal fields
    delete updateData._key;
    delete updateData._id;
    delete updateData._rev;

    const result = await db.collection(collection).update(id, updateData);

    res.json({ success: true, data: result });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

app.post('/api/node/:collection', async (req, res) => {
  try {
    const { collection } = req.params;
    const nodeData = req.body;

    const result = await db.collection(collection).save(nodeData);

    res.json({ success: true, data: result });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

app.delete('/api/node/:collection/:id', async (req, res) => {
  try {
    const { collection, id } = req.params;

    const result = await db.collection(collection).remove(id);

    res.json({ success: true, data: result });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Serve specific static files
app.get('/app.js', (req, res) => {
  res.sendFile(path.join(__dirname, 'app.js'));
});

// Serve static HTML file
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'index.html'));
});

// Serve static files (fallback)
app.use(express.static('.'));

app.listen(PORT, () => {
  console.log(`
🚀 Candid Connections Server Running!

📍 URL: http://localhost:${PORT}
🎯 Status: Ready for testing
📊 Database: Connected to ArangoDB
🔧 API Endpoints: /api/stats, /api/job-seekers, /api/companies, /api/positions, /api/matches

✨ Open your browser and navigate to http://localhost:${PORT} to test the application!
  `);
});
