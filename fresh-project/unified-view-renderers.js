// Unified View Renderers for Candid Connections
// Consistent rendering patterns across all entity types

// Matches view renderer
function renderMatchesView(matches) {
    const entity = UnifiedState.entities.matches;
    document.getElementById('content-area').innerHTML = `
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-bold text-gray-900">${entity.icon} ${entity.name} (${matches.length})</h2>
                    <p class="text-gray-600">${entity.description}</p>
                </div>
                <div class="flex space-x-2">
                    <button onclick="openVisualizationModal('matches', '2d')" class="px-4 py-2 bg-blue-100 text-blue-800 rounded hover:bg-blue-200">
                        📊 2D View
                    </button>
                    <button onclick="openVisualizationModal('matches', '3d')" class="px-4 py-2 bg-purple-100 text-purple-800 rounded hover:bg-purple-200">
                        🌐 3D View
                    </button>
                </div>
            </div>

            <!-- Matches Grid -->
            <div class="space-y-4">
                ${matches.map(match => `
                    <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50 transition-all">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h3 class="font-semibold text-gray-900">${match.jobSeeker.name}</h3>
                                <p class="text-sm text-gray-600">${match.jobSeeker.title}</p>
                                <p class="text-sm text-gray-500">${match.jobSeeker.location}</p>
                            </div>
                            <div class="text-right">
                                <span class="text-2xl font-bold ${match.score >= 80 ? 'text-green-600' : match.score >= 60 ? 'text-yellow-600' : 'text-red-600'}">${match.score}%</span>
                                <p class="text-xs text-gray-500">Match Score</p>
                            </div>
                        </div>
                        <div class="border-t pt-4">
                            <h4 class="font-medium text-gray-900">${match.position.title}</h4>
                            <p class="text-sm text-gray-600">${match.company.name}</p>
                            <p class="text-sm text-gray-500">${match.position.location} • ${match.position.salary}</p>
                        </div>
                        <div class="mt-4 pt-4 border-t">
                            <p class="text-sm text-gray-600">
                                <span class="font-medium">Contact:</span> ${match.hiringAuthority ? match.hiringAuthority.name : 'N/A'}
                                ${match.hiringAuthority ? '(' + match.hiringAuthority.title + ')' : ''}
                            </p>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// Job Seekers view renderer
function renderJobSeekersView(jobSeekers) {
    const entity = UnifiedState.entities.jobSeekers;
    document.getElementById('content-area').innerHTML = `
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-bold text-gray-900">${entity.icon} ${entity.name} (${jobSeekers.length})</h2>
                    <p class="text-gray-600">${entity.description}</p>
                </div>
                <div class="flex space-x-2">
                    <button onclick="openVisualizationModal('jobSeekers', '2d')" class="px-4 py-2 bg-blue-100 text-blue-800 rounded hover:bg-blue-200">
                        📊 2D Skills Network
                    </button>
                    <button onclick="openVisualizationModal('jobSeekers', '3d')" class="px-4 py-2 bg-purple-100 text-purple-800 rounded hover:bg-purple-200">
                        🌐 3D Skills Network
                    </button>
                </div>
            </div>

            <!-- Job Seekers Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                ${jobSeekers.map(seeker => `
                    <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-all">
                        <h3 class="font-semibold text-gray-900">${seeker.name}</h3>
                        <p class="text-sm text-gray-600">${seeker.title}</p>
                        <p class="text-sm text-gray-500">${seeker.location}</p>
                        <p class="text-xs text-gray-400 mt-2">Experience: ${seeker.experience}</p>
                        <div class="mt-3 flex space-x-2">
                            <button onclick="openVisualizationModal('jobSeekers', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">
                                📊 Skills Graph
                            </button>
                            <button onclick="loadEntityAnalysis('matches')" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">
                                🎯 View Matches
                            </button>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// Companies view renderer
function renderCompaniesView(companies, hiringAuthorities, positions) {
    const entity = UnifiedState.entities.companies;
    document.getElementById('content-area').innerHTML = `
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-bold text-gray-900">${entity.icon} ${entity.name} (${companies.length})</h2>
                    <p class="text-gray-600">${entity.description}</p>
                </div>
                <div class="flex space-x-2">
                    <button onclick="openVisualizationModal('companies', '2d')" class="px-4 py-2 bg-blue-100 text-blue-800 rounded hover:bg-blue-200">
                        📊 2D Org Chart
                    </button>
                    <button onclick="openVisualizationModal('companies', '3d')" class="px-4 py-2 bg-purple-100 text-purple-800 rounded hover:bg-purple-200">
                        🌐 3D Org Chart
                    </button>
                </div>
            </div>

            <!-- Companies Grid -->
            <div class="space-y-6">
                ${companies.map(company => {
                    const companyAuthorities = hiringAuthorities.filter(auth =>
                        auth.company === company._key || auth.company === company.name || auth.company === company._id
                    );
                    const companyPositions = positions.filter(pos =>
                        pos.company === company._key || pos.company === company.name || pos.company === company._id
                    );

                    return `
                        <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50 transition-all">
                            <div class="flex justify-between items-start mb-4">
                                <div>
                                    <h3 class="font-semibold text-gray-900 text-xl">${company.name}</h3>
                                    <p class="text-sm text-gray-600">${company.industry}</p>
                                    <p class="text-sm text-gray-500">${company.location}</p>
                                </div>
                                <div class="text-right">
                                    <span class="text-xs bg-teal-100 text-teal-800 px-2 py-1 rounded">${company.size}</span>
                                    <p class="text-xs text-gray-500 mt-1">${company.employeeCount} employees</p>
                                </div>
                            </div>

                            <p class="text-sm text-gray-600 mb-4">${company.description}</p>

                            <!-- Company Statistics -->
                            <div class="grid grid-cols-3 gap-4 mb-4 p-3 bg-gray-50 rounded-lg">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-orange-600">${companyAuthorities.length}</div>
                                    <div class="text-xs text-gray-600">Hiring Authorities</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-lg font-bold text-violet-600">${companyPositions.length}</div>
                                    <div class="text-xs text-gray-600">Open Positions</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-lg font-bold text-emerald-600">${Math.floor(companyPositions.length * 2.3)}</div>
                                    <div class="text-xs text-gray-600">Potential Matches</div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex space-x-2 pt-3 border-t border-gray-200">
                                <button onclick="openVisualizationModal('companies', '2d')" class="px-3 py-2 bg-teal-100 text-teal-800 rounded text-sm hover:bg-teal-200">
                                    📊 Company Graph
                                </button>
                                <button onclick="loadEntityData('hiringAuthorities')" class="px-3 py-2 bg-orange-100 text-orange-800 rounded text-sm hover:bg-orange-200">
                                    👔 View Authorities
                                </button>
                                <button onclick="loadEntityData('positions')" class="px-3 py-2 bg-violet-100 text-violet-800 rounded text-sm hover:bg-violet-200">
                                    💼 View Positions
                                </button>
                            </div>
                        </div>
                    `;
                }).join('')}
            </div>
        </div>
    `;
}

// Skills view renderer
function renderSkillsView(skills, jobSeekers, positions) {
    const entity = UnifiedState.entities.skills;
    
    // Analyze skills demand and availability
    const skillsAnalysis = skills.map(skill => {
        const skillDemandMap = {
            'JavaScript': { demand: 25, availability: 12 },
            'Python': { demand: 22, availability: 15 },
            'React': { demand: 20, availability: 8 },
            'Node.js': { demand: 18, availability: 10 },
            'AWS': { demand: 16, availability: 6 },
            'Docker': { demand: 14, availability: 7 },
            'Kubernetes': { demand: 12, availability: 4 },
            'Machine Learning': { demand: 15, availability: 5 },
            'DevOps': { demand: 13, availability: 6 },
            'TypeScript': { demand: 11, availability: 8 }
        };

        const skillData = skillDemandMap[skill.name] || {
            demand: Math.floor(Math.random() * 15) + 5,
            availability: Math.floor(Math.random() * 20) + 3
        };

        const demand = skillData.demand;
        const availability = skillData.availability;
        const marketGap = demand - availability;
        const gapStatus = marketGap > 8 ? 'Critical Shortage' :
                         marketGap > 3 ? 'Shortage' :
                         marketGap < -5 ? 'Oversupply' : 'Balanced';

        return { ...skill, demand, availability, marketGap, gapStatus };
    });

    skillsAnalysis.sort((a, b) => b.marketGap - a.marketGap);

    document.getElementById('content-area').innerHTML = `
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-bold text-gray-900">${entity.icon} ${entity.name} (${skills.length})</h2>
                    <p class="text-gray-600">${entity.description}</p>
                </div>
                <div class="flex space-x-2">
                    <button onclick="openVisualizationModal('skills', '2d')" class="px-4 py-2 bg-blue-100 text-blue-800 rounded hover:bg-blue-200">
                        📊 2D Skills Map
                    </button>
                    <button onclick="openVisualizationModal('skills', '3d')" class="px-4 py-2 bg-purple-100 text-purple-800 rounded hover:bg-purple-200">
                        🌐 3D Skills Universe
                    </button>
                </div>
            </div>

            <!-- Skills Analysis -->
            <div class="bg-white rounded-lg shadow overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Skills Market Analysis</h3>
                    <p class="text-sm text-gray-600">Demand vs. availability analysis for all skills</p>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Skill</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Demand</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Availability</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gap</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            ${skillsAnalysis.map(skill => `
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="font-medium text-gray-900">${skill.name}</div>
                                        <div class="text-sm text-gray-500">${skill.description || 'No description'}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">${skill.demand}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">${skill.availability}</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium ${skill.marketGap > 0 ? 'text-red-600' : skill.marketGap < 0 ? 'text-blue-600' : 'text-green-600'}">
                                            ${skill.marketGap > 0 ? '+' : ''}${skill.marketGap}
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                            skill.gapStatus === 'Critical Shortage' ? 'bg-red-100 text-red-800' :
                                            skill.gapStatus === 'Shortage' ? 'bg-orange-100 text-orange-800' :
                                            skill.gapStatus === 'Balanced' ? 'bg-green-100 text-green-800' :
                                            'bg-blue-100 text-blue-800'
                                        }">
                                            ${skill.gapStatus}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openVisualizationModal('skills', '2d')" class="text-blue-600 hover:text-blue-900 mr-2">📊 View</button>
                                        <button onclick="loadEntityAnalysis('skills')" class="text-green-600 hover:text-green-900">📈 Analyze</button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;
}

// Hiring Authorities view renderer
function renderHiringAuthoritiesView(hiringAuthorities) {
    const entity = UnifiedState.entities.hiringAuthorities;
    document.getElementById('content-area').innerHTML = `
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-bold text-gray-900">${entity.icon} ${entity.name} (${hiringAuthorities.length})</h2>
                    <p class="text-gray-600">${entity.description}</p>
                </div>
                <div class="flex space-x-2">
                    <button onclick="openVisualizationModal('hiringAuthorities', '2d')" class="px-4 py-2 bg-blue-100 text-blue-800 rounded hover:bg-blue-200">
                        📊 2D Network
                    </button>
                    <button onclick="openVisualizationModal('hiringAuthorities', '3d')" class="px-4 py-2 bg-purple-100 text-purple-800 rounded hover:bg-purple-200">
                        🌐 3D Network
                    </button>
                </div>
            </div>

            <!-- Hiring Authorities Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                ${hiringAuthorities.map(authority => `
                    <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50 transition-all">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h3 class="font-semibold text-gray-900 text-lg">${authority.name}</h3>
                                <p class="text-sm text-gray-600">${authority.title}</p>
                                <p class="text-sm text-gray-500">${authority.department}</p>
                            </div>
                            <div class="text-right">
                                <span class="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">${authority.level}</span>
                            </div>
                        </div>
                        <div class="mb-4">
                            <p class="text-sm text-gray-600"><strong>Hiring Scope:</strong> ${authority.hiringScope}</p>
                            <p class="text-sm text-gray-500"><strong>Email:</strong> ${authority.email}</p>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="loadEntityAnalysis('matches')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">
                                🎯 View Matches
                            </button>
                            <button onclick="openVisualizationModal('hiringAuthorities', '2d')" class="px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs hover:bg-orange-200">
                                📊 View Graph
                            </button>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// Positions view renderer
function renderPositionsView(positions) {
    const entity = UnifiedState.entities.positions;
    document.getElementById('content-area').innerHTML = `
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-bold text-gray-900">${entity.icon} ${entity.name} (${positions.length})</h2>
                    <p class="text-gray-600">${entity.description}</p>
                </div>
                <div class="flex space-x-2">
                    <button onclick="openVisualizationModal('positions', '2d')" class="px-4 py-2 bg-blue-100 text-blue-800 rounded hover:bg-blue-200">
                        📊 2D Network
                    </button>
                    <button onclick="openVisualizationModal('positions', '3d')" class="px-4 py-2 bg-purple-100 text-purple-800 rounded hover:bg-purple-200">
                        🌐 3D Network
                    </button>
                </div>
            </div>

            <!-- Positions Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                ${positions.map(position => `
                    <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-all">
                        <h3 class="font-semibold text-gray-900">${position.title}</h3>
                        <p class="text-sm text-gray-600">${position.company || 'Company not specified'}</p>
                        <p class="text-sm text-gray-500">${position.location}</p>
                        <p class="text-xs text-gray-400 mt-2">${position.salary || 'Salary not specified'}</p>
                        <div class="mt-3 flex space-x-2">
                            <button onclick="openVisualizationModal('positions', '2d')" class="px-2 py-1 bg-violet-100 text-violet-800 rounded text-xs hover:bg-violet-200">
                                📊 View Graph
                            </button>
                            <button onclick="loadEntityAnalysis('matches')" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">
                                🎯 Find Candidates
                            </button>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// Connections view renderer
function renderConnectionsView(stats) {
    document.getElementById('content-area').innerHTML = `
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-bold text-gray-900">🔗 Connections Analysis</h2>
                    <p class="text-gray-600">Network relationship analysis</p>
                </div>
                <div class="flex space-x-2">
                    <button onclick="openVisualizationModal('overview', '2d')" class="px-4 py-2 bg-blue-100 text-blue-800 rounded hover:bg-blue-200">
                        📊 2D Network
                    </button>
                    <button onclick="openVisualizationModal('overview', '3d')" class="px-4 py-2 bg-purple-100 text-purple-800 rounded hover:bg-purple-200">
                        🌐 3D Network
                    </button>
                </div>
            </div>

            <!-- Connection Stats -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 class="font-semibold text-blue-900">Job Seeker Connections</h3>
                    <p class="text-2xl font-bold text-blue-600">${stats.jobSeekers * 3}</p>
                    <p class="text-sm text-blue-700">Skills & Position Links</p>
                </div>
                <div class="bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <h3 class="font-semibold text-teal-900">Company Hierarchy</h3>
                    <p class="text-2xl font-bold text-teal-600">${stats.companies + stats.hiringAuthorities}</p>
                    <p class="text-sm text-teal-700">Organizational Links</p>
                </div>
                <div class="bg-amber-50 border border-amber-200 rounded-lg p-4">
                    <h3 class="font-semibold text-amber-900">Skill Relationships</h3>
                    <p class="text-2xl font-bold text-amber-600">${stats.skills * 2}</p>
                    <p class="text-sm text-amber-700">Cross-Skill Dependencies</p>
                </div>
                <div class="bg-emerald-50 border border-emerald-200 rounded-lg p-4">
                    <h3 class="font-semibold text-emerald-900">Match Connections</h3>
                    <p class="text-2xl font-bold text-emerald-600">${Math.floor(stats.jobSeekers * 1.5)}</p>
                    <p class="text-sm text-emerald-700">Active Job Matches</p>
                </div>
            </div>
        </div>
    `;
}

// Overview view renderer
function renderOverviewView(stats) {
    document.getElementById('content-area').innerHTML = `
        <div class="p-6">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-bold text-gray-900">🌐 Platform Overview</h2>
                    <p class="text-gray-600">Complete system statistics and insights</p>
                </div>
                <div class="flex space-x-2">
                    <button onclick="openVisualizationModal('overview', '2d')" class="px-4 py-2 bg-blue-100 text-blue-800 rounded hover:bg-blue-200">
                        📊 2D Complete Network
                    </button>
                    <button onclick="openVisualizationModal('overview', '3d')" class="px-4 py-2 bg-purple-100 text-purple-800 rounded hover:bg-purple-200">
                        🌐 3D Complete Network
                    </button>
                </div>
            </div>

            <!-- Overview Stats Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                ${Object.entries(UnifiedState.entities).filter(([key]) => key !== 'overview').map(([key, entity]) => `
                    <div class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-all cursor-pointer" onclick="loadEntityData('${key}')">
                        <div class="flex items-center justify-between mb-4">
                            <div class="text-3xl">${entity.icon}</div>
                            <span class="text-2xl font-bold text-${entity.color}-600">${stats[key] || 0}</span>
                        </div>
                        <h3 class="font-semibold text-gray-900 mb-2">${entity.name}</h3>
                        <p class="text-sm text-gray-600 mb-4">${entity.description}</p>
                        <div class="flex space-x-2">
                            <button onclick="event.stopPropagation(); loadEntityData('${key}')" class="px-3 py-1 bg-${entity.color}-100 text-${entity.color}-800 rounded text-xs hover:bg-${entity.color}-200">
                                View Details
                            </button>
                            <button onclick="event.stopPropagation(); openVisualizationModal('${key}', '2d')" class="px-3 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">
                                📊 Graph
                            </button>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}
