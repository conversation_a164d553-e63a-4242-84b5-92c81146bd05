# 🤝 Candid Connections

An intelligent job matching platform powered by ArangoDB graph database technology. This application connects job seekers with the perfect opportunities through advanced relationship mapping and intelligent matching algorithms.

## 🌟 Features

### Core Functionality
- **Intelligent Job Matching**: Advanced algorithm that matches job seekers with positions based on skills, experience, and location
- **Graph Database**: Leverages ArangoDB to model complex relationships between entities
- **Interactive Visualizations**: 2D and 3D graph visualizations using React Force Graph
- **Company Hierarchy Modeling**: Intelligent routing to appropriate hiring authorities based on company size
- **CRUD Operations**: Full data management capabilities with intuitive admin interface

### Key Components
- **Dashboard**: Overview of all entities with real-time statistics
- **Data Management**: Comprehensive CRUD interface for all entities
- **Graph Visualization**: Interactive 2D/3D network graphs showing relationships
- **Job Matching**: Intelligent matching system with detailed scoring
- **Entity Detail Pages**: Individual pages for job seekers, companies, positions, and hiring authorities

## 🏗️ Architecture

### Technology Stack
- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Database**: ArangoDB (Graph Database)
- **Visualization**: React Force Graph (2D/3D), D3.js
- **Deployment**: AWS Amplify Gen 2 ready
- **Styling**: Tailwind CSS with responsive design

### Database Schema

```mermaid
graph TB
    JS[Job Seekers] -->|has skill| S[Skills]
    C[Companies] -->|employs| HA[Hiring Authorities]
    HA -->|manages| P[Positions]
    P -->|requires| S

    subgraph "Document Collections"
        JS
        C
        HA
        P
        S
    end

    subgraph "Edge Collections"
        SS[seekerSkills]
        CHA[companyHiringAuthorities]
        HAP[hiringAuthorityPositions]
        PS[positionSkills]
        M[matches]
    end
```

### Project Structure

```
fresh-project/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API routes
│   │   │   └── matches/       # Job matching API
│   │   ├── companies/[id]/    # Company detail pages
│   │   ├── job-seekers/[id]/  # Job seeker detail pages
│   │   ├── positions/[id]/    # Position detail pages
│   │   ├── data/              # Data management page
│   │   ├── graph/             # Graph visualization page
│   │   ├── matches/           # Job matching results page
│   │   └── page.tsx           # Dashboard
│   ├── components/            # Reusable components
│   │   ├── DataManager.tsx    # CRUD interface
│   │   └── GraphVisualization.tsx # Graph component
│   └── lib/                   # Utilities and services
│       ├── db/                # Database utilities
│       └── matching.ts        # Job matching algorithm
├── scripts/                   # Database and setup scripts
├── public/                    # Static assets
└── docs/                      # Documentation
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- ArangoDB instance (cloud or local)

### Installation

1. **Clone and setup**:
```bash
git clone <repository-url>
cd fresh-project
npm install
```

2. **Database Setup**:
```bash
# Initialize database and collections
node scripts/test-and-init-db.js

# Seed with sample data
node scripts/reset-and-seed.js
```

3. **Environment Configuration**:
Create a `.env.local` file:
```env
NEXT_PUBLIC_ARANGO_URL=your_arango_url
NEXT_PUBLIC_ARANGO_USERNAME=your_username
NEXT_PUBLIC_ARANGO_PASSWORD=your_password
NEXT_PUBLIC_ARANGO_DB_NAME=candid_connections
```

4. **Start Development Server**:
```bash
npm run dev
# or
npx next dev
```

Visit `http://localhost:3000` to see the application.

## 📊 Data Model

### Entities

#### Job Seekers
- Personal information (name, email, location)
- Experience level and current title
- Skills with proficiency levels

#### Companies
- Company details (name, industry, size)
- Employee count for hiring hierarchy determination
- Location and description

#### Hiring Authorities
- Contact information and role
- Hiring scope and department
- Authority level (CEO, VP, Director, Manager)

#### Positions
- Job details (title, level, location, salary)
- Associated company
- Required and preferred skills

#### Skills
- Skill name and category
- Used for matching algorithms

### Relationships

#### Company Hierarchy
- **Small companies** (<100 employees): Direct to CEO/Founder
- **Medium companies** (100-500): Department heads and CTO
- **Large companies** (500-1000): HR managers with department heads
- **Enterprise** (1000+): Structured HR hierarchy

#### Matching Algorithm
The intelligent matching system considers:
- **Skill Matching** (70% weight): Required vs. preferred skills with proficiency levels
- **Location Matching** (15% weight): Geographic compatibility
- **Experience Matching** (15% weight): Seniority level alignment

## 🎯 Usage

### Dashboard
- View real-time statistics
- Quick navigation to all features
- System health indicators

### Data Management
- Browse all entities with tabbed interface
- Edit records inline with modal forms
- Clickable names link to detail pages
- Bulk operations support

### Graph Visualization
- Toggle between 2D and 3D views
- Interactive node exploration
- Color-coded entity types
- Relationship visualization

### Job Matching
- Automatic matching algorithm
- Detailed match scoring
- Skill-by-skill analysis
- Hiring authority routing

### Entity Detail Pages
- Comprehensive entity information
- Related data and connections
- Action buttons for common tasks
- Match recommendations

## 🔧 Available Scripts

### Database Operations
```bash
npm run db:init          # Initialize database
npm run db:seed          # Seed sample data
npm run db:reset         # Reset and reseed
npm run db:setup-all     # Complete setup
```

### Development
```bash
npm run dev              # Start development server
npm run build            # Build for production
npm run start            # Start production server
npm run lint             # Run ESLint
```

### Deployment
```bash
npm run amplify:setup    # Setup AWS Amplify
npm run amplify:deploy   # Deploy to AWS
npm run amplify:domain   # Configure custom domain
```

## 🌐 Deployment

### AWS Amplify Gen 2
The application is configured for AWS Amplify deployment:

1. **Setup Amplify**:
```bash
npm run amplify:setup
```

2. **Configure Environment**:
- Set up ArangoDB credentials in Amplify console
- Configure custom domain (candid.pbradygeorgen.com)

3. **Deploy**:
```bash
npm run amplify:deploy
```

### Manual Deployment
For other platforms:
1. Build the application: `npm run build`
2. Deploy the `out/` directory to your hosting platform
3. Configure environment variables

## 🧪 Testing

### Local Testing
1. Ensure database is running and seeded
2. Start development server
3. Navigate through all features:
   - Dashboard statistics
   - Data management CRUD operations
   - Graph visualization (2D/3D)
   - Job matching results
   - Entity detail pages

### Production Testing
1. Deploy to staging environment
2. Test database connectivity
3. Verify all API endpoints
4. Test responsive design
5. Performance testing

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions:
1. Check the documentation
2. Review existing issues
3. Create a new issue with detailed description

## 🔮 Future Enhancements

- Real-time notifications
- Advanced filtering and search
- Machine learning recommendations
- Integration with job boards
- Mobile application
- Advanced analytics dashboard
