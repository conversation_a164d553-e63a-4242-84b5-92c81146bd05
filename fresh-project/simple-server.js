#!/usr/bin/env node

const express = require('express');
const { Database } = require('arangojs');
const path = require('path');

const app = express();
const PORT = 3002;

// Database connection
const db = new Database({
  url: 'https://f2c8a97499a8.arangodb.cloud:8529',
  auth: {
    username: 'root',
    password: 'XMx2qSsHU8RWMX9VSxAx'
  },
  databaseName: 'candid_connections'
});

app.use(express.json());
app.use(express.static('public'));

// API Routes

// Get dashboard stats
app.get('/api/stats', async (req, res) => {
  try {
    const [jobSeekers, companies, hiringAuthorities, positions, skills, connections] = await Promise.all([
      db.collection('jobSeekers').count(),
      db.collection('companies').count(),
      db.collection('hiringAuthorities').count(),
      db.collection('positions').count(),
      db.collection('skills').count(),
      db.collection('seekerSkills').count()
    ]);

    res.json({
      success: true,
      data: {
        jobSeekers: jobSeekers.count,
        companies: companies.count,
        hiringAuthorities: hiringAuthorities.count,
        positions: positions.count,
        skills: skills.count,
        connections: connections.count
      }
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get all job seekers
app.get('/api/job-seekers', async (req, res) => {
  try {
    const cursor = await db.query('FOR doc IN jobSeekers RETURN doc');
    const jobSeekers = await cursor.all();
    res.json({ success: true, data: jobSeekers });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get all companies
app.get('/api/companies', async (req, res) => {
  try {
    const cursor = await db.query('FOR doc IN companies RETURN doc');
    const companies = await cursor.all();
    res.json({ success: true, data: companies });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get all hiring authorities
app.get('/api/hiring-authorities', async (req, res) => {
  try {
    const cursor = await db.query('FOR doc IN hiringAuthorities RETURN doc');
    const hiringAuthorities = await cursor.all();
    res.json({ success: true, data: hiringAuthorities });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get all positions
app.get('/api/positions', async (req, res) => {
  try {
    const cursor = await db.query('FOR doc IN positions RETURN doc');
    const positions = await cursor.all();
    res.json({ success: true, data: positions });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get graph data for visualization
app.get('/api/graph', async (req, res) => {
  try {
    const { focus, type } = req.query;

    // Get all entities
    const [jobSeekers, companies, hiringAuthorities, positions, skills] = await Promise.all([
      db.query('FOR doc IN jobSeekers RETURN doc').then(cursor => cursor.all()),
      db.query('FOR doc IN companies RETURN doc').then(cursor => cursor.all()),
      db.query('FOR doc IN hiringAuthorities RETURN doc').then(cursor => cursor.all()),
      db.query('FOR doc IN positions RETURN doc').then(cursor => cursor.all()),
      db.query('FOR doc IN skills RETURN doc').then(cursor => cursor.all())
    ]);

    // Get all relationships
    const [seekerSkills, positionSkills, companyHiring, hiringPositions] = await Promise.all([
      db.query('FOR doc IN seekerSkills RETURN doc').then(cursor => cursor.all()),
      db.query('FOR doc IN positionSkills RETURN doc').then(cursor => cursor.all()),
      db.query('FOR doc IN companyHiringAuthorities RETURN doc').then(cursor => cursor.all()),
      db.query('FOR doc IN hiringAuthorityPositions RETURN doc').then(cursor => cursor.all())
    ]);

    // Create nodes
    let nodes = [];
    let links = [];

    // Add nodes based on focus
    if (!focus || type === 'overview') {
      // Overview mode - show all entities
      nodes = [
        ...jobSeekers.map(js => ({
          id: js._id,
          name: js.name,
          type: 'jobSeeker',
          color: '#3B82F6',
          size: 8,
          data: js
        })),
        ...companies.map(c => ({
          id: c._id,
          name: c.name,
          type: 'company',
          color: '#10B981',
          size: 12,
          data: c
        })),
        ...hiringAuthorities.map(ha => ({
          id: ha._id,
          name: ha.name,
          type: 'hiringAuthority',
          color: '#F97316',
          size: 6,
          data: ha
        })),
        ...positions.map(p => ({
          id: p._id,
          name: p.title,
          type: 'position',
          color: '#8B5CF6',
          size: 10,
          data: p
        })),
        ...skills.map(s => ({
          id: s._id,
          name: s.name,
          type: 'skill',
          color: '#F59E0B',
          size: 4,
          data: s
        }))
      ];

      // Add all relationships
      links = [
        ...seekerSkills.map(edge => ({
          source: edge._from,
          target: edge._to,
          type: 'hasSkill',
          label: edge.proficiency,
          color: '#94A3B8'
        })),
        ...positionSkills.map(edge => ({
          source: edge._from,
          target: edge._to,
          type: 'requiresSkill',
          label: edge.required ? 'Required' : 'Preferred',
          color: edge.required ? '#EF4444' : '#94A3B8'
        })),
        ...companyHiring.map(edge => ({
          source: edge._from,
          target: edge._to,
          type: 'employs',
          label: 'employs',
          color: '#10B981'
        })),
        ...hiringPositions.map(edge => ({
          source: edge._from,
          target: edge._to,
          type: 'manages',
          label: 'manages',
          color: '#8B5CF6'
        }))
      ];
    } else {
      // Focused mode - show specific entity and connections
      const focusEntity = [...jobSeekers, ...companies, ...positions, ...hiringAuthorities].find(e => e._id === focus);

      if (focusEntity) {
        nodes.push({
          id: focusEntity._id,
          name: focusEntity.name || focusEntity.title,
          type: type,
          color: type === 'jobSeeker' ? '#3B82F6' : type === 'company' ? '#10B981' : type === 'position' ? '#8B5CF6' : '#F97316',
          size: 15,
          data: focusEntity,
          fx: 0,
          fy: 0
        });

        // Add connected entities based on type
        if (type === 'jobSeeker') {
          // Show skills, potential positions, and companies
          const relatedSkills = seekerSkills.filter(edge => edge._from === focus);
          const skillIds = relatedSkills.map(edge => edge._to);

          // Add skills
          skills.filter(s => skillIds.includes(s._id)).forEach(skill => {
            nodes.push({
              id: skill._id,
              name: skill.name,
              type: 'skill',
              color: '#F59E0B',
              size: 6,
              data: skill
            });
          });

          // Add skill connections
          relatedSkills.forEach(edge => {
            links.push({
              source: edge._from,
              target: edge._to,
              type: 'hasSkill',
              label: edge.proficiency,
              color: '#94A3B8'
            });
          });

          // Find positions that require these skills
          const relatedPositions = positionSkills.filter(edge => skillIds.includes(edge._to));
          const positionIds = [...new Set(relatedPositions.map(edge => edge._from))];

          positions.filter(p => positionIds.includes(p._id)).forEach(position => {
            nodes.push({
              id: position._id,
              name: position.title,
              type: 'position',
              color: '#8B5CF6',
              size: 10,
              data: position
            });
          });

          // Add position-skill connections
          relatedPositions.forEach(edge => {
            if (skillIds.includes(edge._to)) {
              links.push({
                source: edge._from,
                target: edge._to,
                type: 'requiresSkill',
                label: edge.required ? 'Required' : 'Preferred',
                color: edge.required ? '#EF4444' : '#94A3B8'
              });
            }
          });

          // Add companies for these positions
          const companyIds = [...new Set(positions.filter(p => positionIds.includes(p._id)).map(p => `companies/${p.company}`))];
          companies.filter(c => companyIds.includes(c._id)).forEach(company => {
            nodes.push({
              id: company._id,
              name: company.name,
              type: 'company',
              color: '#10B981',
              size: 12,
              data: company
            });
          });
        } else if (type === 'company') {
          // Show hiring authorities, positions, and related job seekers
          const companyKey = focusEntity._key;

          // Add hiring authorities
          const companyHiringRels = companyHiring.filter(edge => edge._from === focus);
          const hiringAuthorityIds = companyHiringRels.map(edge => edge._to);

          hiringAuthorities.filter(ha => hiringAuthorityIds.includes(ha._id)).forEach(ha => {
            nodes.push({
              id: ha._id,
              name: ha.name,
              type: 'hiringAuthority',
              color: '#F97316',
              size: 8,
              data: ha
            });
          });

          // Add company-hiring authority connections
          companyHiringRels.forEach(edge => {
            links.push({
              source: edge._from,
              target: edge._to,
              type: 'employs',
              label: 'employs',
              color: '#10B981'
            });
          });

          // Add positions for this company
          const companyPositions = positions.filter(p => p.company === companyKey);
          companyPositions.forEach(position => {
            nodes.push({
              id: position._id,
              name: position.title,
              type: 'position',
              color: '#8B5CF6',
              size: 10,
              data: position
            });
          });

          // Add hiring authority-position connections
          const hiringPositionRels = hiringPositions.filter(edge =>
            hiringAuthorityIds.includes(edge._from) &&
            companyPositions.some(p => p._id === edge._to)
          );

          hiringPositionRels.forEach(edge => {
            links.push({
              source: edge._from,
              target: edge._to,
              type: 'manages',
              label: 'manages',
              color: '#8B5CF6'
            });
          });

          // Add skills required by these positions
          const positionIds = companyPositions.map(p => p._id);
          const relatedPositionSkills = positionSkills.filter(edge => positionIds.includes(edge._from));
          const skillIds = [...new Set(relatedPositionSkills.map(edge => edge._to))];

          skills.filter(s => skillIds.includes(s._id)).forEach(skill => {
            nodes.push({
              id: skill._id,
              name: skill.name,
              type: 'skill',
              color: '#F59E0B',
              size: 6,
              data: skill
            });
          });

          // Add position-skill connections
          relatedPositionSkills.forEach(edge => {
            links.push({
              source: edge._from,
              target: edge._to,
              type: 'requiresSkill',
              label: edge.required ? 'Required' : 'Preferred',
              color: edge.required ? '#EF4444' : '#94A3B8'
            });
          });

          // Add job seekers who have these skills
          const relatedSeekerSkills = seekerSkills.filter(edge => skillIds.includes(edge._to));
          const seekerIds = [...new Set(relatedSeekerSkills.map(edge => edge._from))];

          jobSeekers.filter(js => seekerIds.includes(js._id)).forEach(seeker => {
            nodes.push({
              id: seeker._id,
              name: seeker.name,
              type: 'jobSeeker',
              color: '#3B82F6',
              size: 8,
              data: seeker
            });
          });

          // Add seeker-skill connections
          relatedSeekerSkills.forEach(edge => {
            links.push({
              source: edge._from,
              target: edge._to,
              type: 'hasSkill',
              label: edge.proficiency,
              color: '#94A3B8'
            });
          });
        } else if (type === 'position') {
          // Show company, hiring authority, required skills, and matching job seekers
          const position = focusEntity;

          // Add company
          const company = companies.find(c => c._key === position.company);
          if (company) {
            nodes.push({
              id: company._id,
              name: company.name,
              type: 'company',
              color: '#10B981',
              size: 12,
              data: company
            });
          }

          // Add hiring authority
          const hiringAuthority = hiringPositions.find(edge => edge._to === focus);
          if (hiringAuthority) {
            const ha = hiringAuthorities.find(h => h._id === hiringAuthority._from);
            if (ha) {
              nodes.push({
                id: ha._id,
                name: ha.name,
                type: 'hiringAuthority',
                color: '#F97316',
                size: 8,
                data: ha
              });

              links.push({
                source: ha._id,
                target: focus,
                type: 'manages',
                label: 'manages',
                color: '#8B5CF6'
              });
            }
          }

          // Add required skills
          const positionSkillRels = positionSkills.filter(edge => edge._from === focus);
          const skillIds = positionSkillRels.map(edge => edge._to);

          skills.filter(s => skillIds.includes(s._id)).forEach(skill => {
            nodes.push({
              id: skill._id,
              name: skill.name,
              type: 'skill',
              color: '#F59E0B',
              size: 6,
              data: skill
            });
          });

          // Add position-skill connections
          positionSkillRels.forEach(edge => {
            links.push({
              source: edge._from,
              target: edge._to,
              type: 'requiresSkill',
              label: edge.required ? 'Required' : 'Preferred',
              color: edge.required ? '#EF4444' : '#94A3B8'
            });
          });

          // Add matching job seekers
          const relatedSeekerSkills = seekerSkills.filter(edge => skillIds.includes(edge._to));
          const seekerIds = [...new Set(relatedSeekerSkills.map(edge => edge._from))];

          jobSeekers.filter(js => seekerIds.includes(js._id)).forEach(seeker => {
            nodes.push({
              id: seeker._id,
              name: seeker.name,
              type: 'jobSeeker',
              color: '#3B82F6',
              size: 8,
              data: seeker
            });
          });

          // Add seeker-skill connections
          relatedSeekerSkills.forEach(edge => {
            links.push({
              source: edge._from,
              target: edge._to,
              type: 'hasSkill',
              label: edge.proficiency,
              color: '#94A3B8'
            });
          });
        }
      }
    }

    res.json({
      success: true,
      data: { nodes, links },
      focus: focus,
      type: type
    });

  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get job matches
app.get('/api/matches', async (req, res) => {
  try {
    const matchQuery = `
      WITH skills, companies, hiringAuthorities
      FOR seeker IN jobSeekers
        FOR position IN positions
          LET company = FIRST(FOR c IN companies FILTER c._key == position.company RETURN c)
          LET hiringAuthority = FIRST(
            FOR ha IN hiringAuthorities
            FILTER ha.company == position.company
            RETURN ha
          )
          LET seekerSkills = (
            FOR skill, edge IN 1..1 OUTBOUND seeker seekerSkills
              RETURN {
                skill: skill.name,
                proficiency: edge.proficiency
              }
          )
          LET positionSkills = (
            FOR skill, edge IN 1..1 OUTBOUND position positionSkills
              RETURN {
                skill: skill.name,
                required: edge.required,
                level: edge.level
              }
          )
          LET skillMatches = (
            FOR posReq IN positionSkills
              LET seekerSkill = FIRST(FOR s IN seekerSkills FILTER s.skill == posReq.skill RETURN s)
              RETURN {
                skill: posReq.skill,
                seekerProficiency: seekerSkill ? seekerSkill.proficiency : "None",
                requiredLevel: posReq.level,
                match: seekerSkill != null,
                weight: posReq.required ? 3 : 1
              }
          )
          LET totalScore = SUM(FOR sm IN skillMatches FILTER sm.match RETURN sm.weight)
          LET maxScore = SUM(FOR sm IN skillMatches RETURN sm.weight)
          LET score = maxScore > 0 ? ROUND((totalScore / maxScore) * 100) : 0
          FILTER score > 0
          SORT score DESC
          LIMIT 20
          RETURN {
            jobSeeker: seeker,
            position: position,
            company: company,
            hiringAuthority: hiringAuthority,
            score: score,
            skillMatches: skillMatches,
            locationMatch: seeker.location == position.location,
            experienceMatch: true
          }
    `;

    const cursor = await db.query(matchQuery);
    const matches = await cursor.all();

    res.json({ success: true, data: matches });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Serve the main page
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🤝 Candid Connections - Job Matching Platform</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <style>
            .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        </style>
    </head>
    <body class="bg-gray-50">
        <div class="min-h-screen">
            <!-- Header -->
            <header class="gradient-bg text-white shadow-lg">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between items-center py-6">
                        <h1 class="text-3xl font-bold">🤝 Candid Connections</h1>
                        <p class="text-lg">Intelligent Job Matching Platform</p>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div class="px-4 py-6 sm:px-0">
                    <!-- Stats Grid -->
                    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-6 mb-8" id="stats-grid">
                        <!-- Stats will be loaded here -->
                    </div>

                    <!-- Navigation Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
                        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadJobSeekers()">
                            <div class="text-3xl mb-3">👤</div>
                            <h3 class="text-lg font-semibold text-gray-900">Job Seekers</h3>
                            <p class="text-sm text-gray-600">View all job seekers and their profiles</p>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadCompanies()">
                            <div class="text-3xl mb-3">🏢</div>
                            <h3 class="text-lg font-semibold text-gray-900">Companies</h3>
                            <p class="text-sm text-gray-600">Browse companies and open positions</p>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadPositions()">
                            <div class="text-3xl mb-3">💼</div>
                            <h3 class="text-lg font-semibold text-gray-900">Positions</h3>
                            <p class="text-sm text-gray-600">View all available job positions</p>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadMatches()">
                            <div class="text-3xl mb-3">🎯</div>
                            <h3 class="text-lg font-semibold text-gray-900">Job Matches</h3>
                            <p class="text-sm text-gray-600">See intelligent job matching results</p>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadGraphVisualization()">
                            <div class="text-3xl mb-3">🌐</div>
                            <h3 class="text-lg font-semibold text-gray-900">Graph Visualization</h3>
                            <p class="text-sm text-gray-600">Interactive network graph of connections</p>
                        </div>
                    </div>

                    <!-- Content Area -->
                    <div class="bg-white rounded-lg shadow" id="content-area">
                        <div class="p-6">
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">Welcome to Candid Connections</h2>
                            <p class="text-gray-600 mb-4">
                                This intelligent job matching platform uses advanced graph database technology to connect
                                job seekers with the perfect opportunities through relationship mapping and smart algorithms.
                            </p>
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <h3 class="font-semibold text-blue-900 mb-2">🚀 Platform Features:</h3>
                                <ul class="text-blue-800 space-y-1">
                                    <li>• Intelligent job matching based on skills, experience, and location</li>
                                    <li>• Company hierarchy modeling for appropriate hiring authority routing</li>
                                    <li>• Graph database relationships for complex data modeling</li>
                                    <li>• Real-time statistics and comprehensive data management</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <script>
            // Load dashboard stats
            async function loadStats() {
                try {
                    const response = await fetch('/api/stats');
                    const data = await response.json();

                    if (data.success) {
                        const stats = data.data;
                        document.getElementById('stats-grid').innerHTML = \`
                            <div class="bg-white overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="text-3xl">👤</div>
                                        <div class="ml-5">
                                            <dt class="text-sm font-medium text-gray-500">Job Seekers</dt>
                                            <dd class="text-lg font-medium text-gray-900">\${stats.jobSeekers}</dd>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="text-3xl">🏢</div>
                                        <div class="ml-5">
                                            <dt class="text-sm font-medium text-gray-500">Companies</dt>
                                            <dd class="text-lg font-medium text-gray-900">\${stats.companies}</dd>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="text-3xl">👔</div>
                                        <div class="ml-5">
                                            <dt class="text-sm font-medium text-gray-500">Hiring Authorities</dt>
                                            <dd class="text-lg font-medium text-gray-900">\${stats.hiringAuthorities}</dd>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="text-3xl">💼</div>
                                        <div class="ml-5">
                                            <dt class="text-sm font-medium text-gray-500">Positions</dt>
                                            <dd class="text-lg font-medium text-gray-900">\${stats.positions}</dd>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="text-3xl">🎯</div>
                                        <div class="ml-5">
                                            <dt class="text-sm font-medium text-gray-500">Skills</dt>
                                            <dd class="text-lg font-medium text-gray-900">\${stats.skills}</dd>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="text-3xl">🔗</div>
                                        <div class="ml-5">
                                            <dt class="text-sm font-medium text-gray-500">Connections</dt>
                                            <dd class="text-lg font-medium text-gray-900">\${stats.connections}</dd>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        \`;
                    }
                } catch (error) {
                    console.error('Error loading stats:', error);
                }
            }

            // Load job seekers
            async function loadJobSeekers() {
                try {
                    const response = await fetch('/api/job-seekers');
                    const data = await response.json();

                    if (data.success) {
                        const jobSeekers = data.data;
                        document.getElementById('content-area').innerHTML = \`
                            <div class="p-6">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">👤 Job Seekers (\${jobSeekers.length})</h2>
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    \${jobSeekers.map(seeker => \`
                                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                                            <h3 class="font-semibold text-gray-900">\${seeker.name}</h3>
                                            <p class="text-sm text-gray-600">\${seeker.title}</p>
                                            <p class="text-sm text-gray-500">\${seeker.location}</p>
                                            <p class="text-xs text-gray-400 mt-2">Experience: \${seeker.experience}</p>
                                            <div class="mt-3 flex space-x-2">
                                                <button onclick="loadGraphVisualization('\${seeker._id}', 'jobSeeker')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">
                                                    🌐 View Graph
                                                </button>
                                                <button onclick="loadMatchesForSeeker('\${seeker._id}')" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">
                                                    🎯 View Matches
                                                </button>
                                            </div>
                                        </div>
                                    \`).join('')}
                                </div>
                            </div>
                        \`;
                    }
                } catch (error) {
                    console.error('Error loading job seekers:', error);
                }
            }

            // Load companies
            async function loadCompanies() {
                try {
                    const response = await fetch('/api/companies');
                    const data = await response.json();

                    if (data.success) {
                        const companies = data.data;
                        document.getElementById('content-area').innerHTML = \`
                            <div class="p-6">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">🏢 Companies (\${companies.length})</h2>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    \${companies.map(company => \`
                                        <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                                            <h3 class="font-semibold text-gray-900 text-lg">\${company.name}</h3>
                                            <p class="text-sm text-gray-600 mb-2">\${company.industry}</p>
                                            <p class="text-sm text-gray-500 mb-2">\${company.location}</p>
                                            <div class="flex justify-between items-center mb-3">
                                                <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">\${company.size}</span>
                                                <span class="text-xs text-gray-500">\${company.employeeCount} employees</span>
                                            </div>
                                            <p class="text-sm text-gray-600 mb-3">\${company.description}</p>
                                            <div class="flex space-x-2">
                                                <button onclick="loadGraphVisualization('\${company._id}', 'company')" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">
                                                    🌐 View Graph
                                                </button>
                                                <button onclick="loadMatchesForCompany('\${company._id}')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200">
                                                    🎯 View Candidates
                                                </button>
                                            </div>
                                        </div>
                                    \`).join('')}
                                </div>
                            </div>
                        \`;
                    }
                } catch (error) {
                    console.error('Error loading companies:', error);
                }
            }

            // Load positions
            async function loadPositions() {
                try {
                    const response = await fetch('/api/positions');
                    const data = await response.json();

                    if (data.success) {
                        const positions = data.data;
                        document.getElementById('content-area').innerHTML = \`
                            <div class="p-6">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">💼 Open Positions (\${positions.length})</h2>
                                <div class="space-y-4">
                                    \${positions.map(position => \`
                                        <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                                            <div class="flex justify-between items-start">
                                                <div>
                                                    <h3 class="font-semibold text-gray-900 text-lg">\${position.title}</h3>
                                                    <p class="text-sm text-gray-600">\${position.company}</p>
                                                    <p class="text-sm text-gray-500">\${position.location}</p>
                                                </div>
                                                <div class="text-right">
                                                    <span class="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">\${position.level}</span>
                                                    <p class="text-sm font-medium text-green-600 mt-1">\${position.salary}</p>
                                                </div>
                                            </div>
                                        </div>
                                    \`).join('')}
                                </div>
                            </div>
                        \`;
                    }
                } catch (error) {
                    console.error('Error loading positions:', error);
                }
            }

            // Load matches
            async function loadMatches() {
                document.getElementById('content-area').innerHTML = \`
                    <div class="p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">🎯 Loading Job Matches...</h2>
                        <div class="animate-pulse">
                            <div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                            <div class="h-4 bg-gray-200 rounded w-1/2"></div>
                        </div>
                    </div>
                \`;

                try {
                    const response = await fetch('/api/matches');
                    const data = await response.json();

                    if (data.success) {
                        const matches = data.data;
                        document.getElementById('content-area').innerHTML = \`
                            <div class="p-6">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">🎯 Job Matches (\${matches.length})</h2>
                                <div class="space-y-4">
                                    \${matches.map(match => \`
                                        <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                                            <div class="flex justify-between items-start mb-4">
                                                <div>
                                                    <h3 class="font-semibold text-gray-900">\${match.jobSeeker.name}</h3>
                                                    <p class="text-sm text-gray-600">\${match.jobSeeker.title}</p>
                                                    <p class="text-sm text-gray-500">\${match.jobSeeker.location}</p>
                                                </div>
                                                <div class="text-right">
                                                    <span class="text-2xl font-bold \${match.score >= 80 ? 'text-green-600' : match.score >= 60 ? 'text-yellow-600' : 'text-red-600'}">\${match.score}%</span>
                                                    <p class="text-xs text-gray-500">Match Score</p>
                                                </div>
                                            </div>
                                            <div class="border-t pt-4">
                                                <h4 class="font-medium text-gray-900">\${match.position.title}</h4>
                                                <p class="text-sm text-gray-600">\${match.company.name}</p>
                                                <p class="text-sm text-gray-500">\${match.position.location} • \${match.position.salary}</p>
                                            </div>
                                            <div class="mt-4 pt-4 border-t">
                                                <p class="text-sm text-gray-600">
                                                    <span class="font-medium">Contact:</span> \${match.hiringAuthority ? match.hiringAuthority.name : 'N/A'}
                                                    \${match.hiringAuthority ? '(' + match.hiringAuthority.title + ')' : ''}
                                                </p>
                                            </div>
                                        </div>
                                    \`).join('')}
                                </div>
                            </div>
                        \`;
                    }
                } catch (error) {
                    console.error('Error loading matches:', error);
                    document.getElementById('content-area').innerHTML = \`
                        <div class="p-6">
                            <h2 class="text-2xl font-bold text-red-600 mb-4">❌ Error Loading Matches</h2>
                            <p class="text-gray-600">There was an error loading the job matches. Please try again.</p>
                        </div>
                    \`;
                }
            }

            // Load graph visualization
            async function loadGraphVisualization(focus = null, type = 'overview') {
                document.getElementById('content-area').innerHTML = \`
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-2xl font-bold text-gray-900">🌐 Graph Visualization</h2>
                            <div class="flex space-x-2">
                                <button onclick="loadGraphVisualization(null, 'overview')" class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded hover:bg-blue-200">
                                    Overview
                                </button>
                                <button onclick="showGraphControls()" class="px-3 py-1 text-sm bg-gray-100 text-gray-800 rounded hover:bg-gray-200">
                                    Focus Mode
                                </button>
                            </div>
                        </div>

                        <!-- Graph Controls -->
                        <div id="graph-controls" class="mb-4 p-4 bg-gray-50 rounded-lg" style="display: none;">
                            <h3 class="font-semibold mb-3">Focus on Entity:</h3>
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Job Seekers</label>
                                    <select id="jobseeker-select" class="w-full p-2 border border-gray-300 rounded" onchange="focusOnEntity(this.value, 'jobSeeker')">
                                        <option value="">Select Job Seeker...</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Companies</label>
                                    <select id="company-select" class="w-full p-2 border border-gray-300 rounded" onchange="focusOnEntity(this.value, 'company')">
                                        <option value="">Select Company...</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Positions</label>
                                    <select id="position-select" class="w-full p-2 border border-gray-300 rounded" onchange="focusOnEntity(this.value, 'position')">
                                        <option value="">Select Position...</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Hiring Authorities</label>
                                    <select id="hiring-select" class="w-full p-2 border border-gray-300 rounded" onchange="focusOnEntity(this.value, 'hiringAuthority')">
                                        <option value="">Select Authority...</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Legend -->
                        <div class="mb-4 p-4 bg-gray-50 rounded-lg">
                            <h3 class="font-semibold mb-3">Legend:</h3>
                            <div class="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-blue-500 rounded-full mr-2"></div>
                                    <span>Job Seekers</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-green-500 rounded-full mr-2"></div>
                                    <span>Companies</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-orange-500 rounded-full mr-2"></div>
                                    <span>Hiring Authorities</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-purple-500 rounded-full mr-2"></div>
                                    <span>Positions</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-yellow-500 rounded-full mr-2"></div>
                                    <span>Skills</span>
                                </div>
                            </div>
                        </div>

                        <!-- Graph Container -->
                        <div class="border border-gray-200 rounded-lg bg-white" style="height: 600px;">
                            <div id="graph-container" style="width: 100%; height: 100%;"></div>
                        </div>

                        <!-- Node Details Panel -->
                        <div id="node-details" class="mt-4 p-4 bg-blue-50 rounded-lg" style="display: none;">
                            <h3 class="font-semibold text-blue-900 mb-2">Node Details</h3>
                            <div id="node-details-content"></div>
                        </div>
                    </div>
                \`;

                // Load graph data and render
                await loadAndRenderGraph(focus, type);

                // Populate dropdowns for focus mode
                await populateGraphControls();
            }

            async function loadAndRenderGraph(focus = null, type = 'overview') {
                try {
                    const url = focus ? \`/api/graph?focus=\${focus}&type=\${type}\` : '/api/graph?type=overview';
                    const response = await fetch(url);
                    const data = await response.json();

                    if (data.success) {
                        renderGraph(data.data);
                    } else {
                        console.error('Failed to load graph data:', data.error);
                    }
                } catch (error) {
                    console.error('Error loading graph:', error);
                }
            }

            function renderGraph(graphData) {
                const container = document.getElementById('graph-container');
                container.innerHTML = ''; // Clear previous graph

                // Create SVG
                const width = container.clientWidth;
                const height = container.clientHeight;

                const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                svg.setAttribute('width', width);
                svg.setAttribute('height', height);
                svg.style.background = '#f8fafc';
                container.appendChild(svg);

                // Simple force simulation using basic positioning
                const nodes = graphData.nodes.map(d => ({...d}));
                const links = graphData.links.map(d => ({...d}));

                // Position nodes in a circle or grid
                const centerX = width / 2;
                const centerY = height / 2;
                const radius = Math.min(width, height) / 3;

                if (nodes.length === 1) {
                    // Single node in center
                    nodes[0].x = centerX;
                    nodes[0].y = centerY;
                } else {
                    // Arrange nodes in a circle
                    nodes.forEach((node, i) => {
                        if (node.fx !== undefined && node.fy !== undefined) {
                            node.x = centerX + node.fx * 100;
                            node.y = centerY + node.fy * 100;
                        } else {
                            const angle = (i / nodes.length) * 2 * Math.PI;
                            node.x = centerX + Math.cos(angle) * radius;
                            node.y = centerY + Math.sin(angle) * radius;
                        }
                    });
                }

                // Draw links
                links.forEach(link => {
                    const sourceNode = nodes.find(n => n.id === link.source);
                    const targetNode = nodes.find(n => n.id === link.target);

                    if (sourceNode && targetNode) {
                        const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                        line.setAttribute('x1', sourceNode.x);
                        line.setAttribute('y1', sourceNode.y);
                        line.setAttribute('x2', targetNode.x);
                        line.setAttribute('y2', targetNode.y);
                        line.setAttribute('stroke', link.color || '#94A3B8');
                        line.setAttribute('stroke-width', '2');
                        line.setAttribute('opacity', '0.6');
                        svg.appendChild(line);

                        // Add link label
                        if (link.label) {
                            const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                            text.setAttribute('x', (sourceNode.x + targetNode.x) / 2);
                            text.setAttribute('y', (sourceNode.y + targetNode.y) / 2);
                            text.setAttribute('text-anchor', 'middle');
                            text.setAttribute('font-size', '10');
                            text.setAttribute('fill', '#6B7280');
                            text.textContent = link.label;
                            svg.appendChild(text);
                        }
                    }
                });

                // Draw nodes
                nodes.forEach(node => {
                    const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                    circle.setAttribute('cx', node.x);
                    circle.setAttribute('cy', node.y);
                    circle.setAttribute('r', node.size || 8);
                    circle.setAttribute('fill', node.color);
                    circle.setAttribute('stroke', '#fff');
                    circle.setAttribute('stroke-width', '2');
                    circle.style.cursor = 'pointer';

                    // Add click handler
                    circle.addEventListener('click', () => showNodeDetails(node));

                    svg.appendChild(circle);

                    // Add node label
                    const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                    text.setAttribute('x', node.x);
                    text.setAttribute('y', node.y + (node.size || 8) + 15);
                    text.setAttribute('text-anchor', 'middle');
                    text.setAttribute('font-size', '12');
                    text.setAttribute('font-weight', 'bold');
                    text.setAttribute('fill', '#374151');
                    text.textContent = node.name.length > 15 ? node.name.substring(0, 15) + '...' : node.name;
                    svg.appendChild(text);
                });
            }

            function showNodeDetails(node) {
                const detailsPanel = document.getElementById('node-details');
                const detailsContent = document.getElementById('node-details-content');

                let content = \`
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <h4 class="font-semibold text-blue-900">\${node.name}</h4>
                            <p class="text-sm text-blue-700">Type: \${node.type}</p>
                        </div>
                        <div class="text-right">
                            <button onclick="focusOnEntity('\${node.id}', '\${node.type}')" class="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700">
                                Focus on This Node
                            </button>
                        </div>
                    </div>
                    <div class="mt-3 text-sm text-blue-800">
                \`;

                // Add specific details based on node type
                if (node.type === 'jobSeeker') {
                    content += \`
                        <p><strong>Title:</strong> \${node.data.title}</p>
                        <p><strong>Experience:</strong> \${node.data.experience}</p>
                        <p><strong>Location:</strong> \${node.data.location}</p>
                        <p><strong>Email:</strong> \${node.data.email}</p>
                    \`;
                } else if (node.type === 'company') {
                    content += \`
                        <p><strong>Industry:</strong> \${node.data.industry}</p>
                        <p><strong>Size:</strong> \${node.data.size} (\${node.data.employeeCount} employees)</p>
                        <p><strong>Location:</strong> \${node.data.location}</p>
                        <p><strong>Description:</strong> \${node.data.description}</p>
                    \`;
                } else if (node.type === 'position') {
                    content += \`
                        <p><strong>Company:</strong> \${node.data.company}</p>
                        <p><strong>Level:</strong> \${node.data.level}</p>
                        <p><strong>Location:</strong> \${node.data.location}</p>
                        <p><strong>Salary:</strong> \${node.data.salary}</p>
                    \`;
                } else if (node.type === 'hiringAuthority') {
                    content += \`
                        <p><strong>Title:</strong> \${node.data.title}</p>
                        <p><strong>Department:</strong> \${node.data.department}</p>
                        <p><strong>Hiring Scope:</strong> \${node.data.hiringScope}</p>
                        <p><strong>Email:</strong> \${node.data.email}</p>
                    \`;
                } else if (node.type === 'skill') {
                    content += \`
                        <p><strong>Category:</strong> \${node.data.category}</p>
                    \`;
                }

                content += '</div>';
                detailsContent.innerHTML = content;
                detailsPanel.style.display = 'block';
            }

            function showGraphControls() {
                const controls = document.getElementById('graph-controls');
                controls.style.display = controls.style.display === 'none' ? 'block' : 'none';
            }

            async function populateGraphControls() {
                try {
                    // Load all entities for dropdowns
                    const [jobSeekers, companies, positions, hiringAuthorities] = await Promise.all([
                        fetch('/api/job-seekers').then(r => r.json()),
                        fetch('/api/companies').then(r => r.json()),
                        fetch('/api/positions').then(r => r.json()),
                        fetch('/api/hiring-authorities').then(r => r.json())
                    ]);

                    // Populate job seekers dropdown
                    const jobSeekerSelect = document.getElementById('jobseeker-select');
                    if (jobSeekers.success) {
                        jobSeekers.data.forEach(js => {
                            const option = document.createElement('option');
                            option.value = js._id;
                            option.textContent = js.name;
                            jobSeekerSelect.appendChild(option);
                        });
                    }

                    // Populate companies dropdown
                    const companySelect = document.getElementById('company-select');
                    if (companies.success) {
                        companies.data.forEach(c => {
                            const option = document.createElement('option');
                            option.value = c._id;
                            option.textContent = c.name;
                            companySelect.appendChild(option);
                        });
                    }

                    // Populate positions dropdown
                    const positionSelect = document.getElementById('position-select');
                    if (positions.success) {
                        positions.data.forEach(p => {
                            const option = document.createElement('option');
                            option.value = p._id;
                            option.textContent = p.title;
                            positionSelect.appendChild(option);
                        });
                    }

                    // Populate hiring authorities dropdown
                    const hiringSelect = document.getElementById('hiring-select');
                    if (hiringAuthorities.success) {
                        hiringAuthorities.data.forEach(ha => {
                            const option = document.createElement('option');
                            option.value = ha._id;
                            option.textContent = ha.name;
                            hiringSelect.appendChild(option);
                        });
                    }
                } catch (error) {
                    console.error('Error populating controls:', error);
                }
            }

            function focusOnEntity(entityId, entityType) {
                if (entityId) {
                    // Clear other dropdowns
                    document.querySelectorAll('#graph-controls select').forEach(select => {
                        if (select.value !== entityId) select.value = '';
                    });

                    loadAndRenderGraph(entityId, entityType);
                }
            }

            // Helper function to load matches for a specific job seeker
            async function loadMatchesForSeeker(seekerId) {
                try {
                    const response = await fetch('/api/matches');
                    const data = await response.json();

                    if (data.success) {
                        const seekerMatches = data.data.filter(match => match.jobSeeker._id === seekerId);

                        document.getElementById('content-area').innerHTML = \`
                            <div class="p-6">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">🎯 Matches for \${seekerMatches[0]?.jobSeeker.name || 'Job Seeker'}</h2>
                                <div class="space-y-4">
                                    \${seekerMatches.map(match => \`
                                        <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                                            <div class="flex justify-between items-start mb-4">
                                                <div>
                                                    <h3 class="font-semibold text-gray-900">\${match.position.title}</h3>
                                                    <p class="text-sm text-gray-600">\${match.company.name}</p>
                                                    <p class="text-sm text-gray-500">\${match.position.location} • \${match.position.salary}</p>
                                                </div>
                                                <div class="text-right">
                                                    <span class="text-2xl font-bold \${match.score >= 80 ? 'text-green-600' : match.score >= 60 ? 'text-yellow-600' : 'text-red-600'}">\${match.score}%</span>
                                                    <p class="text-xs text-gray-500">Match Score</p>
                                                </div>
                                            </div>
                                            <div class="border-t pt-4">
                                                <p class="text-sm text-gray-600">
                                                    <span class="font-medium">Contact:</span> \${match.hiringAuthority ? match.hiringAuthority.name : 'N/A'}
                                                    \${match.hiringAuthority ? '(' + match.hiringAuthority.title + ')' : ''}
                                                </p>
                                                <div class="mt-2">
                                                    <button onclick="loadGraphVisualization('\${match.position._id}', 'position')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200">
                                                        🌐 View Position Graph
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    \`).join('')}
                                </div>
                            </div>
                        \`;
                    }
                } catch (error) {
                    console.error('Error loading seeker matches:', error);
                }
            }

            // Helper function to load candidates for a specific company
            async function loadMatchesForCompany(companyId) {
                try {
                    const response = await fetch('/api/matches');
                    const data = await response.json();

                    if (data.success) {
                        const companyMatches = data.data.filter(match => match.company._id === companyId);

                        document.getElementById('content-area').innerHTML = \`
                            <div class="p-6">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">🎯 Candidates for \${companyMatches[0]?.company.name || 'Company'}</h2>
                                <div class="space-y-4">
                                    \${companyMatches.map(match => \`
                                        <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                                            <div class="flex justify-between items-start mb-4">
                                                <div>
                                                    <h3 class="font-semibold text-gray-900">\${match.jobSeeker.name}</h3>
                                                    <p class="text-sm text-gray-600">\${match.jobSeeker.title}</p>
                                                    <p class="text-sm text-gray-500">\${match.jobSeeker.location} • \${match.jobSeeker.experience}</p>
                                                </div>
                                                <div class="text-right">
                                                    <span class="text-2xl font-bold \${match.score >= 80 ? 'text-green-600' : match.score >= 60 ? 'text-yellow-600' : 'text-red-600'}">\${match.score}%</span>
                                                    <p class="text-xs text-gray-500">Match Score</p>
                                                </div>
                                            </div>
                                            <div class="border-t pt-4">
                                                <h4 class="font-medium text-gray-900">\${match.position.title}</h4>
                                                <p class="text-sm text-gray-500">\${match.position.location} • \${match.position.salary}</p>
                                                <div class="mt-2">
                                                    <button onclick="loadGraphVisualization('\${match.jobSeeker._id}', 'jobSeeker')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">
                                                        🌐 View Candidate Graph
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    \`).join('')}
                                </div>
                            </div>
                        \`;
                    }
                } catch (error) {
                    console.error('Error loading company matches:', error);
                }
            }

            // Load stats on page load
            loadStats();
        </script>
    </body>
    </html>
  `);
});

app.listen(PORT, () => {
  console.log(`
🚀 Candid Connections Server Running!

📍 URL: http://localhost:${PORT}
🎯 Status: Ready for testing
📊 Database: Connected to ArangoDB
🔧 API Endpoints: /api/stats, /api/job-seekers, /api/companies, /api/positions, /api/matches

✨ Open your browser and navigate to http://localhost:${PORT} to test the application!
  `);
});
