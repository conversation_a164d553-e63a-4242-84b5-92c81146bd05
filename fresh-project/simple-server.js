#!/usr/bin/env node

const express = require('express');
const { Database } = require('arangojs');
const path = require('path');

const app = express();
const PORT = 3002;

// Database connection
const db = new Database({
  url: 'https://f2c8a97499a8.arangodb.cloud:8529',
  auth: {
    username: 'root',
    password: 'XMx2qSsHU8RWMX9VSxAx'
  },
  databaseName: 'candid_connections'
});

app.use(express.json());
app.use(express.static('public'));

// API Routes

// Get dashboard stats
app.get('/api/stats', async (req, res) => {
  try {
    const [jobSeekers, companies, hiringAuthorities, positions, skills, connections] = await Promise.all([
      db.collection('jobSeekers').count(),
      db.collection('companies').count(),
      db.collection('hiringAuthorities').count(),
      db.collection('positions').count(),
      db.collection('skills').count(),
      db.collection('seekerSkills').count()
    ]);

    res.json({
      success: true,
      data: {
        jobSeekers: jobSeekers.count,
        companies: companies.count,
        hiringAuthorities: hiringAuthorities.count,
        positions: positions.count,
        skills: skills.count,
        connections: connections.count
      }
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get all job seekers
app.get('/api/job-seekers', async (req, res) => {
  try {
    const cursor = await db.query('FOR doc IN jobSeekers RETURN doc');
    const jobSeekers = await cursor.all();
    res.json({ success: true, data: jobSeekers });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get all companies
app.get('/api/companies', async (req, res) => {
  try {
    const cursor = await db.query('FOR doc IN companies RETURN doc');
    const companies = await cursor.all();
    res.json({ success: true, data: companies });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get all hiring authorities
app.get('/api/hiring-authorities', async (req, res) => {
  try {
    const cursor = await db.query('FOR doc IN hiringAuthorities RETURN doc');
    const hiringAuthorities = await cursor.all();
    res.json({ success: true, data: hiringAuthorities });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get all positions
app.get('/api/positions', async (req, res) => {
  try {
    const cursor = await db.query('FOR doc IN positions RETURN doc');
    const positions = await cursor.all();
    res.json({ success: true, data: positions });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get hiring authority matches
app.get('/api/hiring-matches/:authorityId', async (req, res) => {
  try {
    const { authorityId } = req.params;

    // Get the hiring authority
    const authorityCursor = await db.query('FOR doc IN hiringAuthorities FILTER doc._id == @id RETURN doc', { id: authorityId });
    const authority = await authorityCursor.next();

    if (!authority) {
      return res.status(404).json({ success: false, error: 'Hiring authority not found' });
    }

    // Get positions managed by this authority
    const positionsCursor = await db.query(`
      FOR edge IN hiringAuthorityPositions
        FILTER edge._from == @authorityId
        FOR position IN positions
          FILTER position._id == edge._to
          RETURN position
    `, { authorityId });
    const positions = await positionsCursor.all();

    // Get matches for these positions
    const matchQuery = `
      WITH skills, companies, hiringAuthorities
      FOR seeker IN jobSeekers
        FOR position IN @positions
          LET company = FIRST(FOR c IN companies FILTER c._key == position.company RETURN c)
          LET seekerSkills = (
            FOR skill, edge IN 1..1 OUTBOUND seeker seekerSkills
              RETURN {
                skill: skill.name,
                proficiency: edge.proficiency
              }
          )
          LET positionSkills = (
            FOR skill, edge IN 1..1 OUTBOUND position positionSkills
              RETURN {
                skill: skill.name,
                required: edge.required,
                level: edge.level
              }
          )
          LET skillMatches = (
            FOR posReq IN positionSkills
              LET seekerSkill = FIRST(FOR s IN seekerSkills FILTER s.skill == posReq.skill RETURN s)
              RETURN {
                skill: posReq.skill,
                seekerProficiency: seekerSkill ? seekerSkill.proficiency : "None",
                requiredLevel: posReq.level,
                match: seekerSkill != null,
                weight: posReq.required ? 3 : 1
              }
          )
          LET totalScore = SUM(FOR sm IN skillMatches FILTER sm.match RETURN sm.weight)
          LET maxScore = SUM(FOR sm IN skillMatches RETURN sm.weight)
          LET score = maxScore > 0 ? ROUND((totalScore / maxScore) * 100) : 0
          FILTER score > 0
          SORT score DESC
          RETURN {
            jobSeeker: seeker,
            position: position,
            company: company,
            score: score,
            skillMatches: skillMatches,
            locationMatch: seeker.location == position.location,
            experienceMatch: true
          }
    `;

    const matchesCursor = await db.query(matchQuery, { positions });
    const matches = await matchesCursor.all();

    res.json({
      success: true,
      data: {
        authority,
        positions,
        matches: matches.slice(0, 20) // Top 20 matches
      }
    });

  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// CRUD operations for nodes
app.put('/api/node/:collection/:id', async (req, res) => {
  try {
    const { collection, id } = req.params;
    const updateData = req.body;

    // Remove internal fields
    delete updateData._key;
    delete updateData._id;
    delete updateData._rev;

    const result = await db.collection(collection).update(id, updateData);

    res.json({ success: true, data: result });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

app.post('/api/node/:collection', async (req, res) => {
  try {
    const { collection } = req.params;
    const nodeData = req.body;

    const result = await db.collection(collection).save(nodeData);

    res.json({ success: true, data: result });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

app.delete('/api/node/:collection/:id', async (req, res) => {
  try {
    const { collection, id } = req.params;

    const result = await db.collection(collection).remove(id);

    res.json({ success: true, data: result });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get graph data for visualization
app.get('/api/graph', async (req, res) => {
  try {
    const { focus, type } = req.query;

    // Get all entities
    const [jobSeekers, companies, hiringAuthorities, positions, skills] = await Promise.all([
      db.query('FOR doc IN jobSeekers RETURN doc').then(cursor => cursor.all()),
      db.query('FOR doc IN companies RETURN doc').then(cursor => cursor.all()),
      db.query('FOR doc IN hiringAuthorities RETURN doc').then(cursor => cursor.all()),
      db.query('FOR doc IN positions RETURN doc').then(cursor => cursor.all()),
      db.query('FOR doc IN skills RETURN doc').then(cursor => cursor.all())
    ]);

    // Get all relationships
    const [seekerSkills, positionSkills, companyHiring, hiringPositions] = await Promise.all([
      db.query('FOR doc IN seekerSkills RETURN doc').then(cursor => cursor.all()),
      db.query('FOR doc IN positionSkills RETURN doc').then(cursor => cursor.all()),
      db.query('FOR doc IN companyHiringAuthorities RETURN doc').then(cursor => cursor.all()),
      db.query('FOR doc IN hiringAuthorityPositions RETURN doc').then(cursor => cursor.all())
    ]);

    // Create nodes
    let nodes = [];
    let links = [];

    // Add nodes based on focus
    if (!focus || type === 'overview') {
      // Overview mode - show all entities
      nodes = [
        ...jobSeekers.map(js => ({
          id: js._id,
          name: js.name,
          type: 'jobSeeker',
          color: '#3B82F6',
          size: 8,
          data: js
        })),
        ...companies.map(c => ({
          id: c._id,
          name: c.name,
          type: 'company',
          color: '#10B981',
          size: 12,
          data: c
        })),
        ...hiringAuthorities.map(ha => ({
          id: ha._id,
          name: ha.name,
          type: 'hiringAuthority',
          color: '#F97316',
          size: 6,
          data: ha
        })),
        ...positions.map(p => ({
          id: p._id,
          name: p.title,
          type: 'position',
          color: '#8B5CF6',
          size: 10,
          data: p
        })),
        ...skills.map(s => ({
          id: s._id,
          name: s.name,
          type: 'skill',
          color: '#F59E0B',
          size: 4,
          data: s
        }))
      ];

      // Add all relationships
      links = [
        ...seekerSkills.map(edge => ({
          source: edge._from,
          target: edge._to,
          type: 'hasSkill',
          label: edge.proficiency,
          color: '#94A3B8'
        })),
        ...positionSkills.map(edge => ({
          source: edge._from,
          target: edge._to,
          type: 'requiresSkill',
          label: edge.required ? 'Required' : 'Preferred',
          color: edge.required ? '#EF4444' : '#94A3B8'
        })),
        ...companyHiring.map(edge => ({
          source: edge._from,
          target: edge._to,
          type: 'employs',
          label: 'employs',
          color: '#10B981'
        })),
        ...hiringPositions.map(edge => ({
          source: edge._from,
          target: edge._to,
          type: 'manages',
          label: 'manages',
          color: '#8B5CF6'
        }))
      ];
    } else {
      // Focused mode - show specific entity and connections
      const focusEntity = [...jobSeekers, ...companies, ...positions, ...hiringAuthorities].find(e => e._id === focus);

      if (focusEntity) {
        nodes.push({
          id: focusEntity._id,
          name: focusEntity.name || focusEntity.title,
          type: type,
          color: type === 'jobSeeker' ? '#3B82F6' : type === 'company' ? '#10B981' : type === 'position' ? '#8B5CF6' : '#F97316',
          size: 15,
          data: focusEntity,
          fx: 0,
          fy: 0
        });

        // Add connected entities based on type
        if (type === 'jobSeeker') {
          // Show skills, potential positions, and companies
          const relatedSkills = seekerSkills.filter(edge => edge._from === focus);
          const skillIds = relatedSkills.map(edge => edge._to);

          // Add skills
          skills.filter(s => skillIds.includes(s._id)).forEach(skill => {
            nodes.push({
              id: skill._id,
              name: skill.name,
              type: 'skill',
              color: '#F59E0B',
              size: 6,
              data: skill
            });
          });

          // Add skill connections
          relatedSkills.forEach(edge => {
            links.push({
              source: edge._from,
              target: edge._to,
              type: 'hasSkill',
              label: edge.proficiency,
              color: '#94A3B8'
            });
          });

          // Find positions that require these skills
          const relatedPositions = positionSkills.filter(edge => skillIds.includes(edge._to));
          const positionIds = [...new Set(relatedPositions.map(edge => edge._from))];

          positions.filter(p => positionIds.includes(p._id)).forEach(position => {
            nodes.push({
              id: position._id,
              name: position.title,
              type: 'position',
              color: '#8B5CF6',
              size: 10,
              data: position
            });
          });

          // Add position-skill connections
          relatedPositions.forEach(edge => {
            if (skillIds.includes(edge._to)) {
              links.push({
                source: edge._from,
                target: edge._to,
                type: 'requiresSkill',
                label: edge.required ? 'Required' : 'Preferred',
                color: edge.required ? '#EF4444' : '#94A3B8'
              });
            }
          });

          // Add companies for these positions
          const companyIds = [...new Set(positions.filter(p => positionIds.includes(p._id)).map(p => `companies/${p.company}`))];
          companies.filter(c => companyIds.includes(c._id)).forEach(company => {
            nodes.push({
              id: company._id,
              name: company.name,
              type: 'company',
              color: '#10B981',
              size: 12,
              data: company
            });
          });
        } else if (type === 'company') {
          // Show hiring authorities, positions, and related job seekers
          const companyKey = focusEntity._key;

          // Add hiring authorities
          const companyHiringRels = companyHiring.filter(edge => edge._from === focus);
          const hiringAuthorityIds = companyHiringRels.map(edge => edge._to);

          hiringAuthorities.filter(ha => hiringAuthorityIds.includes(ha._id)).forEach(ha => {
            nodes.push({
              id: ha._id,
              name: ha.name,
              type: 'hiringAuthority',
              color: '#F97316',
              size: 8,
              data: ha
            });
          });

          // Add company-hiring authority connections
          companyHiringRels.forEach(edge => {
            links.push({
              source: edge._from,
              target: edge._to,
              type: 'employs',
              label: 'employs',
              color: '#10B981'
            });
          });

          // Add positions for this company
          const companyPositions = positions.filter(p => p.company === companyKey);
          companyPositions.forEach(position => {
            nodes.push({
              id: position._id,
              name: position.title,
              type: 'position',
              color: '#8B5CF6',
              size: 10,
              data: position
            });
          });

          // Add hiring authority-position connections
          const hiringPositionRels = hiringPositions.filter(edge =>
            hiringAuthorityIds.includes(edge._from) &&
            companyPositions.some(p => p._id === edge._to)
          );

          hiringPositionRels.forEach(edge => {
            links.push({
              source: edge._from,
              target: edge._to,
              type: 'manages',
              label: 'manages',
              color: '#8B5CF6'
            });
          });

          // Add skills required by these positions
          const positionIds = companyPositions.map(p => p._id);
          const relatedPositionSkills = positionSkills.filter(edge => positionIds.includes(edge._from));
          const skillIds = [...new Set(relatedPositionSkills.map(edge => edge._to))];

          skills.filter(s => skillIds.includes(s._id)).forEach(skill => {
            nodes.push({
              id: skill._id,
              name: skill.name,
              type: 'skill',
              color: '#F59E0B',
              size: 6,
              data: skill
            });
          });

          // Add position-skill connections
          relatedPositionSkills.forEach(edge => {
            links.push({
              source: edge._from,
              target: edge._to,
              type: 'requiresSkill',
              label: edge.required ? 'Required' : 'Preferred',
              color: edge.required ? '#EF4444' : '#94A3B8'
            });
          });

          // Add job seekers who have these skills
          const relatedSeekerSkills = seekerSkills.filter(edge => skillIds.includes(edge._to));
          const seekerIds = [...new Set(relatedSeekerSkills.map(edge => edge._from))];

          jobSeekers.filter(js => seekerIds.includes(js._id)).forEach(seeker => {
            nodes.push({
              id: seeker._id,
              name: seeker.name,
              type: 'jobSeeker',
              color: '#3B82F6',
              size: 8,
              data: seeker
            });
          });

          // Add seeker-skill connections
          relatedSeekerSkills.forEach(edge => {
            links.push({
              source: edge._from,
              target: edge._to,
              type: 'hasSkill',
              label: edge.proficiency,
              color: '#94A3B8'
            });
          });
        } else if (type === 'position') {
          // Show company, hiring authority, required skills, and matching job seekers
          const position = focusEntity;

          // Add company
          const company = companies.find(c => c._key === position.company);
          if (company) {
            nodes.push({
              id: company._id,
              name: company.name,
              type: 'company',
              color: '#10B981',
              size: 12,
              data: company
            });
          }

          // Add hiring authority
          const hiringAuthority = hiringPositions.find(edge => edge._to === focus);
          if (hiringAuthority) {
            const ha = hiringAuthorities.find(h => h._id === hiringAuthority._from);
            if (ha) {
              nodes.push({
                id: ha._id,
                name: ha.name,
                type: 'hiringAuthority',
                color: '#F97316',
                size: 8,
                data: ha
              });

              links.push({
                source: ha._id,
                target: focus,
                type: 'manages',
                label: 'manages',
                color: '#8B5CF6'
              });
            }
          }

          // Add required skills
          const positionSkillRels = positionSkills.filter(edge => edge._from === focus);
          const skillIds = positionSkillRels.map(edge => edge._to);

          skills.filter(s => skillIds.includes(s._id)).forEach(skill => {
            nodes.push({
              id: skill._id,
              name: skill.name,
              type: 'skill',
              color: '#F59E0B',
              size: 6,
              data: skill
            });
          });

          // Add position-skill connections
          positionSkillRels.forEach(edge => {
            links.push({
              source: edge._from,
              target: edge._to,
              type: 'requiresSkill',
              label: edge.required ? 'Required' : 'Preferred',
              color: edge.required ? '#EF4444' : '#94A3B8'
            });
          });

          // Add matching job seekers
          const relatedSeekerSkills = seekerSkills.filter(edge => skillIds.includes(edge._to));
          const seekerIds = [...new Set(relatedSeekerSkills.map(edge => edge._from))];

          jobSeekers.filter(js => seekerIds.includes(js._id)).forEach(seeker => {
            nodes.push({
              id: seeker._id,
              name: seeker.name,
              type: 'jobSeeker',
              color: '#3B82F6',
              size: 8,
              data: seeker
            });
          });

          // Add seeker-skill connections
          relatedSeekerSkills.forEach(edge => {
            links.push({
              source: edge._from,
              target: edge._to,
              type: 'hasSkill',
              label: edge.proficiency,
              color: '#94A3B8'
            });
          });
        } else if (type === 'hiringAuthority') {
          // Show company, managed positions, required skills, and matching candidates
          const authority = focusEntity;

          // Add company
          const company = companies.find(c => c._key === authority.company);
          if (company) {
            nodes.push({
              id: company._id,
              name: company.name,
              type: 'company',
              color: '#10B981',
              size: 12,
              data: company
            });

            // Add company-authority connection
            links.push({
              source: company._id,
              target: focus,
              type: 'employs',
              label: 'employs',
              color: '#10B981'
            });
          }

          // Add managed positions
          const managedPositions = hiringPositions.filter(edge => edge._from === focus);
          const positionIds = managedPositions.map(edge => edge._to);

          positions.filter(p => positionIds.includes(p._id)).forEach(position => {
            nodes.push({
              id: position._id,
              name: position.title,
              type: 'position',
              color: '#8B5CF6',
              size: 10,
              data: position
            });
          });

          // Add authority-position connections
          managedPositions.forEach(edge => {
            links.push({
              source: edge._from,
              target: edge._to,
              type: 'manages',
              label: 'manages',
              color: '#8B5CF6'
            });
          });

          // Add required skills for managed positions
          const positionSkillRels = positionSkills.filter(edge => positionIds.includes(edge._from));
          const skillIds = [...new Set(positionSkillRels.map(edge => edge._to))];

          skills.filter(s => skillIds.includes(s._id)).forEach(skill => {
            nodes.push({
              id: skill._id,
              name: skill.name,
              type: 'skill',
              color: '#F59E0B',
              size: 6,
              data: skill
            });
          });

          // Add position-skill connections
          positionSkillRels.forEach(edge => {
            links.push({
              source: edge._from,
              target: edge._to,
              type: 'requiresSkill',
              label: edge.required ? 'Required' : 'Preferred',
              color: edge.required ? '#EF4444' : '#94A3B8'
            });
          });

          // Add matching job seekers
          const relatedSeekerSkills = seekerSkills.filter(edge => skillIds.includes(edge._to));
          const seekerIds = [...new Set(relatedSeekerSkills.map(edge => edge._from))];

          jobSeekers.filter(js => seekerIds.includes(js._id)).forEach(seeker => {
            nodes.push({
              id: seeker._id,
              name: seeker.name,
              type: 'jobSeeker',
              color: '#3B82F6',
              size: 8,
              data: seeker
            });
          });

          // Add seeker-skill connections
          relatedSeekerSkills.forEach(edge => {
            links.push({
              source: edge._from,
              target: edge._to,
              type: 'hasSkill',
              label: edge.proficiency,
              color: '#94A3B8'
            });
          });
        }
      }
    }

    res.json({
      success: true,
      data: { nodes, links },
      focus: focus,
      type: type
    });

  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get job matches
app.get('/api/matches', async (req, res) => {
  try {
    const matchQuery = `
      WITH skills, companies, hiringAuthorities
      FOR seeker IN jobSeekers
        FOR position IN positions
          LET company = FIRST(FOR c IN companies FILTER c._key == position.company RETURN c)
          LET hiringAuthority = FIRST(
            FOR ha IN hiringAuthorities
            FILTER ha.company == position.company
            RETURN ha
          )
          LET seekerSkills = (
            FOR skill, edge IN 1..1 OUTBOUND seeker seekerSkills
              RETURN {
                skill: skill.name,
                proficiency: edge.proficiency
              }
          )
          LET positionSkills = (
            FOR skill, edge IN 1..1 OUTBOUND position positionSkills
              RETURN {
                skill: skill.name,
                required: edge.required,
                level: edge.level
              }
          )
          LET skillMatches = (
            FOR posReq IN positionSkills
              LET seekerSkill = FIRST(FOR s IN seekerSkills FILTER s.skill == posReq.skill RETURN s)
              RETURN {
                skill: posReq.skill,
                seekerProficiency: seekerSkill ? seekerSkill.proficiency : "None",
                requiredLevel: posReq.level,
                match: seekerSkill != null,
                weight: posReq.required ? 3 : 1
              }
          )
          LET totalScore = SUM(FOR sm IN skillMatches FILTER sm.match RETURN sm.weight)
          LET maxScore = SUM(FOR sm IN skillMatches RETURN sm.weight)
          LET score = maxScore > 0 ? ROUND((totalScore / maxScore) * 100) : 0
          FILTER score > 0
          SORT score DESC
          LIMIT 20
          RETURN {
            jobSeeker: seeker,
            position: position,
            company: company,
            hiringAuthority: hiringAuthority,
            score: score,
            skillMatches: skillMatches,
            locationMatch: seeker.location == position.location,
            experienceMatch: true
          }
    `;

    const cursor = await db.query(matchQuery);
    const matches = await cursor.all();

    res.json({ success: true, data: matches });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Serve the main page
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🤝 Candid Connections - Job Matching Platform</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
        <script src="https://unpkg.com/three@0.158.0/examples/js/controls/OrbitControls.js"></script>
        <script src="https://unpkg.com/3d-force-graph@1.72.3/dist/3d-force-graph.min.js"></script>
        <style>
            .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
            .modal-overlay { background: rgba(0, 0, 0, 0.5); }
            .graph-container { position: relative; }
            .graph-controls { position: absolute; top: 10px; right: 10px; z-index: 100; }
        </style>
    </head>
    <body class="bg-gray-50">
        <div class="min-h-screen">
            <!-- Header -->
            <header class="gradient-bg text-white shadow-lg">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between items-center py-6">
                        <h1 class="text-3xl font-bold">🤝 Candid Connections</h1>
                        <p class="text-lg">Intelligent Job Matching Platform</p>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div class="px-4 py-6 sm:px-0">
                    <!-- Stats Grid -->
                    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-6 mb-8" id="stats-grid">
                        <!-- Stats will be loaded here -->
                    </div>

                    <!-- Navigation Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-6 mb-8">
                        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadJobSeekers()">
                            <div class="text-3xl mb-3">👤</div>
                            <h3 class="text-lg font-semibold text-gray-900">Job Seekers</h3>
                            <p class="text-sm text-gray-600">View all job seekers and their profiles</p>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadCompanies()">
                            <div class="text-3xl mb-3">🏢</div>
                            <h3 class="text-lg font-semibold text-gray-900">Companies</h3>
                            <p class="text-sm text-gray-600">Browse companies and open positions</p>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadPositions()">
                            <div class="text-3xl mb-3">💼</div>
                            <h3 class="text-lg font-semibold text-gray-900">Positions</h3>
                            <p class="text-sm text-gray-600">View all available job positions</p>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadMatches()">
                            <div class="text-3xl mb-3">🎯</div>
                            <h3 class="text-lg font-semibold text-gray-900">Job Matches</h3>
                            <p class="text-sm text-gray-600">See intelligent job matching results</p>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadHiringAuthorities()">
                            <div class="text-3xl mb-3">👔</div>
                            <h3 class="text-lg font-semibold text-gray-900">Hiring Authorities</h3>
                            <p class="text-sm text-gray-600">View hiring managers and their best matches</p>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadSkills()">
                            <div class="text-3xl mb-3">🎯</div>
                            <h3 class="text-lg font-semibold text-gray-900">Skills</h3>
                            <p class="text-sm text-gray-600">View skills demand and availability</p>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadGraphVisualization()">
                            <div class="text-3xl mb-3">🌐</div>
                            <h3 class="text-lg font-semibold text-gray-900">Graph Visualization</h3>
                            <p class="text-sm text-gray-600">Interactive 2D/3D network graph</p>
                        </div>
                    </div>

                    <!-- Content Area -->
                    <div class="bg-white rounded-lg shadow" id="content-area">
                        <div class="p-6">
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">Welcome to Candid Connections</h2>
                            <p class="text-gray-600 mb-4">
                                This intelligent job matching platform uses advanced graph database technology to connect
                                job seekers with the perfect opportunities through relationship mapping and smart algorithms.
                            </p>
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <h3 class="font-semibold text-blue-900 mb-2">🚀 Platform Features:</h3>
                                <ul class="text-blue-800 space-y-1">
                                    <li>• Intelligent job matching based on skills, experience, and location</li>
                                    <li>• Company hierarchy modeling for appropriate hiring authority routing</li>
                                    <li>• Graph database relationships for complex data modeling</li>
                                    <li>• Real-time statistics and comprehensive data management</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <script>
            // Load dashboard stats
            async function loadStats() {
                try {
                    const response = await fetch('/api/stats');
                    const data = await response.json();

                    if (data.success) {
                        const stats = data.data;
                        document.getElementById('stats-grid').innerHTML = \`
                            <div class="bg-white overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="text-3xl">👤</div>
                                        <div class="ml-5">
                                            <dt class="text-sm font-medium text-gray-500">Job Seekers</dt>
                                            <dd class="text-lg font-medium text-gray-900">\${stats.jobSeekers}</dd>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="text-3xl">🏢</div>
                                        <div class="ml-5">
                                            <dt class="text-sm font-medium text-gray-500">Companies</dt>
                                            <dd class="text-lg font-medium text-gray-900">\${stats.companies}</dd>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="text-3xl">👔</div>
                                        <div class="ml-5">
                                            <dt class="text-sm font-medium text-gray-500">Hiring Authorities</dt>
                                            <dd class="text-lg font-medium text-gray-900">\${stats.hiringAuthorities}</dd>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="text-3xl">💼</div>
                                        <div class="ml-5">
                                            <dt class="text-sm font-medium text-gray-500">Positions</dt>
                                            <dd class="text-lg font-medium text-gray-900">\${stats.positions}</dd>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="text-3xl">🎯</div>
                                        <div class="ml-5">
                                            <dt class="text-sm font-medium text-gray-500">Skills</dt>
                                            <dd class="text-lg font-medium text-gray-900">\${stats.skills}</dd>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="text-3xl">🔗</div>
                                        <div class="ml-5">
                                            <dt class="text-sm font-medium text-gray-500">Connections</dt>
                                            <dd class="text-lg font-medium text-gray-900">\${stats.connections}</dd>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        \`;
                    }
                } catch (error) {
                    console.error('Error loading stats:', error);
                }
            }

            // Load job seekers
            async function loadJobSeekers() {
                try {
                    const response = await fetch('/api/job-seekers');
                    const data = await response.json();

                    if (data.success) {
                        const jobSeekers = data.data;
                        document.getElementById('content-area').innerHTML = \`
                            <div class="p-6">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">👤 Job Seekers (\${jobSeekers.length})</h2>
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    \${jobSeekers.map(seeker => \`
                                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                                            <h3 class="font-semibold text-gray-900">\${seeker.name}</h3>
                                            <p class="text-sm text-gray-600">\${seeker.title}</p>
                                            <p class="text-sm text-gray-500">\${seeker.location}</p>
                                            <p class="text-xs text-gray-400 mt-2">Experience: \${seeker.experience}</p>
                                            <div class="mt-3 flex space-x-2">
                                                <button onclick="loadGraphVisualization('\${seeker._id}', 'jobSeeker')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">
                                                    🌐 View Graph
                                                </button>
                                                <button onclick="loadMatchesForSeeker('\${seeker._id}')" class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs hover:bg-green-200">
                                                    🎯 View Matches
                                                </button>
                                            </div>
                                        </div>
                                    \`).join('')}
                                </div>
                            </div>
                        \`;
                    }
                } catch (error) {
                    console.error('Error loading job seekers:', error);
                }
            }

            // Load companies with hierarchical structure
            async function loadCompanies() {
                try {
                    const [companiesResponse, hiringAuthoritiesResponse, positionsResponse, skillsResponse] = await Promise.all([
                        fetch('/api/companies'),
                        fetch('/api/hiring-authorities'),
                        fetch('/api/positions'),
                        fetch('/api/skills')
                    ]);

                    const [companiesData, hiringAuthoritiesData, positionsData, skillsData] = await Promise.all([
                        companiesResponse.json(),
                        hiringAuthoritiesResponse.json(),
                        positionsResponse.json(),
                        skillsResponse.json()
                    ]);

                    if (companiesData.success && hiringAuthoritiesData.success && positionsData.success) {
                        const companies = companiesData.data;
                        const hiringAuthorities = hiringAuthoritiesData.data;
                        const positions = positionsData.data;
                        const skills = skillsData.data;

                        // Get position skills relationships
                        const positionSkillsResponse = await fetch('/api/graph?type=overview');
                        const graphData = await positionSkillsResponse.json();
                        const positionSkillsLinks = graphData.data.links.filter(link => link.type === 'requiresSkill');

                        document.getElementById('content-area').innerHTML = \`
                            <div class="p-6">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">🏢 Companies Hierarchy (\${companies.length})</h2>
                                <div class="space-y-6">
                                    \${companies.map(company => {
                                        const companyAuthorities = hiringAuthorities.filter(ha => ha.company === company._key);
                                        return \`
                                            <div class="border border-gray-200 rounded-lg overflow-hidden">
                                                <!-- Company Header -->
                                                <div class="bg-green-50 p-6 border-b border-gray-200">
                                                    <div class="flex justify-between items-start">
                                                        <div>
                                                            <h3 class="text-xl font-bold text-gray-900">\${company.name}</h3>
                                                            <p class="text-sm text-gray-600">\${company.industry} • \${company.location}</p>
                                                            <p class="text-sm text-gray-500">\${company.size} (\${company.employeeCount} employees)</p>
                                                        </div>
                                                        <div class="flex space-x-2">
                                                            <button onclick="loadGraphVisualization('\${company._id}', 'company')" class="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700">
                                                                🌐 View Graph
                                                            </button>
                                                            <button onclick="loadMatchesForCompany('\${company._id}')" class="px-3 py-1 bg-purple-600 text-white rounded text-sm hover:bg-purple-700">
                                                                🎯 View Candidates
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <p class="text-sm text-gray-600 mt-2">\${company.description}</p>
                                                </div>

                                                <!-- Hiring Authorities -->
                                                <div class="p-4">
                                                    <h4 class="font-semibold text-gray-900 mb-3">👔 Hiring Authorities (\${companyAuthorities.length})</h4>
                                                    <div class="space-y-4">
                                                        \${companyAuthorities.map(authority => {
                                                            const authorityPositions = positions.filter(pos => {
                                                                // Find positions managed by this authority
                                                                return graphData.data.links.some(link =>
                                                                    link.source === authority._id &&
                                                                    link.target === pos._id &&
                                                                    link.type === 'manages'
                                                                );
                                                            });

                                                            return \`
                                                                <div class="bg-orange-50 rounded-lg p-4 border border-orange-200">
                                                                    <div class="flex justify-between items-start mb-3">
                                                                        <div>
                                                                            <h5 class="font-semibold text-gray-900">\${authority.name}</h5>
                                                                            <p class="text-sm text-gray-600">\${authority.title} • \${authority.department}</p>
                                                                            <p class="text-sm text-gray-500">Scope: \${authority.hiringScope}</p>
                                                                        </div>
                                                                        <div class="flex space-x-2">
                                                                            <button onclick="loadHiringAuthorityMatches('\${authority._id}')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">
                                                                                🎯 Best Matches
                                                                            </button>
                                                                            <button onclick="loadGraphVisualization('\${authority._id}', 'hiringAuthority')" class="px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs hover:bg-orange-200">
                                                                                🌐 View Graph
                                                                            </button>
                                                                        </div>
                                                                    </div>

                                                                    <!-- Managed Positions -->
                                                                    \${authorityPositions.length > 0 ? \`
                                                                        <div class="mt-3">
                                                                            <h6 class="font-medium text-gray-800 mb-2">💼 Managed Positions (\${authorityPositions.length})</h6>
                                                                            <div class="space-y-2">
                                                                                \${authorityPositions.map(position => {
                                                                                    const positionSkills = positionSkillsLinks
                                                                                        .filter(link => link.source === position._id)
                                                                                        .map(link => {
                                                                                            const skill = skills.find(s => s._id === link.target);
                                                                                            return skill ? {
                                                                                                name: skill.name,
                                                                                                required: link.label === 'Required',
                                                                                                category: skill.category
                                                                                            } : null;
                                                                                        })
                                                                                        .filter(Boolean);

                                                                                    return \`
                                                                                        <div class="bg-purple-50 rounded p-3 border border-purple-200">
                                                                                            <div class="flex justify-between items-start mb-2">
                                                                                                <div>
                                                                                                    <h7 class="font-medium text-gray-900">\${position.title}</h7>
                                                                                                    <p class="text-xs text-gray-600">\${position.level} • \${position.location} • \${position.salary}</p>
                                                                                                </div>
                                                                                                <button onclick="loadGraphVisualization('\${position._id}', 'position')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200">
                                                                                                    🌐 Graph
                                                                                                </button>
                                                                                            </div>

                                                                                            <!-- Required Skills -->
                                                                                            \${positionSkills.length > 0 ? \`
                                                                                                <div class="mt-2">
                                                                                                    <p class="text-xs font-medium text-gray-700 mb-1">🎯 Required Skills:</p>
                                                                                                    <div class="flex flex-wrap gap-1">
                                                                                                        \${positionSkills
                                                                                                            .filter(skill => skill.required)
                                                                                                            .map(skill => \`
                                                                                                                <span class="px-2 py-1 bg-red-100 text-red-800 rounded text-xs">\${skill.name}</span>
                                                                                                            \`).join('')}
                                                                                                    </div>
                                                                                                    \${positionSkills.filter(skill => !skill.required).length > 0 ? \`
                                                                                                        <p class="text-xs font-medium text-gray-700 mb-1 mt-2">💡 Preferred Skills:</p>
                                                                                                        <div class="flex flex-wrap gap-1">
                                                                                                            \${positionSkills
                                                                                                                .filter(skill => !skill.required)
                                                                                                                .map(skill => \`
                                                                                                                    <span class="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">\${skill.name}</span>
                                                                                                                \`).join('')}
                                                                                                        </div>
                                                                                                    \` : ''}
                                                                                                </div>
                                                                                            \` : '<p class="text-xs text-gray-500 italic">No specific skills defined</p>'}
                                                                                        </div>
                                                                                    \`;
                                                                                }).join('')}
                                                                            </div>
                                                                        </div>
                                                                    \` : '<p class="text-sm text-gray-500 italic">No positions currently managed</p>'}
                                                                </div>
                                                            \`;
                                                        }).join('')}
                                                    </div>
                                                </div>
                                            </div>
                                        \`;
                                    }).join('')}
                                </div>
                            </div>
                        \`;
                    }
                } catch (error) {
                    console.error('Error loading companies:', error);
                }
            }

            // Load hiring authorities
            async function loadHiringAuthorities() {
                try {
                    const response = await fetch('/api/hiring-authorities');
                    const data = await response.json();

                    if (data.success) {
                        const hiringAuthorities = data.data;
                        document.getElementById('content-area').innerHTML = \`
                            <div class="p-6">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">👔 Hiring Authorities (\${hiringAuthorities.length})</h2>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    \${hiringAuthorities.map(authority => \`
                                        <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                                            <div class="flex justify-between items-start mb-4">
                                                <div>
                                                    <h3 class="font-semibold text-gray-900 text-lg">\${authority.name}</h3>
                                                    <p class="text-sm text-gray-600">\${authority.title}</p>
                                                    <p class="text-sm text-gray-500">\${authority.department}</p>
                                                </div>
                                                <div class="text-right">
                                                    <span class="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">\${authority.level}</span>
                                                </div>
                                            </div>
                                            <div class="mb-4">
                                                <p class="text-sm text-gray-600"><strong>Hiring Scope:</strong> \${authority.hiringScope}</p>
                                                <p class="text-sm text-gray-500"><strong>Email:</strong> \${authority.email}</p>
                                            </div>
                                            <div class="flex space-x-2">
                                                <button onclick="loadHiringAuthorityMatches('\${authority._id}')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">
                                                    🎯 View Best Matches
                                                </button>
                                                <button onclick="loadGraphVisualization('\${authority._id}', 'hiringAuthority')" class="px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs hover:bg-orange-200">
                                                    🌐 View Graph
                                                </button>
                                            </div>
                                        </div>
                                    \`).join('')}
                                </div>
                            </div>
                        \`;
                    }
                } catch (error) {
                    console.error('Error loading hiring authorities:', error);
                }
            }

            // Load skills with demand and availability analysis
            async function loadSkills() {
                try {
                    const [skillsResponse, graphResponse] = await Promise.all([
                        fetch('/api/skills'),
                        fetch('/api/graph?type=overview')
                    ]);

                    const [skillsData, graphData] = await Promise.all([
                        skillsResponse.json(),
                        graphResponse.json()
                    ]);

                    if (skillsData.success && graphData.success) {
                        const skills = skillsData.data;
                        const links = graphData.data.links;
                        const nodes = graphData.data.nodes;

                        // Analyze skill demand and availability
                        const skillAnalysis = skills.map(skill => {
                            // Find job seekers with this skill
                            const seekerConnections = links.filter(link =>
                                link.target === skill._id && link.type === 'hasSkill'
                            );

                            // Find positions requiring this skill
                            const positionConnections = links.filter(link =>
                                link.source.includes('positions/') && link.target === skill._id && link.type === 'requiresSkill'
                            );

                            // Get companies and hiring authorities related to this skill
                            const relatedPositions = positionConnections.map(link =>
                                nodes.find(node => node.id === link.source)
                            ).filter(Boolean);

                            const relatedCompanies = [...new Set(relatedPositions.map(pos => pos.data.company))];
                            const relatedHiringAuthorities = links
                                .filter(link =>
                                    link.type === 'manages' &&
                                    relatedPositions.some(pos => pos.id === link.target)
                                )
                                .map(link => nodes.find(node => node.id === link.source))
                                .filter(Boolean);

                            return {
                                skill,
                                availability: seekerConnections.length,
                                demand: positionConnections.length,
                                requiredDemand: positionConnections.filter(link => link.label === 'Required').length,
                                preferredDemand: positionConnections.filter(link => link.label === 'Preferred').length,
                                relatedCompanies: relatedCompanies.length,
                                relatedAuthorities: relatedHiringAuthorities.length,
                                seekerConnections,
                                positionConnections,
                                relatedPositions,
                                relatedHiringAuthorities
                            };
                        });

                        // Sort by demand (high to low)
                        skillAnalysis.sort((a, b) => b.demand - a.demand);

                        document.getElementById('content-area').innerHTML = \`
                            <div class="p-6">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">🎯 Skills Analysis (\${skills.length})</h2>

                                <!-- Summary Stats -->
                                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                                    <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
                                        <h3 class="font-semibold text-blue-900">Total Skills</h3>
                                        <p class="text-2xl font-bold text-blue-600">\${skills.length}</p>
                                    </div>
                                    <div class="bg-green-50 rounded-lg p-4 border border-green-200">
                                        <h3 class="font-semibold text-green-900">High Demand</h3>
                                        <p class="text-2xl font-bold text-green-600">\${skillAnalysis.filter(s => s.demand >= 2).length}</p>
                                    </div>
                                    <div class="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
                                        <h3 class="font-semibold text-yellow-900">Skill Gaps</h3>
                                        <p class="text-2xl font-bold text-yellow-600">\${skillAnalysis.filter(s => s.demand > s.availability).length}</p>
                                    </div>
                                    <div class="bg-purple-50 rounded-lg p-4 border border-purple-200">
                                        <h3 class="font-semibold text-purple-900">Oversupplied</h3>
                                        <p class="text-2xl font-bold text-purple-600">\${skillAnalysis.filter(s => s.availability > s.demand && s.demand > 0).length}</p>
                                    </div>
                                </div>

                                <!-- Skills List -->
                                <div class="space-y-4">
                                    \${skillAnalysis.map(analysis => {
                                        const { skill, availability, demand, requiredDemand, preferredDemand, relatedCompanies, relatedAuthorities } = analysis;
                                        const gapStatus = demand > availability ? 'gap' : availability > demand && demand > 0 ? 'oversupply' : 'balanced';
                                        const gapColor = gapStatus === 'gap' ? 'red' : gapStatus === 'oversupply' ? 'purple' : 'green';

                                        return \`
                                            <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                                                <div class="flex justify-between items-start mb-4">
                                                    <div>
                                                        <h3 class="text-lg font-semibold text-gray-900">\${skill.name}</h3>
                                                        <p class="text-sm text-gray-600">Category: \${skill.category}</p>
                                                        <div class="flex items-center space-x-4 mt-2">
                                                            <span class="text-sm">
                                                                <span class="font-medium text-blue-600">Available:</span> \${availability} job seekers
                                                            </span>
                                                            <span class="text-sm">
                                                                <span class="font-medium text-green-600">Demand:</span> \${demand} positions
                                                            </span>
                                                            <span class="text-sm">
                                                                <span class="font-medium text-red-600">Required:</span> \${requiredDemand}
                                                            </span>
                                                            <span class="text-sm">
                                                                <span class="font-medium text-gray-600">Preferred:</span> \${preferredDemand}
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="text-right">
                                                        <span class="px-3 py-1 rounded-full text-sm font-medium \${gapStatus === 'gap' ? 'bg-red-100 text-red-800' : gapStatus === 'oversupply' ? 'bg-purple-100 text-purple-800' : 'bg-green-100 text-green-800'}">
                                                            \${gapStatus === 'gap' ? 'Skill Gap' : gapStatus === 'oversupply' ? 'Oversupplied' : 'Balanced'}
                                                        </span>
                                                        <div class="mt-2">
                                                            <button onclick="loadGraphVisualization('\${skill._id}', 'skill')" class="px-3 py-1 bg-yellow-100 text-yellow-800 rounded text-sm hover:bg-yellow-200">
                                                                🌐 View Graph
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Market Analysis -->
                                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4 pt-4 border-t border-gray-200">
                                                    <div>
                                                        <h4 class="font-medium text-gray-900 mb-2">📈 Market Demand</h4>
                                                        <div class="space-y-1">
                                                            <p class="text-sm text-gray-600">Companies seeking: \${relatedCompanies}</p>
                                                            <p class="text-sm text-gray-600">Hiring authorities: \${relatedAuthorities}</p>
                                                            <div class="w-full bg-gray-200 rounded-full h-2">
                                                                <div class="bg-green-600 h-2 rounded-full" style="width: \${Math.min((demand / Math.max(...skillAnalysis.map(s => s.demand))) * 100, 100)}%"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <h4 class="font-medium text-gray-900 mb-2">👥 Talent Availability</h4>
                                                        <div class="space-y-1">
                                                            <p class="text-sm text-gray-600">Available candidates: \${availability}</p>
                                                            <p class="text-sm text-gray-600">Supply ratio: \${demand > 0 ? Math.round((availability / demand) * 100) : 0}%</p>
                                                            <div class="w-full bg-gray-200 rounded-full h-2">
                                                                <div class="bg-blue-600 h-2 rounded-full" style="width: \${Math.min((availability / Math.max(...skillAnalysis.map(s => s.availability))) * 100, 100)}%"></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Related Entities -->
                                                \${analysis.relatedPositions.length > 0 ? \`
                                                    <div class="mt-4 pt-4 border-t border-gray-200">
                                                        <h4 class="font-medium text-gray-900 mb-2">💼 Positions Requiring This Skill</h4>
                                                        <div class="flex flex-wrap gap-2">
                                                            \${analysis.relatedPositions.slice(0, 5).map(position => \`
                                                                <button onclick="loadGraphVisualization('\${position.id}', 'position')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200">
                                                                    \${position.name}
                                                                </button>
                                                            \`).join('')}
                                                            \${analysis.relatedPositions.length > 5 ? \`<span class="text-xs text-gray-500">+\${analysis.relatedPositions.length - 5} more</span>\` : ''}
                                                        </div>
                                                    </div>
                                                \` : ''}
                                            </div>
                                        \`;
                                    }).join('')}
                                </div>
                            </div>
                        \`;
                    }
                } catch (error) {
                    console.error('Error loading skills:', error);
                }
            }

            // Load positions
            async function loadPositions() {
                try {
                    const response = await fetch('/api/positions');
                    const data = await response.json();

                    if (data.success) {
                        const positions = data.data;
                        document.getElementById('content-area').innerHTML = \`
                            <div class="p-6">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">💼 Open Positions (\${positions.length})</h2>
                                <div class="space-y-4">
                                    \${positions.map(position => \`
                                        <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                                            <div class="flex justify-between items-start">
                                                <div>
                                                    <h3 class="font-semibold text-gray-900 text-lg">\${position.title}</h3>
                                                    <p class="text-sm text-gray-600">\${position.company}</p>
                                                    <p class="text-sm text-gray-500">\${position.location}</p>
                                                </div>
                                                <div class="text-right">
                                                    <span class="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">\${position.level}</span>
                                                    <p class="text-sm font-medium text-green-600 mt-1">\${position.salary}</p>
                                                </div>
                                            </div>
                                        </div>
                                    \`).join('')}
                                </div>
                            </div>
                        \`;
                    }
                } catch (error) {
                    console.error('Error loading positions:', error);
                }
            }

            // Load matches
            async function loadMatches() {
                document.getElementById('content-area').innerHTML = \`
                    <div class="p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">🎯 Loading Job Matches...</h2>
                        <div class="animate-pulse">
                            <div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                            <div class="h-4 bg-gray-200 rounded w-1/2"></div>
                        </div>
                    </div>
                \`;

                try {
                    const response = await fetch('/api/matches');
                    const data = await response.json();

                    if (data.success) {
                        const matches = data.data;
                        document.getElementById('content-area').innerHTML = \`
                            <div class="p-6">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">🎯 Job Matches (\${matches.length})</h2>
                                <div class="space-y-4">
                                    \${matches.map(match => \`
                                        <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                                            <div class="flex justify-between items-start mb-4">
                                                <div>
                                                    <h3 class="font-semibold text-gray-900">\${match.jobSeeker.name}</h3>
                                                    <p class="text-sm text-gray-600">\${match.jobSeeker.title}</p>
                                                    <p class="text-sm text-gray-500">\${match.jobSeeker.location}</p>
                                                </div>
                                                <div class="text-right">
                                                    <span class="text-2xl font-bold \${match.score >= 80 ? 'text-green-600' : match.score >= 60 ? 'text-yellow-600' : 'text-red-600'}">\${match.score}%</span>
                                                    <p class="text-xs text-gray-500">Match Score</p>
                                                </div>
                                            </div>
                                            <div class="border-t pt-4">
                                                <h4 class="font-medium text-gray-900">\${match.position.title}</h4>
                                                <p class="text-sm text-gray-600">\${match.company.name}</p>
                                                <p class="text-sm text-gray-500">\${match.position.location} • \${match.position.salary}</p>
                                            </div>
                                            <div class="mt-4 pt-4 border-t">
                                                <p class="text-sm text-gray-600">
                                                    <span class="font-medium">Contact:</span> \${match.hiringAuthority ? match.hiringAuthority.name : 'N/A'}
                                                    \${match.hiringAuthority ? '(' + match.hiringAuthority.title + ')' : ''}
                                                </p>
                                            </div>
                                        </div>
                                    \`).join('')}
                                </div>
                            </div>
                        \`;
                    }
                } catch (error) {
                    console.error('Error loading matches:', error);
                    document.getElementById('content-area').innerHTML = \`
                        <div class="p-6">
                            <h2 class="text-2xl font-bold text-red-600 mb-4">❌ Error Loading Matches</h2>
                            <p class="text-gray-600">There was an error loading the job matches. Please try again.</p>
                        </div>
                    \`;
                }
            }

            // Load graph visualization
            async function loadGraphVisualization(focus = null, type = 'overview') {
                document.getElementById('content-area').innerHTML = \`
                    <div class="p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-2xl font-bold text-gray-900">🌐 Graph Visualization</h2>
                            <div class="flex space-x-2">
                                <button onclick="loadGraphVisualization(null, 'overview')" class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded hover:bg-blue-200">
                                    Overview
                                </button>
                                <button onclick="showGraphControls()" class="px-3 py-1 text-sm bg-gray-100 text-gray-800 rounded hover:bg-gray-200">
                                    Focus Mode
                                </button>
                                <button onclick="toggleVisualizationMode()" id="mode-toggle" class="px-3 py-1 text-sm bg-purple-100 text-purple-800 rounded hover:bg-purple-200">
                                    Switch to 3D
                                </button>
                                <button onclick="toggleEditMode()" id="edit-toggle" class="px-3 py-1 text-sm bg-green-100 text-green-800 rounded hover:bg-green-200">
                                    Enable Editing
                                </button>
                            </div>
                        </div>

                        <!-- Graph Controls -->
                        <div id="graph-controls" class="mb-4 p-4 bg-gray-50 rounded-lg" style="display: none;">
                            <h3 class="font-semibold mb-3">Focus on Entity:</h3>
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Job Seekers</label>
                                    <select id="jobseeker-select" class="w-full p-2 border border-gray-300 rounded" onchange="focusOnEntity(this.value, 'jobSeeker')">
                                        <option value="">Select Job Seeker...</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Companies</label>
                                    <select id="company-select" class="w-full p-2 border border-gray-300 rounded" onchange="focusOnEntity(this.value, 'company')">
                                        <option value="">Select Company...</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Positions</label>
                                    <select id="position-select" class="w-full p-2 border border-gray-300 rounded" onchange="focusOnEntity(this.value, 'position')">
                                        <option value="">Select Position...</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">Hiring Authorities</label>
                                    <select id="hiring-select" class="w-full p-2 border border-gray-300 rounded" onchange="focusOnEntity(this.value, 'hiringAuthority')">
                                        <option value="">Select Authority...</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Legend -->
                        <div class="mb-4 p-4 bg-gray-50 rounded-lg">
                            <h3 class="font-semibold mb-3">Legend:</h3>
                            <div class="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-blue-500 rounded-full mr-2"></div>
                                    <span>Job Seekers</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-green-500 rounded-full mr-2"></div>
                                    <span>Companies</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-orange-500 rounded-full mr-2"></div>
                                    <span>Hiring Authorities</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-purple-500 rounded-full mr-2"></div>
                                    <span>Positions</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-yellow-500 rounded-full mr-2"></div>
                                    <span>Skills</span>
                                </div>
                            </div>
                        </div>

                        <!-- Graph Container -->
                        <div class="border border-gray-200 rounded-lg bg-white graph-container" style="height: 600px;">
                            <div id="graph-container" style="width: 100%; height: 100%;"></div>
                            <div class="graph-controls">
                                <div class="bg-white rounded-lg shadow-lg p-2 space-y-2">
                                    <button onclick="resetGraphView()" class="w-full px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs hover:bg-gray-200">
                                        Reset View
                                    </button>
                                    <button onclick="centerGraph()" class="w-full px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">
                                        Center Graph
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Node Details Panel -->
                        <div id="node-details" class="mt-4 p-4 bg-blue-50 rounded-lg" style="display: none;">
                            <h3 class="font-semibold text-blue-900 mb-2">Node Details</h3>
                            <div id="node-details-content"></div>
                        </div>
                    </div>

                    <!-- CRUD Modal -->
                    <div id="crud-modal" class="fixed inset-0 modal-overlay z-50" style="display: none;">
                        <div class="flex items-center justify-center min-h-screen p-4">
                            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-screen overflow-y-auto">
                                <div class="p-6">
                                    <div class="flex justify-between items-center mb-4">
                                        <h3 id="modal-title" class="text-lg font-semibold text-gray-900">Edit Node</h3>
                                        <button onclick="closeCrudModal()" class="text-gray-400 hover:text-gray-600">
                                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        </button>
                                    </div>
                                    <form id="crud-form">
                                        <div id="form-fields" class="space-y-4">
                                            <!-- Dynamic form fields will be inserted here -->
                                        </div>
                                        <div class="flex justify-end space-x-3 mt-6">
                                            <button type="button" onclick="closeCrudModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">
                                                Cancel
                                            </button>
                                            <button type="button" onclick="deleteNode()" id="delete-btn" class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700">
                                                Delete
                                            </button>
                                            <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                                                Save Changes
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                \`;

                // Load graph data and render
                await loadAndRenderGraph(focus, type);

                // Populate dropdowns for focus mode
                await populateGraphControls();
            }

            async function loadAndRenderGraph(focus = null, type = 'overview') {
                try {
                    const url = focus ? \`/api/graph?focus=\${focus}&type=\${type}\` : '/api/graph?type=overview';
                    const response = await fetch(url);
                    const data = await response.json();

                    if (data.success) {
                        renderGraph(data.data);
                    } else {
                        console.error('Failed to load graph data:', data.error);
                    }
                } catch (error) {
                    console.error('Error loading graph:', error);
                }
            }

            // Global variables for graph state
            let currentGraphData = null;
            let currentGraph = null;
            let is3DMode = false;
            let isEditMode = false;
            let selectedNode = null;

            function renderGraph(graphData) {
                currentGraphData = graphData;
                const container = document.getElementById('graph-container');
                container.innerHTML = ''; // Clear previous graph

                if (is3DMode) {
                    render3DGraph(graphData, container);
                } else {
                    render2DGraph(graphData, container);
                }
            }

            function render2DGraph(graphData, container) {
                // Create SVG
                const width = container.clientWidth;
                const height = container.clientHeight;

                const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                svg.setAttribute('width', width);
                svg.setAttribute('height', height);
                svg.style.background = '#f8fafc';
                svg.setAttribute('viewBox', \`0 0 \${width} \${height}\`);

                // Create main group for pan/zoom transformations
                const mainGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                svg.appendChild(mainGroup);
                container.appendChild(svg);

                // Add pan and zoom functionality
                let isPanning = false;
                let panStart = { x: 0, y: 0 };
                let currentTransform = { x: 0, y: 0, scale: 1 };

                // Mouse wheel zoom to cursor
                svg.addEventListener('wheel', (e) => {
                    e.preventDefault();
                    const rect = svg.getBoundingClientRect();
                    const mouseX = e.clientX - rect.left;
                    const mouseY = e.clientY - rect.top;

                    const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
                    const newScale = currentTransform.scale * zoomFactor;

                    // Zoom towards cursor position
                    const dx = mouseX - currentTransform.x;
                    const dy = mouseY - currentTransform.y;

                    currentTransform.x = mouseX - dx * zoomFactor;
                    currentTransform.y = mouseY - dy * zoomFactor;
                    currentTransform.scale = newScale;

                    updateTransform();
                });

                // Pan functionality
                svg.addEventListener('mousedown', (e) => {
                    if (e.button === 0 && !e.target.closest('g[data-node]')) { // Left click and not on a node
                        isPanning = true;
                        panStart.x = e.clientX - currentTransform.x;
                        panStart.y = e.clientY - currentTransform.y;
                        svg.style.cursor = 'grabbing';
                    }
                });

                svg.addEventListener('mousemove', (e) => {
                    if (isPanning) {
                        currentTransform.x = e.clientX - panStart.x;
                        currentTransform.y = e.clientY - panStart.y;
                        updateTransform();
                    }
                });

                svg.addEventListener('mouseup', () => {
                    isPanning = false;
                    svg.style.cursor = 'default';
                });

                svg.addEventListener('mouseleave', () => {
                    isPanning = false;
                    svg.style.cursor = 'default';
                });

                function updateTransform() {
                    mainGroup.setAttribute('transform',
                        \`translate(\${currentTransform.x}, \${currentTransform.y}) scale(\${currentTransform.scale})\`
                    );
                }

                // Simple force simulation using basic positioning
                const nodes = graphData.nodes.map(d => ({...d}));
                const links = graphData.links.map(d => ({...d}));

                // Position nodes in a circle or grid
                const centerX = width / 2;
                const centerY = height / 2;
                const radius = Math.min(width, height) / 3;

                if (nodes.length === 1) {
                    // Single node in center
                    nodes[0].x = centerX;
                    nodes[0].y = centerY;
                } else {
                    // Arrange nodes in a circle
                    nodes.forEach((node, i) => {
                        if (node.fx !== undefined && node.fy !== undefined) {
                            node.x = centerX + node.fx * 100;
                            node.y = centerY + node.fy * 100;
                        } else {
                            const angle = (i / nodes.length) * 2 * Math.PI;
                            node.x = centerX + Math.cos(angle) * radius;
                            node.y = centerY + Math.sin(angle) * radius;
                        }
                    });
                }

                // Draw links with proper data attributes for updates
                links.forEach((link, index) => {
                    const sourceNode = nodes.find(n => n.id === link.source);
                    const targetNode = nodes.find(n => n.id === link.target);

                    if (sourceNode && targetNode) {
                        const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                        line.setAttribute('x1', sourceNode.x);
                        line.setAttribute('y1', sourceNode.y);
                        line.setAttribute('x2', targetNode.x);
                        line.setAttribute('y2', targetNode.y);
                        line.setAttribute('stroke', link.color || '#94A3B8');
                        line.setAttribute('stroke-width', '2');
                        line.setAttribute('opacity', '0.6');
                        line.setAttribute('data-link', `${link.source}-${link.target}`);
                        line.setAttribute('data-link-index', index);
                        mainGroup.appendChild(line);

                        // Add link label
                        if (link.label) {
                            const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                            text.setAttribute('x', (sourceNode.x + targetNode.x) / 2);
                            text.setAttribute('y', (sourceNode.y + targetNode.y) / 2);
                            text.setAttribute('text-anchor', 'middle');
                            text.setAttribute('font-size', '10');
                            text.setAttribute('fill', '#6B7280');
                            text.setAttribute('data-link-label', `${link.source}-${link.target}`);
                            text.textContent = link.label;
                            mainGroup.appendChild(text);
                        }
                    }
                });

                // Draw nodes
                nodes.forEach(node => {
                    const group = document.createElementNS('http://www.w3.org/2000/svg', 'g');
                    group.style.cursor = 'pointer';
                    group.setAttribute('data-node', node.id);

                    const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
                    circle.setAttribute('cx', node.x);
                    circle.setAttribute('cy', node.y);
                    circle.setAttribute('r', node.size || 8);
                    circle.setAttribute('fill', node.color);
                    circle.setAttribute('stroke', '#fff');
                    circle.setAttribute('stroke-width', '2');

                    // Add drag functionality if edit mode is enabled
                    if (isEditMode) {
                        let isDragging = false;
                        let startX, startY;

                        group.addEventListener('mousedown', (e) => {
                            isDragging = true;
                            startX = e.clientX - node.x;
                            startY = e.clientY - node.y;
                            e.preventDefault();
                        });

                        svg.addEventListener('mousemove', (e) => {
                            if (isDragging) {
                                const rect = svg.getBoundingClientRect();
                                node.x = e.clientX - rect.left - startX;
                                node.y = e.clientY - rect.top - startY;
                                circle.setAttribute('cx', node.x);
                                circle.setAttribute('cy', node.y);
                                text.setAttribute('x', node.x);
                                text.setAttribute('y', node.y + (node.size || 8) + 15);

                                // Update connected links
                                links.forEach(link => {
                                    if (link.source === node.id || link.target === node.id) {
                                        const sourceNode = nodes.find(n => n.id === link.source);
                                        const targetNode = nodes.find(n => n.id === link.target);
                                        const linkElement = svg.querySelector(\`line[data-link="\${link.source}-\${link.target}"]\`);
                                        const linkLabel = svg.querySelector(\`text[data-link-label="\${link.source}-\${link.target}"]\`);

                                        if (linkElement && sourceNode && targetNode) {
                                            linkElement.setAttribute('x1', sourceNode.x);
                                            linkElement.setAttribute('y1', sourceNode.y);
                                            linkElement.setAttribute('x2', targetNode.x);
                                            linkElement.setAttribute('y2', targetNode.y);
                                        }

                                        if (linkLabel && sourceNode && targetNode) {
                                            linkLabel.setAttribute('x', (sourceNode.x + targetNode.x) / 2);
                                            linkLabel.setAttribute('y', (sourceNode.y + targetNode.y) / 2);
                                        }
                                    }
                                });
                            }
                        });

                        svg.addEventListener('mouseup', () => {
                            isDragging = false;
                        });
                    }

                    // Add click handler
                    group.addEventListener('click', (e) => {
                        if (!isDragging) {
                            if (isEditMode) {
                                openCrudModal(node);
                            } else {
                                showNodeDetails(node);
                            }
                        }
                    });

                    group.appendChild(circle);

                    // Add node label
                    const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                    text.setAttribute('x', node.x);
                    text.setAttribute('y', node.y + (node.size || 8) + 15);
                    text.setAttribute('text-anchor', 'middle');
                    text.setAttribute('font-size', '12');
                    text.setAttribute('font-weight', 'bold');
                    text.setAttribute('fill', '#374151');
                    text.textContent = node.name.length > 15 ? node.name.substring(0, 15) + '...' : node.name;
                    group.appendChild(text);

                    mainGroup.appendChild(group);
                });
            }

            function render3DGraph(graphData, container) {
                // Create 3D force graph with constrained size
                const Graph = ForceGraph3D()(container)
                    .graphData(graphData)
                    .width(container.clientWidth)
                    .height(container.clientHeight)
                    .nodeLabel('name')
                    .nodeColor(node => node.color)
                    .nodeVal(node => node.size)
                    .nodeThreeObject(node => {
                        // Create a group for node and label
                        const group = new THREE.Group();

                        // Create sphere for node
                        const sphereGeometry = new THREE.SphereGeometry(node.size || 8);
                        const sphereMaterial = new THREE.MeshLambertMaterial({ color: node.color });
                        const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
                        group.add(sphere);

                        // Create text label
                        const canvas = document.createElement('canvas');
                        const context = canvas.getContext('2d');
                        canvas.width = 256;
                        canvas.height = 64;

                        context.fillStyle = '#ffffff';
                        context.fillRect(0, 0, canvas.width, canvas.height);
                        context.fillStyle = '#000000';
                        context.font = '16px Arial';
                        context.textAlign = 'center';
                        context.fillText(node.name.length > 15 ? node.name.substring(0, 15) + '...' : node.name, 128, 40);

                        const texture = new THREE.CanvasTexture(canvas);
                        const spriteMaterial = new THREE.SpriteMaterial({ map: texture });
                        const sprite = new THREE.Sprite(spriteMaterial);
                        sprite.scale.set(40, 10, 1);
                        sprite.position.set(0, (node.size || 8) + 15, 0);
                        group.add(sprite);

                        return group;
                    })
                    .linkLabel('label')
                    .linkColor(link => link.color)
                    .linkWidth(2)
                    .linkOpacity(0.6)
                    .onNodeClick((node, event) => {
                        // Animate camera to optimal position for selected node
                        animateCameraToNode(node, Graph);

                        if (isEditMode) {
                            openCrudModal(node);
                        } else {
                            showNodeDetails(node);
                        }
                    })
                    .onNodeDrag((node) => {
                        if (isEditMode) {
                            // Node position is automatically updated by the 3D graph
                        }
                    })
                    .enableNodeDrag(isEditMode)
                    .showNavInfo(false)
                    .onEngineStop(() => {
                        // Ensure graph fits within container
                        Graph.zoomToFit(400, 100);
                    });

                currentGraph = Graph;

                // Enhanced controls with zoom-to-cursor and pan functionality
                const controls = Graph.controls();
                controls.enableDamping = true;
                controls.dampingFactor = 0.1;
                controls.enablePan = true;
                controls.enableZoom = true;
                controls.enableRotate = true;

                // Add zoom-to-cursor functionality
                const renderer = Graph.renderer();
                const camera = Graph.camera();

                container.addEventListener('wheel', (event) => {
                    event.preventDefault();

                    const rect = container.getBoundingClientRect();
                    const mouse = new THREE.Vector2();
                    mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
                    mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

                    const raycaster = new THREE.Raycaster();
                    raycaster.setFromCamera(mouse, camera);

                    const zoomFactor = event.deltaY > 0 ? 1.1 : 0.9;
                    const direction = new THREE.Vector3();
                    camera.getWorldDirection(direction);

                    camera.position.addScaledVector(direction, (zoomFactor - 1) * 50);
                    controls.update();
                });
            }

            function animateCameraToNode(node, graph) {
                const camera = graph.camera();
                const controls = graph.controls();

                // Calculate optimal camera position
                const nodePosition = new THREE.Vector3(node.x || 0, node.y || 0, node.z || 0);
                const distance = 200; // Optimal viewing distance
                const angle = Math.PI / 4; // 45-degree angle for best view

                // Calculate camera target position
                const targetPosition = new THREE.Vector3(
                    nodePosition.x + distance * Math.cos(angle),
                    nodePosition.y + distance * Math.sin(angle),
                    nodePosition.z + distance
                );

                // Animate camera movement with ease-out
                const startPosition = camera.position.clone();
                const startTarget = controls.target.clone();
                const duration = 1500; // 1.5 seconds
                const startTime = Date.now();

                function animate() {
                    const elapsed = Date.now() - startTime;
                    const progress = Math.min(elapsed / duration, 1);

                    // Ease-out cubic function
                    const easeOut = 1 - Math.pow(1 - progress, 3);

                    // Interpolate camera position
                    camera.position.lerpVectors(startPosition, targetPosition, easeOut);
                    controls.target.lerpVectors(startTarget, nodePosition, easeOut);

                    controls.update();

                    if (progress < 1) {
                        requestAnimationFrame(animate);
                    }
                }

                animate();
            }

            function toggleVisualizationMode() {
                is3DMode = !is3DMode;
                const toggleBtn = document.getElementById('mode-toggle');
                toggleBtn.textContent = is3DMode ? 'Switch to 2D' : 'Switch to 3D';

                if (currentGraphData) {
                    renderGraph(currentGraphData);
                }
            }

            function toggleEditMode() {
                isEditMode = !isEditMode;
                const toggleBtn = document.getElementById('edit-toggle');
                toggleBtn.textContent = isEditMode ? 'Disable Editing' : 'Enable Editing';
                toggleBtn.className = isEditMode ?
                    'px-3 py-1 text-sm bg-red-100 text-red-800 rounded hover:bg-red-200' :
                    'px-3 py-1 text-sm bg-green-100 text-green-800 rounded hover:bg-green-200';

                if (currentGraphData) {
                    renderGraph(currentGraphData);
                }
            }

            function resetGraphView() {
                if (is3DMode && currentGraph) {
                    currentGraph.cameraPosition({ x: 0, y: 0, z: 400 });
                } else {
                    // Reset 2D view
                    if (currentGraphData) {
                        renderGraph(currentGraphData);
                    }
                }
            }

            function centerGraph() {
                if (is3DMode && currentGraph) {
                    currentGraph.zoomToFit(400);
                } else {
                    // Center 2D view
                    resetGraphView();
                }
            }

            function showNodeDetails(node) {
                const detailsPanel = document.getElementById('node-details');
                const detailsContent = document.getElementById('node-details-content');

                let content = \`
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <h4 class="font-semibold text-blue-900">\${node.name}</h4>
                            <p class="text-sm text-blue-700">Type: \${node.type}</p>
                        </div>
                        <div class="text-right">
                            <button onclick="focusOnEntity('\${node.id}', '\${node.type}')" class="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 mr-2">
                                Focus on This Node
                            </button>
                            <button onclick="openCrudModal(node)" class="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700">
                                Edit Node
                            </button>
                        </div>
                    </div>
                    <div class="mt-3 text-sm text-blue-800">
                \`;

                // Add specific details based on node type
                if (node.type === 'jobSeeker') {
                    content += \`
                        <p><strong>Title:</strong> \${node.data.title}</p>
                        <p><strong>Experience:</strong> \${node.data.experience}</p>
                        <p><strong>Location:</strong> \${node.data.location}</p>
                        <p><strong>Email:</strong> \${node.data.email}</p>
                    \`;
                } else if (node.type === 'company') {
                    content += \`
                        <p><strong>Industry:</strong> \${node.data.industry}</p>
                        <p><strong>Size:</strong> \${node.data.size} (\${node.data.employeeCount} employees)</p>
                        <p><strong>Location:</strong> \${node.data.location}</p>
                        <p><strong>Description:</strong> \${node.data.description}</p>
                    \`;
                } else if (node.type === 'position') {
                    content += \`
                        <p><strong>Company:</strong> \${node.data.company}</p>
                        <p><strong>Level:</strong> \${node.data.level}</p>
                        <p><strong>Location:</strong> \${node.data.location}</p>
                        <p><strong>Salary:</strong> \${node.data.salary}</p>
                    \`;
                } else if (node.type === 'hiringAuthority') {
                    content += \`
                        <p><strong>Title:</strong> \${node.data.title}</p>
                        <p><strong>Department:</strong> \${node.data.department}</p>
                        <p><strong>Hiring Scope:</strong> \${node.data.hiringScope}</p>
                        <p><strong>Email:</strong> \${node.data.email}</p>
                    \`;
                } else if (node.type === 'skill') {
                    content += \`
                        <p><strong>Category:</strong> \${node.data.category}</p>
                    \`;
                }

                content += '</div>';
                detailsContent.innerHTML = content;
                detailsPanel.style.display = 'block';
            }

            function showGraphControls() {
                const controls = document.getElementById('graph-controls');
                controls.style.display = controls.style.display === 'none' ? 'block' : 'none';
            }

            async function populateGraphControls() {
                try {
                    // Load all entities for dropdowns
                    const [jobSeekers, companies, positions, hiringAuthorities] = await Promise.all([
                        fetch('/api/job-seekers').then(r => r.json()),
                        fetch('/api/companies').then(r => r.json()),
                        fetch('/api/positions').then(r => r.json()),
                        fetch('/api/hiring-authorities').then(r => r.json())
                    ]);

                    // Populate job seekers dropdown
                    const jobSeekerSelect = document.getElementById('jobseeker-select');
                    if (jobSeekers.success) {
                        jobSeekers.data.forEach(js => {
                            const option = document.createElement('option');
                            option.value = js._id;
                            option.textContent = js.name;
                            jobSeekerSelect.appendChild(option);
                        });
                    }

                    // Populate companies dropdown
                    const companySelect = document.getElementById('company-select');
                    if (companies.success) {
                        companies.data.forEach(c => {
                            const option = document.createElement('option');
                            option.value = c._id;
                            option.textContent = c.name;
                            companySelect.appendChild(option);
                        });
                    }

                    // Populate positions dropdown
                    const positionSelect = document.getElementById('position-select');
                    if (positions.success) {
                        positions.data.forEach(p => {
                            const option = document.createElement('option');
                            option.value = p._id;
                            option.textContent = p.title;
                            positionSelect.appendChild(option);
                        });
                    }

                    // Populate hiring authorities dropdown
                    const hiringSelect = document.getElementById('hiring-select');
                    if (hiringAuthorities.success) {
                        hiringAuthorities.data.forEach(ha => {
                            const option = document.createElement('option');
                            option.value = ha._id;
                            option.textContent = ha.name;
                            hiringSelect.appendChild(option);
                        });
                    }
                } catch (error) {
                    console.error('Error populating controls:', error);
                }
            }

            function focusOnEntity(entityId, entityType) {
                if (entityId) {
                    // Clear other dropdowns
                    document.querySelectorAll('#graph-controls select').forEach(select => {
                        if (select.value !== entityId) select.value = '';
                    });

                    loadAndRenderGraph(entityId, entityType);
                }
            }

            // Helper function to load matches for a specific job seeker
            async function loadMatchesForSeeker(seekerId) {
                try {
                    const response = await fetch('/api/matches');
                    const data = await response.json();

                    if (data.success) {
                        const seekerMatches = data.data.filter(match => match.jobSeeker._id === seekerId);

                        document.getElementById('content-area').innerHTML = \`
                            <div class="p-6">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">🎯 Matches for \${seekerMatches[0]?.jobSeeker.name || 'Job Seeker'}</h2>
                                <div class="space-y-4">
                                    \${seekerMatches.map(match => \`
                                        <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                                            <div class="flex justify-between items-start mb-4">
                                                <div>
                                                    <h3 class="font-semibold text-gray-900">\${match.position.title}</h3>
                                                    <p class="text-sm text-gray-600">\${match.company.name}</p>
                                                    <p class="text-sm text-gray-500">\${match.position.location} • \${match.position.salary}</p>
                                                </div>
                                                <div class="text-right">
                                                    <span class="text-2xl font-bold \${match.score >= 80 ? 'text-green-600' : match.score >= 60 ? 'text-yellow-600' : 'text-red-600'}">\${match.score}%</span>
                                                    <p class="text-xs text-gray-500">Match Score</p>
                                                </div>
                                            </div>
                                            <div class="border-t pt-4">
                                                <p class="text-sm text-gray-600">
                                                    <span class="font-medium">Contact:</span> \${match.hiringAuthority ? match.hiringAuthority.name : 'N/A'}
                                                    \${match.hiringAuthority ? '(' + match.hiringAuthority.title + ')' : ''}
                                                </p>
                                                <div class="mt-2">
                                                    <button onclick="loadGraphVisualization('\${match.position._id}', 'position')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200">
                                                        🌐 View Position Graph
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    \`).join('')}
                                </div>
                            </div>
                        \`;
                    }
                } catch (error) {
                    console.error('Error loading seeker matches:', error);
                }
            }

            // CRUD Modal Functions
            function openCrudModal(node) {
                selectedNode = node;
                const modal = document.getElementById('crud-modal');
                const title = document.getElementById('modal-title');
                const formFields = document.getElementById('form-fields');

                title.textContent = \`Edit \${node.type}: \${node.name}\`;

                // Generate form fields based on node type
                let fieldsHTML = '';
                const data = node.data;

                // Common fields for all node types
                if (data.name) {
                    fieldsHTML += \`
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                            <input type="text" name="name" value="\${data.name}" class="w-full p-2 border border-gray-300 rounded-md">
                        </div>
                    \`;
                }

                if (data.title) {
                    fieldsHTML += \`
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Title</label>
                            <input type="text" name="title" value="\${data.title}" class="w-full p-2 border border-gray-300 rounded-md">
                        </div>
                    \`;
                }

                if (data.email) {
                    fieldsHTML += \`
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input type="email" name="email" value="\${data.email}" class="w-full p-2 border border-gray-300 rounded-md">
                        </div>
                    \`;
                }

                if (data.location) {
                    fieldsHTML += \`
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Location</label>
                            <input type="text" name="location" value="\${data.location}" class="w-full p-2 border border-gray-300 rounded-md">
                        </div>
                    \`;
                }

                // Type-specific fields
                if (node.type === 'jobSeeker') {
                    fieldsHTML += \`
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Experience Level</label>
                            <select name="experience" class="w-full p-2 border border-gray-300 rounded-md">
                                <option value="Junior" \${data.experience === 'Junior' ? 'selected' : ''}>Junior</option>
                                <option value="Mid-level" \${data.experience === 'Mid-level' ? 'selected' : ''}>Mid-level</option>
                                <option value="Senior" \${data.experience === 'Senior' ? 'selected' : ''}>Senior</option>
                            </select>
                        </div>
                    \`;
                } else if (node.type === 'company') {
                    fieldsHTML += \`
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Industry</label>
                            <input type="text" name="industry" value="\${data.industry || ''}" class="w-full p-2 border border-gray-300 rounded-md">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Employee Count</label>
                            <input type="number" name="employeeCount" value="\${data.employeeCount || ''}" class="w-full p-2 border border-gray-300 rounded-md">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Size</label>
                            <select name="size" class="w-full p-2 border border-gray-300 rounded-md">
                                <option value="Small" \${data.size === 'Small' ? 'selected' : ''}>Small</option>
                                <option value="Medium" \${data.size === 'Medium' ? 'selected' : ''}>Medium</option>
                                <option value="Large" \${data.size === 'Large' ? 'selected' : ''}>Large</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                            <textarea name="description" rows="3" class="w-full p-2 border border-gray-300 rounded-md">\${data.description || ''}</textarea>
                        </div>
                    \`;
                } else if (node.type === 'position') {
                    fieldsHTML += \`
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Level</label>
                            <select name="level" class="w-full p-2 border border-gray-300 rounded-md">
                                <option value="Junior" \${data.level === 'Junior' ? 'selected' : ''}>Junior</option>
                                <option value="Mid-level" \${data.level === 'Mid-level' ? 'selected' : ''}>Mid-level</option>
                                <option value="Senior" \${data.level === 'Senior' ? 'selected' : ''}>Senior</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Salary</label>
                            <input type="text" name="salary" value="\${data.salary || ''}" class="w-full p-2 border border-gray-300 rounded-md">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Company</label>
                            <input type="text" name="company" value="\${data.company || ''}" class="w-full p-2 border border-gray-300 rounded-md">
                        </div>
                    \`;
                } else if (node.type === 'hiringAuthority') {
                    fieldsHTML += \`
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Level</label>
                            <select name="level" class="w-full p-2 border border-gray-300 rounded-md">
                                <option value="Manager" \${data.level === 'Manager' ? 'selected' : ''}>Manager</option>
                                <option value="Director" \${data.level === 'Director' ? 'selected' : ''}>Director</option>
                                <option value="VP" \${data.level === 'VP' ? 'selected' : ''}>VP</option>
                                <option value="CTO" \${data.level === 'CTO' ? 'selected' : ''}>CTO</option>
                                <option value="CEO" \${data.level === 'CEO' ? 'selected' : ''}>CEO</option>
                                <option value="Founder" \${data.level === 'Founder' ? 'selected' : ''}>Founder</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Department</label>
                            <input type="text" name="department" value="\${data.department || ''}" class="w-full p-2 border border-gray-300 rounded-md">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Hiring Scope</label>
                            <input type="text" name="hiringScope" value="\${data.hiringScope || ''}" class="w-full p-2 border border-gray-300 rounded-md">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Company</label>
                            <input type="text" name="company" value="\${data.company || ''}" class="w-full p-2 border border-gray-300 rounded-md">
                        </div>
                    \`;
                } else if (node.type === 'skill') {
                    fieldsHTML += \`
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                            <select name="category" class="w-full p-2 border border-gray-300 rounded-md">
                                <option value="Programming" \${data.category === 'Programming' ? 'selected' : ''}>Programming</option>
                                <option value="Frontend" \${data.category === 'Frontend' ? 'selected' : ''}>Frontend</option>
                                <option value="Backend" \${data.category === 'Backend' ? 'selected' : ''}>Backend</option>
                                <option value="Database" \${data.category === 'Database' ? 'selected' : ''}>Database</option>
                                <option value="DevOps" \${data.category === 'DevOps' ? 'selected' : ''}>DevOps</option>
                                <option value="Cloud" \${data.category === 'Cloud' ? 'selected' : ''}>Cloud</option>
                                <option value="API" \${data.category === 'API' ? 'selected' : ''}>API</option>
                            </select>
                        </div>
                    \`;
                }

                formFields.innerHTML = fieldsHTML;
                modal.style.display = 'block';

                // Setup form submission
                const form = document.getElementById('crud-form');
                form.onsubmit = async (e) => {
                    e.preventDefault();
                    await saveNodeChanges();
                };
            }

            function closeCrudModal() {
                document.getElementById('crud-modal').style.display = 'none';
                selectedNode = null;
            }

            async function saveNodeChanges() {
                if (!selectedNode) return;

                const form = document.getElementById('crud-form');
                const formData = new FormData(form);
                const updateData = {};

                for (let [key, value] of formData.entries()) {
                    updateData[key] = value;
                }

                try {
                    const collection = selectedNode.type === 'jobSeeker' ? 'jobSeekers' :
                                     selectedNode.type === 'company' ? 'companies' :
                                     selectedNode.type === 'position' ? 'positions' :
                                     selectedNode.type === 'hiringAuthority' ? 'hiringAuthorities' : 'skills';

                    const response = await fetch(\`/api/node/\${collection}/\${selectedNode.data._key}\`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(updateData)
                    });

                    const result = await response.json();

                    if (result.success) {
                        // Update the node data
                        Object.assign(selectedNode.data, updateData);
                        selectedNode.name = updateData.name || updateData.title || selectedNode.name;

                        // Refresh the graph
                        if (currentGraphData) {
                            renderGraph(currentGraphData);
                        }

                        closeCrudModal();
                        alert('Node updated successfully!');
                    } else {
                        alert('Error updating node: ' + result.error);
                    }
                } catch (error) {
                    console.error('Error saving node:', error);
                    alert('Error saving changes');
                }
            }

            async function deleteNode() {
                if (!selectedNode || !confirm('Are you sure you want to delete this node?')) return;

                try {
                    const collection = selectedNode.type === 'jobSeeker' ? 'jobSeekers' :
                                     selectedNode.type === 'company' ? 'companies' :
                                     selectedNode.type === 'position' ? 'positions' :
                                     selectedNode.type === 'hiringAuthority' ? 'hiringAuthorities' : 'skills';

                    const response = await fetch(\`/api/node/\${collection}/\${selectedNode.data._key}\`, {
                        method: 'DELETE'
                    });

                    const result = await response.json();

                    if (result.success) {
                        closeCrudModal();
                        alert('Node deleted successfully!');
                        // Reload the graph data
                        await loadAndRenderGraph();
                    } else {
                        alert('Error deleting node: ' + result.error);
                    }
                } catch (error) {
                    console.error('Error deleting node:', error);
                    alert('Error deleting node');
                }
            }

            // Load hiring authority matches
            async function loadHiringAuthorityMatches(authorityId) {
                try {
                    const response = await fetch(\`/api/hiring-matches/\${authorityId}\`);
                    const data = await response.json();

                    if (data.success) {
                        const { authority, positions, matches } = data.data;

                        document.getElementById('content-area').innerHTML = \`
                            <div class="p-6">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">🎯 Best Matches for \${authority.name}</h2>
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                                    <h3 class="font-semibold text-blue-900 mb-2">Hiring Authority Details</h3>
                                    <p class="text-blue-800"><strong>Title:</strong> \${authority.title}</p>
                                    <p class="text-blue-800"><strong>Department:</strong> \${authority.department}</p>
                                    <p class="text-blue-800"><strong>Hiring Scope:</strong> \${authority.hiringScope}</p>
                                    <p class="text-blue-800"><strong>Managed Positions:</strong> \${positions.length}</p>
                                    <div class="mt-3">
                                        <button onclick="loadGraphVisualization('\${authority._id}', 'hiringAuthority')" class="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700">
                                            🌐 View Authority Graph
                                        </button>
                                    </div>
                                </div>

                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Candidate Matches (\${matches.length})</h3>
                                <div class="space-y-4">
                                    \${matches.map(match => \`
                                        <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                                            <div class="flex justify-between items-start mb-4">
                                                <div>
                                                    <h4 class="font-semibold text-gray-900">\${match.jobSeeker.name}</h4>
                                                    <p class="text-sm text-gray-600">\${match.jobSeeker.title}</p>
                                                    <p class="text-sm text-gray-500">\${match.jobSeeker.location} • \${match.jobSeeker.experience}</p>
                                                </div>
                                                <div class="text-right">
                                                    <span class="text-2xl font-bold \${match.score >= 80 ? 'text-green-600' : match.score >= 60 ? 'text-yellow-600' : 'text-red-600'}">\${match.score}%</span>
                                                    <p class="text-xs text-gray-500">Match Score</p>
                                                </div>
                                            </div>
                                            <div class="border-t pt-4">
                                                <h5 class="font-medium text-gray-900">\${match.position.title}</h5>
                                                <p class="text-sm text-gray-500">\${match.position.location} • \${match.position.salary}</p>
                                                <div class="mt-3 flex space-x-2">
                                                    <button onclick="loadGraphVisualization('\${match.jobSeeker._id}', 'jobSeeker')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">
                                                        🌐 View Candidate Graph
                                                    </button>
                                                    <button onclick="loadGraphVisualization('\${match.position._id}', 'position')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200">
                                                        🌐 View Position Graph
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    \`).join('')}
                                </div>
                            </div>
                        \`;
                    }
                } catch (error) {
                    console.error('Error loading hiring authority matches:', error);
                }
            }

            // Helper function to load candidates for a specific company
            async function loadMatchesForCompany(companyId) {
                try {
                    const response = await fetch('/api/matches');
                    const data = await response.json();

                    if (data.success) {
                        const companyMatches = data.data.filter(match => match.company._id === companyId);

                        document.getElementById('content-area').innerHTML = \`
                            <div class="p-6">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">🎯 Candidates for \${companyMatches[0]?.company.name || 'Company'}</h2>
                                <div class="space-y-4">
                                    \${companyMatches.map(match => \`
                                        <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                                            <div class="flex justify-between items-start mb-4">
                                                <div>
                                                    <h3 class="font-semibold text-gray-900">\${match.jobSeeker.name}</h3>
                                                    <p class="text-sm text-gray-600">\${match.jobSeeker.title}</p>
                                                    <p class="text-sm text-gray-500">\${match.jobSeeker.location} • \${match.jobSeeker.experience}</p>
                                                </div>
                                                <div class="text-right">
                                                    <span class="text-2xl font-bold \${match.score >= 80 ? 'text-green-600' : match.score >= 60 ? 'text-yellow-600' : 'text-red-600'}">\${match.score}%</span>
                                                    <p class="text-xs text-gray-500">Match Score</p>
                                                </div>
                                            </div>
                                            <div class="border-t pt-4">
                                                <h4 class="font-medium text-gray-900">\${match.position.title}</h4>
                                                <p class="text-sm text-gray-500">\${match.position.location} • \${match.position.salary}</p>
                                                <div class="mt-2">
                                                    <button onclick="loadGraphVisualization('\${match.jobSeeker._id}', 'jobSeeker')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">
                                                        🌐 View Candidate Graph
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    \`).join('')}
                                </div>
                            </div>
                        \`;
                    }
                } catch (error) {
                    console.error('Error loading company matches:', error);
                }
            }

            // Load stats on page load
            loadStats();
        </script>
    </body>
    </html>
  `);
});

app.listen(PORT, () => {
  console.log(`
🚀 Candid Connections Server Running!

📍 URL: http://localhost:${PORT}
🎯 Status: Ready for testing
📊 Database: Connected to ArangoDB
🔧 API Endpoints: /api/stats, /api/job-seekers, /api/companies, /api/positions, /api/matches

✨ Open your browser and navigate to http://localhost:${PORT} to test the application!
  `);
});
