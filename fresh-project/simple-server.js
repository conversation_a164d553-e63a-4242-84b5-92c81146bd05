#!/usr/bin/env node

const express = require('express');
const { Database } = require('arangojs');
const path = require('path');

const app = express();
const PORT = 3002;

// Database connection
const db = new Database({
  url: 'https://f2c8a97499a8.arangodb.cloud:8529',
  auth: { 
    username: 'root', 
    password: 'XMx2qSsHU8RWMX9VSxAx' 
  },
  databaseName: 'candid_connections'
});

app.use(express.json());
app.use(express.static('public'));

// API Routes

// Get dashboard stats
app.get('/api/stats', async (req, res) => {
  try {
    const [jobSeekers, companies, hiringAuthorities, positions, skills, connections] = await Promise.all([
      db.collection('jobSeekers').count(),
      db.collection('companies').count(),
      db.collection('hiringAuthorities').count(),
      db.collection('positions').count(),
      db.collection('skills').count(),
      db.collection('seekerSkills').count()
    ]);

    res.json({
      success: true,
      data: {
        jobSeekers: jobSeekers.count,
        companies: companies.count,
        hiringAuthorities: hiringAuthorities.count,
        positions: positions.count,
        skills: skills.count,
        connections: connections.count
      }
    });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get all job seekers
app.get('/api/job-seekers', async (req, res) => {
  try {
    const cursor = await db.query('FOR doc IN jobSeekers RETURN doc');
    const jobSeekers = await cursor.all();
    res.json({ success: true, data: jobSeekers });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get all companies
app.get('/api/companies', async (req, res) => {
  try {
    const cursor = await db.query('FOR doc IN companies RETURN doc');
    const companies = await cursor.all();
    res.json({ success: true, data: companies });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get all hiring authorities
app.get('/api/hiring-authorities', async (req, res) => {
  try {
    const cursor = await db.query('FOR doc IN hiringAuthorities RETURN doc');
    const hiringAuthorities = await cursor.all();
    res.json({ success: true, data: hiringAuthorities });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get all positions
app.get('/api/positions', async (req, res) => {
  try {
    const cursor = await db.query('FOR doc IN positions RETURN doc');
    const positions = await cursor.all();
    res.json({ success: true, data: positions });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get job matches
app.get('/api/matches', async (req, res) => {
  try {
    const matchQuery = `
      WITH skills, companies, hiringAuthorities
      FOR seeker IN jobSeekers
        FOR position IN positions
          LET company = FIRST(FOR c IN companies FILTER c._key == position.company RETURN c)
          LET hiringAuthority = FIRST(
            FOR ha IN hiringAuthorities 
            FILTER ha.company == position.company 
            RETURN ha
          )
          LET seekerSkills = (
            FOR skill, edge IN 1..1 OUTBOUND seeker seekerSkills
              RETURN {
                skill: skill.name,
                proficiency: edge.proficiency
              }
          )
          LET positionSkills = (
            FOR skill, edge IN 1..1 OUTBOUND position positionSkills
              RETURN {
                skill: skill.name,
                required: edge.required,
                level: edge.level
              }
          )
          LET skillMatches = (
            FOR posReq IN positionSkills
              LET seekerSkill = FIRST(FOR s IN seekerSkills FILTER s.skill == posReq.skill RETURN s)
              RETURN {
                skill: posReq.skill,
                seekerProficiency: seekerSkill ? seekerSkill.proficiency : "None",
                requiredLevel: posReq.level,
                match: seekerSkill != null,
                weight: posReq.required ? 3 : 1
              }
          )
          LET totalScore = SUM(FOR sm IN skillMatches FILTER sm.match RETURN sm.weight)
          LET maxScore = SUM(FOR sm IN skillMatches RETURN sm.weight)
          LET score = maxScore > 0 ? ROUND((totalScore / maxScore) * 100) : 0
          FILTER score > 0
          SORT score DESC
          LIMIT 20
          RETURN {
            jobSeeker: seeker,
            position: position,
            company: company,
            hiringAuthority: hiringAuthority,
            score: score,
            skillMatches: skillMatches,
            locationMatch: seeker.location == position.location,
            experienceMatch: true
          }
    `;
    
    const cursor = await db.query(matchQuery);
    const matches = await cursor.all();
    
    res.json({ success: true, data: matches });
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

// Serve the main page
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🤝 Candid Connections - Job Matching Platform</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <style>
            .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        </style>
    </head>
    <body class="bg-gray-50">
        <div class="min-h-screen">
            <!-- Header -->
            <header class="gradient-bg text-white shadow-lg">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between items-center py-6">
                        <h1 class="text-3xl font-bold">🤝 Candid Connections</h1>
                        <p class="text-lg">Intelligent Job Matching Platform</p>
                    </div>
                </div>
            </header>

            <!-- Main Content -->
            <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div class="px-4 py-6 sm:px-0">
                    <!-- Stats Grid -->
                    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-6 mb-8" id="stats-grid">
                        <!-- Stats will be loaded here -->
                    </div>

                    <!-- Navigation Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadJobSeekers()">
                            <div class="text-3xl mb-3">👤</div>
                            <h3 class="text-lg font-semibold text-gray-900">Job Seekers</h3>
                            <p class="text-sm text-gray-600">View all job seekers and their profiles</p>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadCompanies()">
                            <div class="text-3xl mb-3">🏢</div>
                            <h3 class="text-lg font-semibold text-gray-900">Companies</h3>
                            <p class="text-sm text-gray-600">Browse companies and open positions</p>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadPositions()">
                            <div class="text-3xl mb-3">💼</div>
                            <h3 class="text-lg font-semibold text-gray-900">Positions</h3>
                            <p class="text-sm text-gray-600">View all available job positions</p>
                        </div>
                        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadMatches()">
                            <div class="text-3xl mb-3">🎯</div>
                            <h3 class="text-lg font-semibold text-gray-900">Job Matches</h3>
                            <p class="text-sm text-gray-600">See intelligent job matching results</p>
                        </div>
                    </div>

                    <!-- Content Area -->
                    <div class="bg-white rounded-lg shadow" id="content-area">
                        <div class="p-6">
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">Welcome to Candid Connections</h2>
                            <p class="text-gray-600 mb-4">
                                This intelligent job matching platform uses advanced graph database technology to connect 
                                job seekers with the perfect opportunities through relationship mapping and smart algorithms.
                            </p>
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <h3 class="font-semibold text-blue-900 mb-2">🚀 Platform Features:</h3>
                                <ul class="text-blue-800 space-y-1">
                                    <li>• Intelligent job matching based on skills, experience, and location</li>
                                    <li>• Company hierarchy modeling for appropriate hiring authority routing</li>
                                    <li>• Graph database relationships for complex data modeling</li>
                                    <li>• Real-time statistics and comprehensive data management</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <script>
            // Load dashboard stats
            async function loadStats() {
                try {
                    const response = await fetch('/api/stats');
                    const data = await response.json();
                    
                    if (data.success) {
                        const stats = data.data;
                        document.getElementById('stats-grid').innerHTML = \`
                            <div class="bg-white overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="text-3xl">👤</div>
                                        <div class="ml-5">
                                            <dt class="text-sm font-medium text-gray-500">Job Seekers</dt>
                                            <dd class="text-lg font-medium text-gray-900">\${stats.jobSeekers}</dd>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="text-3xl">🏢</div>
                                        <div class="ml-5">
                                            <dt class="text-sm font-medium text-gray-500">Companies</dt>
                                            <dd class="text-lg font-medium text-gray-900">\${stats.companies}</dd>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="text-3xl">👔</div>
                                        <div class="ml-5">
                                            <dt class="text-sm font-medium text-gray-500">Hiring Authorities</dt>
                                            <dd class="text-lg font-medium text-gray-900">\${stats.hiringAuthorities}</dd>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="text-3xl">💼</div>
                                        <div class="ml-5">
                                            <dt class="text-sm font-medium text-gray-500">Positions</dt>
                                            <dd class="text-lg font-medium text-gray-900">\${stats.positions}</dd>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="text-3xl">🎯</div>
                                        <div class="ml-5">
                                            <dt class="text-sm font-medium text-gray-500">Skills</dt>
                                            <dd class="text-lg font-medium text-gray-900">\${stats.skills}</dd>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white overflow-hidden shadow rounded-lg">
                                <div class="p-5">
                                    <div class="flex items-center">
                                        <div class="text-3xl">🔗</div>
                                        <div class="ml-5">
                                            <dt class="text-sm font-medium text-gray-500">Connections</dt>
                                            <dd class="text-lg font-medium text-gray-900">\${stats.connections}</dd>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        \`;
                    }
                } catch (error) {
                    console.error('Error loading stats:', error);
                }
            }

            // Load job seekers
            async function loadJobSeekers() {
                try {
                    const response = await fetch('/api/job-seekers');
                    const data = await response.json();
                    
                    if (data.success) {
                        const jobSeekers = data.data;
                        document.getElementById('content-area').innerHTML = \`
                            <div class="p-6">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">👤 Job Seekers (\${jobSeekers.length})</h2>
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    \${jobSeekers.map(seeker => \`
                                        <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                                            <h3 class="font-semibold text-gray-900">\${seeker.name}</h3>
                                            <p class="text-sm text-gray-600">\${seeker.title}</p>
                                            <p class="text-sm text-gray-500">\${seeker.location}</p>
                                            <p class="text-xs text-gray-400 mt-2">Experience: \${seeker.experience}</p>
                                        </div>
                                    \`).join('')}
                                </div>
                            </div>
                        \`;
                    }
                } catch (error) {
                    console.error('Error loading job seekers:', error);
                }
            }

            // Load companies
            async function loadCompanies() {
                try {
                    const response = await fetch('/api/companies');
                    const data = await response.json();
                    
                    if (data.success) {
                        const companies = data.data;
                        document.getElementById('content-area').innerHTML = \`
                            <div class="p-6">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">🏢 Companies (\${companies.length})</h2>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    \${companies.map(company => \`
                                        <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                                            <h3 class="font-semibold text-gray-900 text-lg">\${company.name}</h3>
                                            <p class="text-sm text-gray-600 mb-2">\${company.industry}</p>
                                            <p class="text-sm text-gray-500 mb-2">\${company.location}</p>
                                            <div class="flex justify-between items-center">
                                                <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">\${company.size}</span>
                                                <span class="text-xs text-gray-500">\${company.employeeCount} employees</span>
                                            </div>
                                            <p class="text-sm text-gray-600 mt-3">\${company.description}</p>
                                        </div>
                                    \`).join('')}
                                </div>
                            </div>
                        \`;
                    }
                } catch (error) {
                    console.error('Error loading companies:', error);
                }
            }

            // Load positions
            async function loadPositions() {
                try {
                    const response = await fetch('/api/positions');
                    const data = await response.json();
                    
                    if (data.success) {
                        const positions = data.data;
                        document.getElementById('content-area').innerHTML = \`
                            <div class="p-6">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">💼 Open Positions (\${positions.length})</h2>
                                <div class="space-y-4">
                                    \${positions.map(position => \`
                                        <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                                            <div class="flex justify-between items-start">
                                                <div>
                                                    <h3 class="font-semibold text-gray-900 text-lg">\${position.title}</h3>
                                                    <p class="text-sm text-gray-600">\${position.company}</p>
                                                    <p class="text-sm text-gray-500">\${position.location}</p>
                                                </div>
                                                <div class="text-right">
                                                    <span class="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">\${position.level}</span>
                                                    <p class="text-sm font-medium text-green-600 mt-1">\${position.salary}</p>
                                                </div>
                                            </div>
                                        </div>
                                    \`).join('')}
                                </div>
                            </div>
                        \`;
                    }
                } catch (error) {
                    console.error('Error loading positions:', error);
                }
            }

            // Load matches
            async function loadMatches() {
                document.getElementById('content-area').innerHTML = \`
                    <div class="p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">🎯 Loading Job Matches...</h2>
                        <div class="animate-pulse">
                            <div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                            <div class="h-4 bg-gray-200 rounded w-1/2"></div>
                        </div>
                    </div>
                \`;
                
                try {
                    const response = await fetch('/api/matches');
                    const data = await response.json();
                    
                    if (data.success) {
                        const matches = data.data;
                        document.getElementById('content-area').innerHTML = \`
                            <div class="p-6">
                                <h2 class="text-2xl font-bold text-gray-900 mb-4">🎯 Job Matches (\${matches.length})</h2>
                                <div class="space-y-4">
                                    \${matches.map(match => \`
                                        <div class="border border-gray-200 rounded-lg p-6 hover:bg-gray-50">
                                            <div class="flex justify-between items-start mb-4">
                                                <div>
                                                    <h3 class="font-semibold text-gray-900">\${match.jobSeeker.name}</h3>
                                                    <p class="text-sm text-gray-600">\${match.jobSeeker.title}</p>
                                                    <p class="text-sm text-gray-500">\${match.jobSeeker.location}</p>
                                                </div>
                                                <div class="text-right">
                                                    <span class="text-2xl font-bold \${match.score >= 80 ? 'text-green-600' : match.score >= 60 ? 'text-yellow-600' : 'text-red-600'}">\${match.score}%</span>
                                                    <p class="text-xs text-gray-500">Match Score</p>
                                                </div>
                                            </div>
                                            <div class="border-t pt-4">
                                                <h4 class="font-medium text-gray-900">\${match.position.title}</h4>
                                                <p class="text-sm text-gray-600">\${match.company.name}</p>
                                                <p class="text-sm text-gray-500">\${match.position.location} • \${match.position.salary}</p>
                                            </div>
                                            <div class="mt-4 pt-4 border-t">
                                                <p class="text-sm text-gray-600">
                                                    <span class="font-medium">Contact:</span> \${match.hiringAuthority ? match.hiringAuthority.name : 'N/A'} 
                                                    \${match.hiringAuthority ? '(' + match.hiringAuthority.title + ')' : ''}
                                                </p>
                                            </div>
                                        </div>
                                    \`).join('')}
                                </div>
                            </div>
                        \`;
                    }
                } catch (error) {
                    console.error('Error loading matches:', error);
                    document.getElementById('content-area').innerHTML = \`
                        <div class="p-6">
                            <h2 class="text-2xl font-bold text-red-600 mb-4">❌ Error Loading Matches</h2>
                            <p class="text-gray-600">There was an error loading the job matches. Please try again.</p>
                        </div>
                    \`;
                }
            }

            // Load stats on page load
            loadStats();
        </script>
    </body>
    </html>
  `);
});

app.listen(PORT, () => {
  console.log(`
🚀 Candid Connections Server Running!

📍 URL: http://localhost:${PORT}
🎯 Status: Ready for testing
📊 Database: Connected to ArangoDB
🔧 API Endpoints: /api/stats, /api/job-seekers, /api/companies, /api/positions, /api/matches

✨ Open your browser and navigate to http://localhost:${PORT} to test the application!
  `);
});
