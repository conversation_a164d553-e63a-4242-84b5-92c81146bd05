#!/bin/bash

# Colors for output
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
NC="\033[0m" # No Color

echo -e "${YELLOW}Starting Candid Connections local setup...${NC}"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
  echo -e "${RED}Docker is not running. Please start Docker and try again.${NC}"
  exit 1
fi

# Install dependencies
echo -e "${YELLOW}Installing dependencies...${NC}"
npm install

# Install ArangoDB CLI tools
echo -e "${YELLOW}Installing ArangoDB CLI tools...${NC}"
./scripts/install-arango-tools.sh

# Create .env.local if it doesn't exist
if [ ! -f .env.local ]; then
  echo -e "${YELLOW}Creating .env.local file...${NC}"
  cat > .env.local << EOL
NEXT_PUBLIC_API_URL=http://localhost:3000
ARANGO_URL=http://localhost:8529
ARANGO_USERNAME=root
ARANGO_PASSWORD=rootpassword
ARANGO_DB_NAME=candid_connections
EOL
fi

# Start ArangoDB
echo -e "${YELLOW}Starting ArangoDB...${NC}"
cd docker && docker-compose up -d && cd ..

# Wait for ArangoDB to be ready
echo -e "${YELLOW}Waiting for ArangoDB to be ready...${NC}"
until $(curl --output /dev/null --silent --head --fail http://localhost:8529); do
  printf '.'
  sleep 2
done
echo -e "\n${GREEN}ArangoDB is ready!${NC}"

# Initialize database
echo -e "${YELLOW}Initializing database...${NC}"
node scripts/init-db.js

# Seed database
echo -e "${YELLOW}Seeding database with sample data...${NC}"
node scripts/seed-data.js

echo -e "${GREEN}Setup complete! Run 'npm run dev' to start the development server.${NC}"
