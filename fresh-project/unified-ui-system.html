<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤝 Candid Connections - Unified UI System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
    <script src="https://unpkg.com/3d-force-graph@1.73.0/dist/3d-force-graph.min.js"></script>
    <script src="https://unpkg.com/d3-force@3.0.0/dist/d3-force.min.js"></script>
    <style>
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .modal-overlay { background: rgba(0, 0, 0, 0.8); backdrop-filter: blur(4px); }
        .bento-card {
            transition: all 0.2s ease-in-out;
            border: 1px solid #e5e7eb;
            background: white;
        }
        .bento-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border-color: #d1d5db;
        }
        .viz-modal {
            max-width: 95vw;
            max-height: 95vh;
            width: 1400px;
            height: 900px;
        }
        .viz-container {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="gradient-bg text-white shadow-lg">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <h1 class="text-3xl font-bold">🤝 Candid Connections</h1>
                    <p class="text-lg">Unified Intelligence Platform</p>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <div class="px-4 py-6 sm:px-0">
                <!-- Unified Bento Navigation Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-3 mb-6">

                    <!-- 1. Job Matches Card -->
                    <div class="bento-card rounded-lg p-4 cursor-pointer relative" onclick="loadEntityData('matches')">
                        <div class="flex justify-between items-start mb-2">
                            <div class="text-2xl">🎯</div>
                            <span id="matches-count" class="text-sm font-bold text-emerald-600">-</span>
                        </div>
                        <h3 class="text-sm font-semibold text-gray-900 mb-1">Job Matches</h3>
                        <p class="text-xs text-gray-600 mb-2">Intelligent matching results</p>

                        <!-- Smart Preview -->
                        <div class="bg-emerald-50 rounded p-2 mb-2">
                            <div class="text-xs text-emerald-700">
                                <span class="font-medium">Top Score: <span id="matches-top-score">-</span>%</span>
                            </div>
                        </div>

                        <!-- Universal Actions -->
                        <div class="flex space-x-1">
                            <button onclick="event.stopPropagation(); toggleEntityActions('matches')" class="flex-1 px-2 py-1 bg-emerald-50 text-emerald-800 rounded text-xs hover:bg-emerald-100 transition-all">
                                Actions ▼
                            </button>
                            <button onclick="event.stopPropagation(); openVisualizationModal('matches', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200" title="2D visualization">📊</button>
                            <button onclick="event.stopPropagation(); openVisualizationModal('matches', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200" title="3D visualization">🌐</button>
                        </div>

                        <!-- Universal Actions Dropdown -->
                        <div id="matches-actions-menu" class="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-20 overflow-hidden" style="display: none;">
                            <div class="p-2 space-y-1">
                                <div class="text-xs font-medium text-gray-500 mb-1">Analysis</div>
                                <button onclick="event.stopPropagation(); loadEntityAnalysis('matches'); closeEntityActions('matches')" class="w-full px-2 py-1 text-left text-xs hover:bg-emerald-50 rounded">📈 Match Analytics</button>
                                <button onclick="event.stopPropagation(); loadEntityFilters('matches'); closeEntityActions('matches')" class="w-full px-2 py-1 text-left text-xs hover:bg-emerald-50 rounded">🔍 Filter Matches</button>
                                <hr class="my-2 border-gray-100">
                                <div class="text-xs font-medium text-gray-500 mb-1">Visualizations</div>
                                <button onclick="event.stopPropagation(); openVisualizationModal('matches', '2d'); closeEntityActions('matches')" class="w-full px-2 py-1 text-left text-xs hover:bg-blue-50 rounded">📊 2D Network View</button>
                                <button onclick="event.stopPropagation(); openVisualizationModal('matches', '3d'); closeEntityActions('matches')" class="w-full px-2 py-1 text-left text-xs hover:bg-purple-50 rounded">🌐 3D Network View</button>
                            </div>
                        </div>
                    </div>

                    <!-- 2. Job Seekers Card -->
                    <div class="bento-card rounded-lg p-4 cursor-pointer relative" onclick="loadEntityData('jobSeekers')">
                        <div class="flex justify-between items-start mb-2">
                            <div class="text-2xl">👤</div>
                            <span id="job-seekers-count" class="text-sm font-bold text-blue-600">-</span>
                        </div>
                        <h3 class="text-sm font-semibold text-gray-900 mb-1">Job Seekers</h3>
                        <p class="text-xs text-gray-600 mb-2">Candidate profiles & skills</p>

                        <!-- Smart Preview -->
                        <div class="bg-blue-50 rounded p-2 mb-2">
                            <div class="text-xs text-blue-700">
                                <span class="font-medium">Active: <span id="seekers-active">-</span></span>
                            </div>
                        </div>

                        <!-- Universal Actions -->
                        <div class="flex space-x-1">
                            <button onclick="event.stopPropagation(); toggleEntityActions('jobSeekers')" class="flex-1 px-2 py-1 bg-blue-50 text-blue-800 rounded text-xs hover:bg-blue-100 transition-all">
                                Actions ▼
                            </button>
                            <button onclick="event.stopPropagation(); openVisualizationModal('jobSeekers', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200" title="2D visualization">📊</button>
                            <button onclick="event.stopPropagation(); openVisualizationModal('jobSeekers', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200" title="3D visualization">🌐</button>
                        </div>

                        <!-- Universal Actions Dropdown -->
                        <div id="jobSeekers-actions-menu" class="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-20 overflow-hidden" style="display: none;">
                            <div class="p-2 space-y-1">
                                <div class="text-xs font-medium text-gray-500 mb-1">Management</div>
                                <button onclick="event.stopPropagation(); loadEntityCRUD('jobSeekers'); closeEntityActions('jobSeekers')" class="w-full px-2 py-1 text-left text-xs hover:bg-blue-50 rounded">✏️ Manage Profiles</button>
                                <button onclick="event.stopPropagation(); loadEntitySkills('jobSeekers'); closeEntityActions('jobSeekers')" class="w-full px-2 py-1 text-left text-xs hover:bg-blue-50 rounded">⚡ Skills Analysis</button>
                                <hr class="my-2 border-gray-100">
                                <div class="text-xs font-medium text-gray-500 mb-1">Visualizations</div>
                                <button onclick="event.stopPropagation(); openVisualizationModal('jobSeekers', '2d'); closeEntityActions('jobSeekers')" class="w-full px-2 py-1 text-left text-xs hover:bg-blue-50 rounded">📊 2D Skills Network</button>
                                <button onclick="event.stopPropagation(); openVisualizationModal('jobSeekers', '3d'); closeEntityActions('jobSeekers')" class="w-full px-2 py-1 text-left text-xs hover:bg-purple-50 rounded">🌐 3D Skills Network</button>
                            </div>
                        </div>
                    </div>

                    <!-- 3. Companies Card -->
                    <div class="bento-card rounded-lg p-4 cursor-pointer relative" onclick="loadEntityData('companies')">
                        <div class="flex justify-between items-start mb-2">
                            <div class="text-2xl">🏢</div>
                            <span id="companies-count" class="text-sm font-bold text-teal-600">-</span>
                        </div>
                        <h3 class="text-sm font-semibold text-gray-900 mb-1">Companies</h3>
                        <p class="text-xs text-gray-600 mb-2">Organizational structure</p>

                        <!-- Smart Preview -->
                        <div class="bg-teal-50 rounded p-2 mb-2">
                            <div class="flex justify-between text-xs text-teal-700">
                                <span>👔 <span id="hiring-authorities-count" class="font-medium">-</span></span>
                                <span>💼 <span id="positions-count" class="font-medium">-</span></span>
                            </div>
                        </div>

                        <!-- Universal Actions -->
                        <div class="flex space-x-1">
                            <button onclick="event.stopPropagation(); toggleEntityActions('companies')" class="flex-1 px-2 py-1 bg-teal-50 text-teal-800 rounded text-xs hover:bg-teal-100 transition-all">
                                Actions ▼
                            </button>
                            <button onclick="event.stopPropagation(); openVisualizationModal('companies', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200" title="2D visualization">📊</button>
                            <button onclick="event.stopPropagation(); openVisualizationModal('companies', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200" title="3D visualization">🌐</button>
                        </div>

                        <!-- Universal Actions Dropdown -->
                        <div id="companies-actions-menu" class="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-20 overflow-hidden" style="display: none;">
                            <div class="p-2 space-y-1">
                                <div class="text-xs font-medium text-gray-500 mb-1">Navigate Hierarchy</div>
                                <button onclick="event.stopPropagation(); loadEntityData('hiringAuthorities'); closeEntityActions('companies')" class="w-full px-2 py-1 text-left text-xs hover:bg-orange-50 rounded">👔 Hiring Authorities</button>
                                <button onclick="event.stopPropagation(); loadEntityData('positions'); closeEntityActions('companies')" class="w-full px-2 py-1 text-left text-xs hover:bg-violet-50 rounded">💼 Open Positions</button>
                                <hr class="my-2 border-gray-100">
                                <div class="text-xs font-medium text-gray-500 mb-1">Visualizations</div>
                                <button onclick="event.stopPropagation(); openVisualizationModal('companies', '2d'); closeEntityActions('companies')" class="w-full px-2 py-1 text-left text-xs hover:bg-blue-50 rounded">📊 2D Org Chart</button>
                                <button onclick="event.stopPropagation(); openVisualizationModal('companies', '3d'); closeEntityActions('companies')" class="w-full px-2 py-1 text-left text-xs hover:bg-purple-50 rounded">🌐 3D Org Chart</button>
                                <button onclick="event.stopPropagation(); openVisualizationModal('hiringAuthorities', '2d'); closeEntityActions('companies')" class="w-full px-2 py-1 text-left text-xs hover:bg-blue-50 rounded">📊 Authorities Network</button>
                                <button onclick="event.stopPropagation(); openVisualizationModal('positions', '3d'); closeEntityActions('companies')" class="w-full px-2 py-1 text-left text-xs hover:bg-purple-50 rounded">🌐 Positions Network</button>
                            </div>
                        </div>
                    </div>

                    <!-- 4. Skills Card -->
                    <div class="bento-card rounded-lg p-4 cursor-pointer relative" onclick="loadEntityData('skills')">
                        <div class="flex justify-between items-start mb-2">
                            <div class="text-2xl">⚡</div>
                            <span id="skills-count" class="text-sm font-bold text-amber-600">-</span>
                        </div>
                        <h3 class="text-sm font-semibold text-gray-900 mb-1">Skills</h3>
                        <p class="text-xs text-gray-600 mb-2">Demand & availability</p>

                        <!-- Smart Preview -->
                        <div class="bg-amber-50 rounded p-2 mb-2">
                            <div class="text-xs text-amber-700">
                                <span class="font-medium">🚨 <span id="skills-critical">-</span> Critical</span>
                            </div>
                        </div>

                        <!-- Universal Actions -->
                        <div class="flex space-x-1">
                            <button onclick="event.stopPropagation(); toggleEntityActions('skills')" class="flex-1 px-2 py-1 bg-amber-50 text-amber-800 rounded text-xs hover:bg-amber-100 transition-all">
                                Actions ▼
                            </button>
                            <button onclick="event.stopPropagation(); openVisualizationModal('skills', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200" title="2D visualization">📊</button>
                            <button onclick="event.stopPropagation(); openVisualizationModal('skills', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200" title="3D visualization">🌐</button>
                        </div>

                        <!-- Universal Actions Dropdown -->
                        <div id="skills-actions-menu" class="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-20 overflow-hidden" style="display: none;">
                            <div class="p-2 space-y-1">
                                <div class="text-xs font-medium text-gray-500 mb-1">Analysis</div>
                                <button onclick="event.stopPropagation(); loadEntityAnalysis('skills'); closeEntityActions('skills')" class="w-full px-2 py-1 text-left text-xs hover:bg-amber-50 rounded">📈 Demand Analysis</button>
                                <button onclick="event.stopPropagation(); loadEntityGaps('skills'); closeEntityActions('skills')" class="w-full px-2 py-1 text-left text-xs hover:bg-amber-50 rounded">🚨 Skills Gaps</button>
                                <hr class="my-2 border-gray-100">
                                <div class="text-xs font-medium text-gray-500 mb-1">Visualizations</div>
                                <button onclick="event.stopPropagation(); openVisualizationModal('skills', '2d'); closeEntityActions('skills')" class="w-full px-2 py-1 text-left text-xs hover:bg-blue-50 rounded">📊 2D Skills Map</button>
                                <button onclick="event.stopPropagation(); openVisualizationModal('skills', '3d'); closeEntityActions('skills')" class="w-full px-2 py-1 text-left text-xs hover:bg-purple-50 rounded">🌐 3D Skills Universe</button>
                            </div>
                        </div>
                    </div>

                    <!-- 5. Global Visualization Card -->
                    <div class="bento-card rounded-lg p-4 cursor-pointer relative" onclick="openVisualizationModal('overview', '2d')">
                        <div class="flex justify-between items-start mb-2">
                            <div class="text-2xl">🌐</div>
                            <div class="w-4 h-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full"></div>
                        </div>
                        <h3 class="text-sm font-semibold text-gray-900 mb-1">Global View</h3>
                        <p class="text-xs text-gray-600 mb-2">Complete network</p>

                        <!-- Smart Preview -->
                        <div class="bg-indigo-50 rounded p-2 mb-2">
                            <div class="text-xs text-indigo-700">
                                <span class="font-medium">🔗 <span id="connections-count">-</span> Links</span>
                            </div>
                        </div>

                        <!-- Universal Actions -->
                        <div class="flex space-x-1">
                            <button onclick="event.stopPropagation(); toggleEntityActions('global')" class="flex-1 px-2 py-1 bg-indigo-50 text-indigo-800 rounded text-xs hover:bg-indigo-100 transition-all">
                                Actions ▼
                            </button>
                            <button onclick="event.stopPropagation(); openVisualizationModal('overview', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200" title="2D visualization">📊</button>
                            <button onclick="event.stopPropagation(); openVisualizationModal('overview', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200" title="3D visualization">🌐</button>
                        </div>

                        <!-- Universal Actions Dropdown -->
                        <div id="global-actions-menu" class="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-20 overflow-hidden" style="display: none;">
                            <div class="p-2 space-y-1">
                                <div class="text-xs font-medium text-gray-500 mb-1">Global Views</div>
                                <button onclick="event.stopPropagation(); openVisualizationModal('overview', '2d'); closeEntityActions('global')" class="w-full px-2 py-1 text-left text-xs hover:bg-blue-50 rounded">📊 Complete 2D Network</button>
                                <button onclick="event.stopPropagation(); openVisualizationModal('overview', '3d'); closeEntityActions('global')" class="w-full px-2 py-1 text-left text-xs hover:bg-purple-50 rounded">🌐 Complete 3D Network</button>
                                <button onclick="event.stopPropagation(); loadEntityData('connections'); closeEntityActions('global')" class="w-full px-2 py-1 text-left text-xs hover:bg-indigo-50 rounded">🔗 Connections Analysis</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Content Area -->
                <div class="bg-white rounded-lg shadow" id="content-area">
                    <div class="p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Welcome to Candid Connections</h2>
                        <p class="text-gray-600 mb-4">
                            Unified intelligence platform with consistent visualization and interaction patterns across all data entities.
                        </p>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h3 class="font-semibold text-blue-900 mb-2">🚀 Unified Features:</h3>
                            <ul class="text-blue-800 space-y-1">
                                <li>• Consistent bento-style navigation across all entities</li>
                                <li>• Universal visualization engine with 2D/3D modal system</li>
                                <li>• Smart context-aware previews for all data types</li>
                                <li>• Standardized actions dropdown with entity-specific options</li>
                                <li>• Origin-based graph visualizations with hierarchical positioning</li>
                                <li>• Unified CRUD operations and data management</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Universal Visualization Modal -->
    <div id="visualization-modal" class="fixed inset-0 modal-overlay z-50" style="display: none;">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="viz-modal bg-white rounded-lg shadow-2xl overflow-hidden">
                <!-- Modal Header -->
                <div class="flex justify-between items-center p-4 border-b border-gray-200 bg-gray-50">
                    <div class="flex items-center space-x-3">
                        <h3 id="viz-modal-title" class="text-lg font-semibold text-gray-900">Graph Visualization</h3>
                        <div class="flex items-center space-x-2">
                            <span id="viz-mode-indicator" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium">2D</span>
                            <span id="viz-entity-indicator" class="px-2 py-1 bg-gray-100 text-gray-800 rounded text-xs">Overview</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <!-- Internal 2D/3D Toggle -->
                        <button onclick="toggleVisualizationMode()" id="viz-mode-toggle" class="px-3 py-1 bg-purple-100 text-purple-800 rounded text-sm hover:bg-purple-200 transition-all">
                            Switch to 3D
                        </button>
                        <button onclick="closeVisualizationModal()" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Visualization Container -->
                <div class="viz-container relative" style="height: 800px;">
                    <div id="modal-graph-container" style="width: 100%; height: 100%;"></div>

                    <!-- Universal HUD Controls -->
                    <div id="modal-graph-hud" class="absolute top-4 right-4 bg-black bg-opacity-10 backdrop-blur-sm rounded-lg transition-all duration-300">
                        <div class="p-3 space-y-2 text-xs">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-gray-700 font-medium">Controls</span>
                            </div>
                            <div class="flex space-x-1">
                                <button onclick="resetModalGraphView()" class="px-2 py-1 bg-white bg-opacity-60 text-gray-700 rounded text-xs hover:bg-opacity-80 transition-all" title="Reset view">🎯</button>
                                <button onclick="fitModalToView()" class="px-2 py-1 bg-white bg-opacity-60 text-gray-700 rounded text-xs hover:bg-opacity-80 transition-all" title="Fit to view">🔍</button>
                                <button onclick="toggleModalEditMode()" class="px-2 py-1 bg-white bg-opacity-60 text-gray-700 rounded text-xs hover:bg-opacity-80 transition-all" title="Edit mode">✏️</button>
                            </div>
                            <div class="text-gray-600 text-xs border-t border-gray-300 pt-2">
                                <div id="modal-hud-zoom-level">Zoom: 100%</div>
                                <div id="modal-hud-node-count">Nodes: 0</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="unified-ui-engine.js"></script>
    <script src="unified-view-renderers.js"></script>
    <script src="unified-graph-engine.js"></script>
    <script>
        // Initialize the unified system
        document.addEventListener('DOMContentLoaded', function() {
            initializeUnifiedSystem();
        });
    </script>
</body>
</html>
