// Unified Graph Rendering Engine for Candid Connections
// Handles both 2D and 3D visualizations with consistent patterns

// Visual hierarchy establishment
function establishVisualHierarchy(graphData, focusType) {
    const enhanced = JSON.parse(JSON.stringify(graphData)); // Deep clone

    // Define hierarchy levels based on focus
    const getHierarchyLevel = (node, focus) => {
        if (!focus || focus === 'overview') {
            const hierarchy = {
                'company': 1,
                'hiringAuthority': 2,
                'position': 3,
                'jobSeeker': 4,
                'skill': 5
            };
            return hierarchy[node.type] || 3;
        }

        // Enhanced focus-based hierarchy
        if (node.type === focus || (focus === 'matches' && node.type === 'jobSeeker')) {
            return 1; // Origin nodes - highest priority
        } else if (focus === 'companies') {
            if (node.type === 'hiringAuthority') return 2;
            if (node.type === 'position') return 3;
            if (node.type === 'jobSeeker') return 4;
        } else if (focus === 'hiringAuthorities') {
            if (node.type === 'company') return 2;
            if (node.type === 'position') return 2;
            if (node.type === 'jobSeeker') return 3;
        } else if (focus === 'positions') {
            if (node.type === 'hiringAuthority') return 2;
            if (node.type === 'company') return 3;
            if (node.type === 'skill') return 2;
            if (node.type === 'jobSeeker') return 3;
        } else if (focus === 'jobSeekers') {
            if (node.type === 'skill') return 2;
            if (node.type === 'position') return 3;
            if (node.type === 'company') return 4;
        } else if (focus === 'skills') {
            if (node.type === 'jobSeeker') return 2;
            if (node.type === 'position') return 3;
        }

        return 4; // Default secondary nodes
    };

    // Enhance nodes with hierarchy and visual properties
    enhanced.nodes = enhanced.nodes.map(node => ({
        ...node,
        hierarchyLevel: getHierarchyLevel(node, focusType),
        isOrigin: getHierarchyLevel(node, focusType) === 1,
        size: getNodeSize(node.type, getHierarchyLevel(node, focusType)),
        fontSize: getFontSize(node.type, 1, getHierarchyLevel(node, focusType)),
        fontWeight: getFontWeight(node.type, getHierarchyLevel(node, focusType))
    }));

    return enhanced;
}

// Enhanced node sizing based on hierarchy
function getNodeSize(nodeType, hierarchyLevel) {
    const baseSizes = {
        'company': 12,
        'hiringAuthority': 10,
        'position': 8,
        'jobSeeker': 8,
        'skill': 6
    };

    const baseSize = baseSizes[nodeType] || 8;
    const hierarchyMultiplier = hierarchyLevel === 1 ? 1.5 : hierarchyLevel === 2 ? 1.2 : 1;

    return baseSize * hierarchyMultiplier;
}

// Global font sizing system with hierarchy support
function getFontSize(nodeType, zoomLevel = 1, hierarchyLevel = 3) {
    const baseSize = getNodeImportance(nodeType);
    const hierarchyBoost = hierarchyLevel === 1 ? 1.4 : hierarchyLevel === 2 ? 1.2 : 1;
    const scaledSize = baseSize * zoomLevel * hierarchyBoost;
    return Math.max(10, Math.min(22, scaledSize));
}

function getFontWeight(nodeType, hierarchyLevel = 3) {
    const baseWeights = {
        'company': 900,
        'hiringAuthority': 700,
        'position': 600,
        'jobSeeker': 500,
        'skill': 400
    };

    const baseWeight = baseWeights[nodeType] || 500;

    // Origin nodes get extra weight boost
    if (hierarchyLevel === 1) {
        return Math.min(900, baseWeight + 200);
    }

    return baseWeight;
}

function getNodeImportance(nodeType) {
    const importance = {
        'company': 22,
        'hiringAuthority': 18,
        'position': 16,
        'jobSeeker': 14,
        'skill': 10
    };
    return importance[nodeType] || 14;
}

// Modal 2D Graph Renderer
function renderModal2DGraph(graphData, container, focusType) {
    const width = container.clientWidth;
    const height = container.clientHeight;

    console.log('Rendering 2D graph with data:', graphData);
    console.log('Container dimensions:', { width, height });

    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', width);
    svg.setAttribute('height', height);
    svg.style.background = 'transparent';
    svg.setAttribute('viewBox', `0 0 ${width} ${height}`);

    const mainGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    svg.appendChild(mainGroup);
    container.appendChild(svg);

    // Enhanced pan and zoom functionality
    let isPanning = false;
    let isDragging = false;
    let draggedNode = null;
    let panStart = { x: 0, y: 0 };
    let currentTransform = { x: 0, y: 0, scale: 1 };

    // Position nodes based on hierarchy and origin focus with better spacing
    const centerX = width / 2;
    const centerY = height / 2;
    const baseRadius = Math.min(width, height) / 6; // Increased base radius for better spacing

    if (graphData.nodes.length === 1) {
        graphData.nodes[0].x = centerX;
        graphData.nodes[0].y = centerY;
    } else {
        // Group nodes by hierarchy level
        const nodesByLevel = {};
        graphData.nodes.forEach(node => {
            const level = node.hierarchyLevel || 3;
            if (!nodesByLevel[level]) nodesByLevel[level] = [];
            nodesByLevel[level].push(node);
        });

        // Position origin nodes (level 1) at center with better spacing
        if (nodesByLevel[1]) {
            if (nodesByLevel[1].length === 1) {
                nodesByLevel[1][0].x = centerX;
                nodesByLevel[1][0].y = centerY;
            } else {
                nodesByLevel[1].forEach((node, i) => {
                    const angle = (i / nodesByLevel[1].length) * 2 * Math.PI;
                    node.x = centerX + Math.cos(angle) * (baseRadius * 0.5);
                    node.y = centerY + Math.sin(angle) * (baseRadius * 0.5);
                });
            }
        }

        // Position level 2 nodes around origin with increased spacing
        if (nodesByLevel[2]) {
            nodesByLevel[2].forEach((node, i) => {
                const angle = (i / nodesByLevel[2].length) * 2 * Math.PI;
                node.x = centerX + Math.cos(angle) * (baseRadius * 1.5);
                node.y = centerY + Math.sin(angle) * (baseRadius * 1.5);
            });
        }

        // Position level 3+ nodes in outer rings with better spacing
        [3, 4, 5].forEach(level => {
            if (nodesByLevel[level]) {
                nodesByLevel[level].forEach((node, i) => {
                    const angle = (i / nodesByLevel[level].length) * 2 * Math.PI;
                    const levelRadius = baseRadius * (1.5 + (level - 2) * 0.8); // Better spacing multiplier
                    node.x = centerX + Math.cos(angle) * levelRadius;
                    node.y = centerY + Math.sin(angle) * levelRadius;
                });
            }
        });
    }

    // Create links group for proper layering
    const linksGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    linksGroup.setAttribute('class', 'links-layer');
    mainGroup.appendChild(linksGroup);

    // Create nodes group for proper layering
    const nodesGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    nodesGroup.setAttribute('class', 'nodes-layer');
    mainGroup.appendChild(nodesGroup);

    // Draw links first (so they appear behind nodes)
    console.log('Drawing links:', graphData.links.length);
    graphData.links.forEach((link, index) => {
        // Handle both string IDs and object references
        const sourceId = typeof link.source === 'object' ? link.source.id : link.source;
        const targetId = typeof link.target === 'object' ? link.target.id : link.target;

        const sourceNode = graphData.nodes.find(n => n.id === sourceId);
        const targetNode = graphData.nodes.find(n => n.id === targetId);

        console.log(`Link ${index}:`, { sourceId, targetId, sourceNode: !!sourceNode, targetNode: !!targetNode });

        if (sourceNode && targetNode) {
            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
            line.setAttribute('x1', sourceNode.x);
            line.setAttribute('y1', sourceNode.y);
            line.setAttribute('x2', targetNode.x);
            line.setAttribute('y2', targetNode.y);
            line.setAttribute('stroke', link.color || '#94A3B8');
            line.setAttribute('stroke-width', '2');
            line.setAttribute('opacity', '0.6');
            line.setAttribute('stroke-linecap', 'round');
            linksGroup.appendChild(line);
            console.log(`Link ${index} drawn from (${sourceNode.x}, ${sourceNode.y}) to (${targetNode.x}, ${targetNode.y})`);
        } else {
            console.warn(`Link ${index} missing nodes:`, { sourceId, targetId, sourceFound: !!sourceNode, targetFound: !!targetNode });
        }
    });

    // Draw nodes
    graphData.nodes.forEach(node => {
        const nodeGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        nodeGroup.setAttribute('transform', `translate(${node.x}, ${node.y})`);
        nodeGroup.setAttribute('data-node', node.id);
        nodeGroup.style.cursor = 'pointer';

        // Node circle
        const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        circle.setAttribute('r', node.size || 8);
        circle.setAttribute('fill', node.color || '#3B82F6');
        circle.setAttribute('stroke', node.isOrigin ? '#1F2937' : '#FFFFFF');
        circle.setAttribute('stroke-width', node.isOrigin ? '3' : '2');
        nodeGroup.appendChild(circle);

        // Node label
        const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        text.setAttribute('text-anchor', 'middle');
        text.setAttribute('dy', '0.35em');
        text.setAttribute('font-size', node.fontSize || '12px');
        text.setAttribute('font-weight', node.fontWeight || '500');
        text.setAttribute('fill', '#1F2937');
        text.textContent = node.name;
        nodeGroup.appendChild(text);

        // Node click handler
        nodeGroup.addEventListener('click', (e) => {
            e.stopPropagation();
            showNodeDetails(node);
        });

        nodesGroup.appendChild(nodeGroup);
    });

    // Enhanced mouse wheel zoom with governors
    svg.addEventListener('wheel', (e) => {
        e.preventDefault();

        const rect = svg.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;

        const zoomFactor = e.deltaY > 0 ? 0.92 : 1.08;
        const minScale = 0.1;
        const maxScale = 20;
        const newScale = Math.max(minScale, Math.min(maxScale, currentTransform.scale * zoomFactor));

        if (newScale === currentTransform.scale) return;

        const dx = mouseX - currentTransform.x;
        const dy = mouseY - currentTransform.y;

        currentTransform.x = mouseX - dx * (newScale / currentTransform.scale);
        currentTransform.y = mouseY - dy * (newScale / currentTransform.scale);
        currentTransform.scale = newScale;

        updateTransform();
        updateModalHUDZoom();
    });

    // Pan and drag functionality
    svg.addEventListener('mousedown', (e) => {
        const nodeElement = e.target.closest('g[data-node]');

        if (e.button === 0) {
            if (nodeElement) {
                isDragging = true;
                draggedNode = nodeElement;
                svg.style.cursor = 'grabbing';
                e.preventDefault();
            } else {
                isPanning = true;
                panStart.x = e.clientX - currentTransform.x;
                panStart.y = e.clientY - currentTransform.y;
                svg.style.cursor = 'grabbing';
            }
        }
    });

    svg.addEventListener('mousemove', (e) => {
        if (isDragging && draggedNode) {
            const rect = svg.getBoundingClientRect();
            const x = (e.clientX - rect.left - currentTransform.x) / currentTransform.scale;
            const y = (e.clientY - rect.top - currentTransform.y) / currentTransform.scale;
            draggedNode.setAttribute('transform', `translate(${x}, ${y})`);
        } else if (isPanning) {
            currentTransform.x = e.clientX - panStart.x;
            currentTransform.y = e.clientY - panStart.y;
            updateTransform();
        }
    });

    svg.addEventListener('mouseup', () => {
        isPanning = false;
        isDragging = false;
        draggedNode = null;
        svg.style.cursor = 'default';
    });

    function updateTransform() {
        mainGroup.setAttribute('transform',
            `translate(${currentTransform.x}, ${currentTransform.y}) scale(${currentTransform.scale})`
        );
    }

    function updateModalHUDZoom() {
        updateElementText('modal-hud-zoom-level', `Zoom: ${Math.round(currentTransform.scale * 100)}%`);
    }

    // Store current graph for controls
    UnifiedState.currentGraph = {
        svg,
        mainGroup,
        currentTransform,
        updateTransform,
        updateModalHUDZoom
    };
}

// Modal 3D Graph Renderer
function renderModal3DGraph(graphData, container, focusType) {
    // Clear container
    container.innerHTML = '';

    console.log('Rendering 3D graph with data:', graphData);

    // Prepare graph data with proper link format
    const processedGraphData = {
        nodes: graphData.nodes.map(node => ({
            ...node,
            // Ensure proper node sizing for 3D
            val: (node.size || 8) * 2, // Scale up for 3D visibility
        })),
        links: graphData.links.map(link => ({
            ...link,
            // Ensure links reference node IDs correctly
            source: typeof link.source === 'object' ? link.source.id : link.source,
            target: typeof link.target === 'object' ? link.target.id : link.target,
        }))
    };

    console.log('Processed 3D graph data:', processedGraphData);

    // Create 3D graph using 3d-force-graph
    const Graph = ForceGraph3D()(container)
        .graphData(processedGraphData)
        .nodeLabel(node => `${node.name} (${node.type})`)
        .nodeColor(node => node.color || '#3B82F6')
        .nodeVal(node => node.val || 16)
        .nodeOpacity(0.9)
        .linkColor(link => link.color || '#94A3B8')
        .linkOpacity(0.6)
        .linkWidth(2)
        .backgroundColor('rgba(248, 250, 252, 0.1)') // Very light background
        .showNavInfo(false)
        .enableNodeDrag(true)
        .enableNavigationControls(true)
        .controlType('orbit')
        .onNodeClick(node => {
            console.log('3D Node clicked:', node);
            showNodeDetails(node);
        })
        .onNodeHover(node => {
            container.style.cursor = node ? 'pointer' : 'default';
        });

    // Enhanced 3D positioning based on hierarchy with proper scaling
    const centerNodes = processedGraphData.nodes.filter(n => n.hierarchyLevel === 1);
    const secondaryNodes = processedGraphData.nodes.filter(n => n.hierarchyLevel === 2);
    const tertiaryNodes = processedGraphData.nodes.filter(n => n.hierarchyLevel >= 3);

    console.log('3D Node distribution:', {
        center: centerNodes.length,
        secondary: secondaryNodes.length,
        tertiary: tertiaryNodes.length
    });

    // Position origin nodes at center with proper scale
    centerNodes.forEach((node, i) => {
        if (centerNodes.length === 1) {
            node.fx = 0;
            node.fy = 0;
            node.fz = 0;
        } else {
            const angle = (i / centerNodes.length) * 2 * Math.PI;
            node.fx = Math.cos(angle) * 100;
            node.fy = Math.sin(angle) * 100;
            node.fz = 0;
        }
    });

    // Position secondary nodes in inner sphere
    secondaryNodes.forEach((node, i) => {
        const angle = (i / secondaryNodes.length) * 2 * Math.PI;
        const elevation = (i / secondaryNodes.length - 0.5) * Math.PI * 0.5; // More controlled elevation
        const radius = 300;
        node.fx = Math.cos(angle) * Math.cos(elevation) * radius;
        node.fy = Math.sin(angle) * Math.cos(elevation) * radius;
        node.fz = Math.sin(elevation) * radius;
    });

    // Position tertiary nodes in outer sphere
    tertiaryNodes.forEach((node, i) => {
        const angle = (i / tertiaryNodes.length) * 2 * Math.PI;
        const elevation = (i / tertiaryNodes.length - 0.5) * Math.PI * 0.7; // More controlled elevation
        const radius = 500;
        node.fx = Math.cos(angle) * Math.cos(elevation) * radius;
        node.fy = Math.sin(angle) * Math.cos(elevation) * radius;
        node.fz = Math.sin(elevation) * radius;
    });

    // Store current graph for controls
    UnifiedState.currentGraph = Graph;

    // Set proper initial camera position
    setTimeout(() => {
        const nodeCount = processedGraphData.nodes.length;
        const distance = Math.max(800, nodeCount * 50); // Scale distance based on node count

        Graph.cameraPosition(
            { x: distance * 0.7, y: distance * 0.5, z: distance * 0.7 }, // Camera position
            { x: 0, y: 0, z: 0 }, // Look at center
            2000 // Animation duration
        );

        console.log('3D Camera positioned at distance:', distance);
    }, 500); // Increased delay to ensure graph is ready

    // Add some debugging
    Graph.onEngineStop(() => {
        console.log('3D Force simulation stopped');
    });
}

// Show node details
function showNodeDetails(node) {
    console.log('Node clicked:', node);
    // Could implement a details panel or modal here
}

// Modal graph control functions
function resetModalGraphView() {
    if (UnifiedState.currentGraph) {
        if (UnifiedState.currentMode === '3d' && UnifiedState.currentGraph.cameraPosition) {
            // Reset 3D camera
            UnifiedState.currentGraph.cameraPosition(
                { x: 200, y: 200, z: 200 },
                { x: 0, y: 0, z: 0 },
                1000
            );
        } else if (UnifiedState.currentGraph.currentTransform) {
            // Reset 2D transform
            UnifiedState.currentGraph.currentTransform.x = 0;
            UnifiedState.currentGraph.currentTransform.y = 0;
            UnifiedState.currentGraph.currentTransform.scale = 1;
            UnifiedState.currentGraph.updateTransform();
            UnifiedState.currentGraph.updateModalHUDZoom();
        }
    }
}

function fitModalToView() {
    if (UnifiedState.currentGraph) {
        if (UnifiedState.currentMode === '3d' && UnifiedState.currentGraph.zoomToFit) {
            UnifiedState.currentGraph.zoomToFit(1000);
        } else {
            // Implement 2D fit to view
            resetModalGraphView();
        }
    }
}
