// Unified UI Engine for Candid Connections
// Provides consistent interaction patterns across all entities

// Global state management
const UnifiedState = {
    currentEntity: null,
    currentMode: '2d', // '2d' or '3d'
    currentGraph: null,
    currentGraphData: null,
    isEditMode: false,
    openDropdowns: new Set(),

    // Entity configurations
    entities: {
        matches: {
            icon: '🎯',
            color: 'emerald',
            name: 'Job Matches',
            description: 'Intelligent matching results'
        },
        jobSeekers: {
            icon: '👤',
            color: 'blue',
            name: 'Job Seekers',
            description: 'Candidate profiles & skills'
        },
        companies: {
            icon: '🏢',
            color: 'teal',
            name: 'Companies',
            description: 'Organizational structure'
        },
        skills: {
            icon: '⚡',
            color: 'amber',
            name: 'Skills',
            description: 'Demand & availability'
        },
        hiringAuthorities: {
            icon: '👔',
            color: 'orange',
            name: 'Hiring Authorities',
            description: 'Decision makers'
        },
        positions: {
            icon: '💼',
            color: 'violet',
            name: 'Positions',
            description: 'Open opportunities'
        },
        overview: {
            icon: '🌐',
            color: 'indigo',
            name: 'Global View',
            description: 'Complete network'
        }
    }
};

// Initialize the unified system
async function initializeUnifiedSystem() {
    console.log('🚀 Initializing Unified UI System...');

    // Load initial stats for all cards
    await loadUnifiedStats();

    // Set up global event listeners
    setupGlobalEventListeners();

    // Initialize visualization engine
    initializeVisualizationEngine();

    console.log('✅ Unified UI System Ready');
}

// Load stats for all entity cards
async function loadUnifiedStats() {
    try {
        const response = await fetch('/api/stats');
        const data = await response.json();

        if (data.success) {
            const stats = data.data;

            // Update all card counts
            updateElementText('job-seekers-count', stats.jobSeekers);
            updateElementText('companies-count', stats.companies);
            updateElementText('hiring-authorities-count', stats.hiringAuthorities);
            updateElementText('positions-count', stats.positions);
            updateElementText('skills-count', stats.skills);
            updateElementText('connections-count', stats.connections);

            // Load matches count separately
            try {
                const matchesResponse = await fetch('/api/matches');
                const matchesData = await matchesResponse.json();
                if (matchesData.success) {
                    updateElementText('matches-count', matchesData.data.length);
                    // Update top score preview
                    const topScore = Math.max(...matchesData.data.map(m => m.score || 0));
                    updateElementText('matches-top-score', topScore);
                }
            } catch (error) {
                console.error('Error loading matches:', error);
                updateElementText('matches-count', '0');
                updateElementText('matches-top-score', '0');
            }

            // Update smart previews
            updateElementText('seekers-active', Math.floor(stats.jobSeekers * 0.8)); // 80% active
            updateElementText('skills-critical', Math.floor(stats.skills * 0.15)); // 15% critical
        }
    } catch (error) {
        console.error('Error loading unified stats:', error);
    }
}

// Utility function to safely update element text
function updateElementText(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = value;
    }
}

// Setup global event listeners
function setupGlobalEventListeners() {
    // Close dropdowns when clicking outside
    document.addEventListener('click', (e) => {
        if (!e.target.closest('[id$="-actions-menu"]') && !e.target.closest('[onclick*="toggleEntityActions"]')) {
            closeAllEntityActions();
        }
    });

    // Escape key handlers
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            closeAllEntityActions();
            closeVisualizationModal();
        }
    });
}

// Universal entity actions dropdown system
function toggleEntityActions(entityType) {
    const menuId = `${entityType}-actions-menu`;
    const menu = document.getElementById(menuId);
    const btn = document.querySelector(`[onclick*="toggleEntityActions('${entityType}')"]`);

    if (!menu || !btn) return;

    // Close other dropdowns first
    closeAllEntityActions();

    if (menu.style.display === 'none' || menu.style.display === '') {
        // Show menu
        menu.style.display = 'block';
        btn.textContent = 'Actions ▲';
        btn.classList.add(`bg-${UnifiedState.entities[entityType].color}-100`);
        UnifiedState.openDropdowns.add(entityType);

        // Position menu properly
        positionDropdownMenu(menu, btn);
    } else {
        closeEntityActions(entityType);
    }
}

function closeEntityActions(entityType) {
    const menuId = `${entityType}-actions-menu`;
    const menu = document.getElementById(menuId);
    const btn = document.querySelector(`[onclick*="toggleEntityActions('${entityType}')"]`);

    if (menu && btn) {
        menu.style.display = 'none';
        btn.textContent = 'Actions ▼';
        btn.classList.remove(`bg-${UnifiedState.entities[entityType].color}-100`);
        UnifiedState.openDropdowns.delete(entityType);
    }
}

function closeAllEntityActions() {
    UnifiedState.openDropdowns.forEach(entityType => {
        closeEntityActions(entityType);
    });
    UnifiedState.openDropdowns.clear();
}

// Position dropdown menus properly
function positionDropdownMenu(menu, button) {
    const buttonRect = button.getBoundingClientRect();
    const menuRect = menu.getBoundingClientRect();
    const viewportHeight = window.innerHeight;

    // Check if menu would go below viewport
    if (buttonRect.bottom + menuRect.height > viewportHeight) {
        // Position above button
        menu.style.top = 'auto';
        menu.style.bottom = '100%';
        menu.style.marginBottom = '4px';
        menu.style.marginTop = '0';
    } else {
        // Position below button (default)
        menu.style.top = '100%';
        menu.style.bottom = 'auto';
        menu.style.marginTop = '4px';
        menu.style.marginBottom = '0';
    }
}

// Universal entity data loading
async function loadEntityData(entityType) {
    console.log(`Loading data for: ${entityType}`);
    UnifiedState.currentEntity = entityType;

    const entity = UnifiedState.entities[entityType];
    if (!entity) {
        console.error(`Unknown entity type: ${entityType}`);
        return;
    }

    // Show loading state
    document.getElementById('content-area').innerHTML = `
        <div class="p-6">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">${entity.icon} Loading ${entity.name}...</h2>
            <div class="animate-pulse">
                <div class="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                <div class="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
        </div>
    `;

    try {
        // Route to appropriate data loader
        switch (entityType) {
            case 'matches':
                await loadMatchesData();
                break;
            case 'jobSeekers':
                await loadJobSeekersData();
                break;
            case 'companies':
                await loadCompaniesData();
                break;
            case 'skills':
                await loadSkillsData();
                break;
            case 'hiringAuthorities':
                await loadHiringAuthoritiesData();
                break;
            case 'positions':
                await loadPositionsData();
                break;
            case 'connections':
                await loadConnectionsData();
                break;
            default:
                await loadOverviewData();
        }
    } catch (error) {
        console.error(`Error loading ${entityType}:`, error);
        showErrorState(entityType, error);
    }
}

// Universal error state display
function showErrorState(entityType, error) {
    const entity = UnifiedState.entities[entityType] || { icon: '❌', name: 'Unknown' };
    document.getElementById('content-area').innerHTML = `
        <div class="p-6">
            <h2 class="text-2xl font-bold text-red-600 mb-4">${entity.icon} Error Loading ${entity.name}</h2>
            <p class="text-gray-600 mb-4">There was an error loading the data. Please try again.</p>
            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                <p class="text-red-800 text-sm">${error.message}</p>
            </div>
            <button onclick="loadEntityData('${entityType}')" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                Try Again
            </button>
        </div>
    `;
}

// Universal visualization modal system
function openVisualizationModal(entityType, mode = '2d') {
    console.log(`Opening visualization modal: ${entityType} in ${mode} mode`);

    UnifiedState.currentEntity = entityType;
    UnifiedState.currentMode = mode;

    const entity = UnifiedState.entities[entityType] || UnifiedState.entities.overview;

    // Update modal title and indicators
    document.getElementById('viz-modal-title').textContent = `${entity.icon} ${entity.name} - Graph Visualization`;
    document.getElementById('viz-mode-indicator').textContent = mode.toUpperCase();
    document.getElementById('viz-entity-indicator').textContent = entity.name;

    // Update mode toggle button
    const modeToggle = document.getElementById('viz-mode-toggle');
    modeToggle.textContent = mode === '2d' ? 'Switch to 3D' : 'Switch to 2D';

    // Show modal
    document.getElementById('visualization-modal').style.display = 'block';
    document.body.style.overflow = 'hidden'; // Prevent background scrolling

    // Load and render graph
    loadModalVisualization(entityType, mode);
}

function closeVisualizationModal() {
    document.getElementById('visualization-modal').style.display = 'none';
    document.body.style.overflow = 'auto'; // Restore scrolling

    // Clean up graph
    const container = document.getElementById('modal-graph-container');
    if (container) {
        container.innerHTML = '';
    }

    UnifiedState.currentGraph = null;
    UnifiedState.currentGraphData = null;
}

function toggleVisualizationMode() {
    const newMode = UnifiedState.currentMode === '2d' ? '3d' : '2d';
    UnifiedState.currentMode = newMode;

    // Update indicators
    document.getElementById('viz-mode-indicator').textContent = newMode.toUpperCase();
    const modeToggle = document.getElementById('viz-mode-toggle');
    modeToggle.textContent = newMode === '2d' ? 'Switch to 3D' : 'Switch to 2D';

    // Re-render with new mode
    if (UnifiedState.currentEntity) {
        loadModalVisualization(UnifiedState.currentEntity, newMode);
    }
}

// Initialize visualization engine
function initializeVisualizationEngine() {
    console.log('🎨 Initializing Visualization Engine...');
    // Visualization engine initialization will be expanded
}

// Entity-specific data loading implementations
async function loadMatchesData() {
    try {
        const response = await fetch('/api/matches');
        const data = await response.json();

        if (data.success) {
            const matches = data.data;
            renderMatchesView(matches);
        } else {
            throw new Error(data.error || 'Failed to load matches');
        }
    } catch (error) {
        throw error;
    }
}

async function loadJobSeekersData() {
    try {
        const response = await fetch('/api/job-seekers');
        const data = await response.json();

        if (data.success) {
            const jobSeekers = data.data;
            renderJobSeekersView(jobSeekers);
        } else {
            throw new Error(data.error || 'Failed to load job seekers');
        }
    } catch (error) {
        throw error;
    }
}

async function loadCompaniesData() {
    try {
        const [companiesResponse, hiringAuthoritiesResponse, positionsResponse] = await Promise.all([
            fetch('/api/companies'),
            fetch('/api/hiring-authorities'),
            fetch('/api/positions')
        ]);

        const [companiesData, hiringAuthoritiesData, positionsData] = await Promise.all([
            companiesResponse.json(),
            hiringAuthoritiesResponse.json(),
            positionsResponse.json()
        ]);

        if (companiesData.success && hiringAuthoritiesData.success && positionsData.success) {
            renderCompaniesView(companiesData.data, hiringAuthoritiesData.data, positionsData.data);
        } else {
            throw new Error('Failed to load companies data');
        }
    } catch (error) {
        throw error;
    }
}

async function loadSkillsData() {
    try {
        const [skillsResponse, jobSeekersResponse, positionsResponse] = await Promise.all([
            fetch('/api/skills'),
            fetch('/api/job-seekers'),
            fetch('/api/positions')
        ]);

        const [skillsData, jobSeekersData, positionsData] = await Promise.all([
            skillsResponse.json(),
            jobSeekersResponse.json(),
            positionsResponse.json()
        ]);

        if (skillsData.success && jobSeekersData.success && positionsData.success) {
            renderSkillsView(skillsData.data, jobSeekersData.data, positionsData.data);
        } else {
            throw new Error('Failed to load skills data');
        }
    } catch (error) {
        throw error;
    }
}

async function loadHiringAuthoritiesData() {
    try {
        const response = await fetch('/api/hiring-authorities');
        const data = await response.json();

        if (data.success) {
            const hiringAuthorities = data.data;
            renderHiringAuthoritiesView(hiringAuthorities);
        } else {
            throw new Error(data.error || 'Failed to load hiring authorities');
        }
    } catch (error) {
        throw error;
    }
}

async function loadPositionsData() {
    try {
        const response = await fetch('/api/positions');
        const data = await response.json();

        if (data.success) {
            const positions = data.data;
            renderPositionsView(positions);
        } else {
            throw new Error(data.error || 'Failed to load positions');
        }
    } catch (error) {
        throw error;
    }
}

async function loadConnectionsData() {
    try {
        // Load connections analysis
        const response = await fetch('/api/stats');
        const data = await response.json();

        if (data.success) {
            renderConnectionsView(data.data);
        } else {
            throw new Error(data.error || 'Failed to load connections');
        }
    } catch (error) {
        throw error;
    }
}

async function loadOverviewData() {
    try {
        const response = await fetch('/api/stats');
        const data = await response.json();

        if (data.success) {
            renderOverviewView(data.data);
        } else {
            throw new Error(data.error || 'Failed to load overview');
        }
    } catch (error) {
        throw error;
    }
}

// Modal visualization loading with unified engine
async function loadModalVisualization(entityType, mode) {
    console.log(`Loading modal visualization: ${entityType} in ${mode} mode`);

    const container = document.getElementById('modal-graph-container');
    container.innerHTML = `
        <div class="flex items-center justify-center h-full">
            <div class="text-center">
                <div class="text-4xl mb-4">${UnifiedState.entities[entityType]?.icon || '🌐'}</div>
                <div class="text-lg font-semibold text-gray-700">Loading ${mode.toUpperCase()} Visualization...</div>
                <div class="text-sm text-gray-500 mt-2">Preparing ${entityType} network graph</div>
            </div>
        </div>
    `;

    try {
        // Load graph data with origin context
        const url = entityType === 'overview' ? '/api/graph?type=overview' : `/api/graph?focus=${entityType}&type=${entityType}`;
        const response = await fetch(url);
        const data = await response.json();

        if (data.success) {
            UnifiedState.currentGraphData = data.data;
            renderModalGraph(data.data, entityType, mode);
        } else {
            throw new Error(data.error || 'Failed to load graph data');
        }
    } catch (error) {
        console.error('Error loading modal visualization:', error);
        container.innerHTML = `
            <div class="flex items-center justify-center h-full">
                <div class="text-center">
                    <div class="text-4xl mb-4 text-red-500">❌</div>
                    <div class="text-lg font-semibold text-red-700">Visualization Error</div>
                    <div class="text-sm text-red-500 mt-2">${error.message}</div>
                    <button onclick="loadModalVisualization('${entityType}', '${mode}')" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                        Try Again
                    </button>
                </div>
            </div>
        `;
    }
}

// Unified modal graph rendering
function renderModalGraph(graphData, entityType, mode) {
    const container = document.getElementById('modal-graph-container');
    container.innerHTML = ''; // Clear loading state

    // Update HUD info
    updateModalHUDInfo(graphData);

    // Establish visual hierarchy based on entity focus
    const enhancedGraphData = establishVisualHierarchy(graphData, entityType);

    if (mode === '3d') {
        renderModal3DGraph(enhancedGraphData, container, entityType);
    } else {
        renderModal2DGraph(enhancedGraphData, container, entityType);
    }
}

// Update modal HUD information
function updateModalHUDInfo(graphData) {
    const nodeCount = graphData.nodes ? graphData.nodes.length : 0;
    const edgeCount = graphData.links ? graphData.links.length : 0;

    updateElementText('modal-hud-node-count', `Nodes: ${nodeCount}`);
    updateElementText('modal-hud-zoom-level', 'Zoom: 100%');
}

// Modal graph control functions
function resetModalGraphView() {
    console.log('Resetting modal graph view...');
}

function fitModalToView() {
    console.log('Fitting modal graph to view...');
}

function toggleModalEditMode() {
    UnifiedState.isEditMode = !UnifiedState.isEditMode;
    console.log(`Modal edit mode: ${UnifiedState.isEditMode ? 'ON' : 'OFF'}`);
}

// Entity-specific action functions (placeholders)
function loadEntityAnalysis(entityType) {
    console.log(`Loading analysis for: ${entityType}`);
}

function loadEntityFilters(entityType) {
    console.log(`Loading filters for: ${entityType}`);
}

function loadEntityCRUD(entityType) {
    console.log(`Loading CRUD interface for: ${entityType}`);
}

function loadEntitySkills(entityType) {
    console.log(`Loading skills analysis for: ${entityType}`);
}

function loadEntityGaps(entityType) {
    console.log(`Loading gaps analysis for: ${entityType}`);
}

// Export for global access
window.UnifiedState = UnifiedState;
