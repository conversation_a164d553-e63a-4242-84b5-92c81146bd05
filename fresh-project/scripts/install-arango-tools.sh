#!/bin/bash

# Colors for output
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
NC="\033[0m" # No Color

echo -e "${YELLOW}Installing ArangoDB CLI tools...${NC}"

# Check if we can use package managers first
if command -v brew &> /dev/null && [[ "$OSTYPE" == "darwin"* ]]; then
  echo -e "${YELLOW}Installing ArangoDB CLI tools via Homebrew...${NC}"
  brew install arangodb
  echo -e "${GREEN}ArangoDB CLI tools installed via Homebrew!${NC}"
  exit 0
fi

# For other systems, use npm package
echo -e "${YELLOW}Installing ArangoDB CLI tools via npm...${NC}"
npm install -g arangojs-cli

if [ $? -eq 0 ]; then
  echo -e "${GREEN}ArangoDB CLI tools installed via npm!${NC}"
else
  echo -e "${YELLOW}npm installation failed, trying alternative approach...${NC}"

  # Create a simple wrapper script that uses the arangojs library
  mkdir -p tools/arangodb/bin

  cat > tools/arangodb/bin/arangosh << 'EOF'
#!/usr/bin/env node

const { Database } = require('arangojs');
const readline = require('readline');

// Parse command line arguments
const args = process.argv.slice(2);
let endpoint = 'http://localhost:8529';
let username = 'root';
let password = '';
let database = '_system';
let executeString = '';

for (let i = 0; i < args.length; i++) {
  if (args[i] === '--server.endpoint') {
    endpoint = args[i + 1];
    i++;
  } else if (args[i] === '--server.username') {
    username = args[i + 1];
    i++;
  } else if (args[i] === '--server.password') {
    password = args[i + 1];
    i++;
  } else if (args[i] === '--server.database') {
    database = args[i + 1];
    i++;
  } else if (args[i] === '--javascript.execute-string') {
    executeString = args[i + 1];
    i++;
  }
}

async function main() {
  try {
    const db = new Database({
      url: endpoint,
      auth: { username, password },
      databaseName: database
    });

    if (executeString) {
      // Execute the JavaScript string
      const result = eval(`(async function() { ${executeString} })()`);
      if (result instanceof Promise) {
        await result;
      }
    } else {
      console.log('Interactive mode not supported in this wrapper.');
      console.log('Use --javascript.execute-string to execute commands.');
    }
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

main();
EOF

  chmod +x tools/arangodb/bin/arangosh

  # Create symlink in node_modules/.bin
  mkdir -p node_modules/.bin
  ln -sf "$(pwd)/tools/arangodb/bin/arangosh" "node_modules/.bin/arangosh"

  echo -e "${GREEN}Custom ArangoDB wrapper created!${NC}"
fi

echo -e "${YELLOW}ArangoDB CLI tools are now available.${NC}"
