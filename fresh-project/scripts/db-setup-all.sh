#!/bin/bash

# Colors for output
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
NC="\033[0m" # No Color

echo -e "${YELLOW}Starting comprehensive ArangoDB setup...${NC}"

# Check if environment variables are set
if [ -z "$ARANGO_URL" ]; then
  ARANGO_URL="http://localhost:8529"
  echo -e "${YELLOW}ARANGO_URL not set, using default: $ARANGO_URL${NC}"
fi

if [ -z "$ARANGO_USERNAME" ]; then
  ARANGO_USERNAME="root"
  echo -e "${YELLOW}ARANGO_USERNAME not set, using default: $ARANGO_USERNAME${NC}"
fi

if [ -z "$ARANGO_PASSWORD" ]; then
  ARANGO_PASSWORD="rootpassword"
  echo -e "${YELLOW}ARANGO_PASSWORD not set, using default: $ARANGO_PASSWORD${NC}"
fi

if [ -z "$ARANGO_DB_NAME" ]; then
  ARANGO_DB_NAME="candid_connections"
  echo -e "${YELLOW}ARANGO_DB_NAME not set, using default: $ARANGO_DB_NAME${NC}"
fi

# Check if we're running locally
IS_LOCAL=false
if [[ "$ARANGO_URL" == *"localhost"* ]]; then
  IS_LOCAL=true
fi

# If local, check if Docker is running
if [ "$IS_LOCAL" = true ]; then
  if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}Docker is not running. Please start Docker and try again.${NC}"
    exit 1
  fi
  
  # Start ArangoDB using Docker
  echo -e "${YELLOW}Starting ArangoDB using Docker...${NC}"
  cd docker && docker-compose up -d && cd ..
  
  # Wait for ArangoDB to be ready
  echo -e "${YELLOW}Waiting for ArangoDB to be ready...${NC}"
  until $(curl --output /dev/null --silent --head --fail $ARANGO_URL); do
    printf '.'
    sleep 2
  done
  echo -e "\n${GREEN}ArangoDB is ready!${NC}"
else
  # Check if ArangoDB is accessible
  echo -e "${YELLOW}Checking if ArangoDB is accessible at $ARANGO_URL...${NC}"
  if ! curl --output /dev/null --silent --head --fail $ARANGO_URL; then
    echo -e "${RED}Cannot connect to ArangoDB at $ARANGO_URL${NC}"
    echo -e "${YELLOW}Please check your ArangoDB credentials and make sure the server is running.${NC}"
    exit 1
  fi
  echo -e "${GREEN}ArangoDB is accessible!${NC}"
fi

# Install ArangoDB CLI tools if not already installed
if [ ! -d "tools/arangodb/bin" ]; then
  echo -e "${YELLOW}Installing ArangoDB CLI tools...${NC}"
  ./scripts/install-arango-tools.sh
fi

# Create database
echo -e "${YELLOW}Creating database $ARANGO_DB_NAME...${NC}"
TOOLS_DIR="./tools/arangodb/bin"
$TOOLS_DIR/arangosh \
  --server.endpoint $ARANGO_URL \
  --server.username $ARANGO_USERNAME \
  --server.password $ARANGO_PASSWORD \
  --javascript.execute-string "if (!db._databases().includes('$ARANGO_DB_NAME')) { db._createDatabase('$ARANGO_DB_NAME'); console.log('Database created successfully.'); } else { console.log('Database already exists.'); }"

# Create collections
echo -e "${YELLOW}Creating collections...${NC}"
$TOOLS_DIR/arangosh \
  --server.endpoint $ARANGO_URL \
  --server.username $ARANGO_USERNAME \
  --server.password $ARANGO_PASSWORD \
  --server.database $ARANGO_DB_NAME \
  --javascript.execute-string "
    const collections = [
      { name: 'jobSeekers', type: 'document' },
      { name: 'companies', type: 'document' },
      { name: 'hiringAuthorities', type: 'document' },
      { name: 'positions', type: 'document' },
      { name: 'skills', type: 'document' },
      { name: 'seekerSkills', type: 'edge' },
      { name: 'positionSkills', type: 'edge' },
      { name: 'companyHiringAuthorities', type: 'edge' },
      { name: 'hiringAuthorityPositions', type: 'edge' }
    ];
    
    for (const collection of collections) {
      if (!db._collection(collection.name)) {
        if (collection.type === 'edge') {
          db._createEdgeCollection(collection.name);
          console.log('Edge collection ' + collection.name + ' created.');
        } else {
          db._createDocumentCollection(collection.name);
          console.log('Document collection ' + collection.name + ' created.');
        }
      } else {
        console.log('Collection ' + collection.name + ' already exists.');
      }
    }
  "

# Seed the database with mock data
echo -e "${YELLOW}Seeding database with mock data...${NC}"
node scripts/seed-data.js

echo -e "${GREEN}ArangoDB setup completed successfully!${NC}"
echo -e "${YELLOW}You can now access the ArangoDB web interface at:${NC}"
echo -e "${GREEN}$ARANGO_URL${NC}"
echo -e "${YELLOW}Username: $ARANGO_USERNAME${NC}"
echo -e "${YELLOW}Password: $ARANGO_PASSWORD${NC}"
echo -e "${YELLOW}Database: $ARANGO_DB_NAME${NC}"
