#!/bin/bash

# Colors for output
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

echo -e "${BLUE}ArangoDB Credentials Manager${NC}"
echo -e "${YELLOW}This script helps you manage ArangoDB credentials securely.${NC}"

# Function to add credentials to zsh configuration
add_to_zsh_config() {
  local url="$1"
  local username="$2"
  local password="$3"
  local db_name="$4"
  
  echo -e "${YELLOW}Adding ArangoDB credentials to ~/.zshrc...${NC}"
  
  # Backup existing .zshrc
  if [ -f ~/.zshrc ]; then
    cp ~/.zshrc ~/.zshrc.backup.$(date +%Y%m%d_%H%M%S)
    echo -e "${GREEN}Backed up existing .zshrc${NC}"
  fi
  
  # Remove existing ArangoDB configuration if it exists
  if grep -q "# ArangoDB Configuration" ~/.zshrc; then
    echo -e "${YELLOW}Removing existing ArangoDB configuration...${NC}"
    sed -i.bak '/# ArangoDB Configuration/,/# End ArangoDB Configuration/d' ~/.zshrc
  fi
  
  # Add new configuration
  cat >> ~/.zshrc << EOF

# ArangoDB Configuration
export ARANGO_URL="$url"
export ARANGO_USERNAME="$username"
export ARANGO_PASSWORD="$password"
export ARANGO_DB_NAME="$db_name"
export ARANGO_HOST="\$(echo \$ARANGO_URL | sed 's|.*://||' | sed 's|:.*||')"
export ARANGO_PORT="\$(echo \$ARANGO_URL | sed 's|.*:||' | sed 's|/.*||')"
# End ArangoDB Configuration
EOF
  
  echo -e "${GREEN}ArangoDB credentials added to ~/.zshrc${NC}"
  echo -e "${YELLOW}Run 'source ~/.zshrc' to load the new configuration.${NC}"
}

# Function to sync credentials with Amplify
sync_with_amplify() {
  echo -e "${YELLOW}Syncing credentials with AWS Amplify...${NC}"
  
  # Check if Amplify is initialized
  if [ ! -f "amplify/.config/project-config.json" ]; then
    echo -e "${RED}Amplify is not initialized in this project.${NC}"
    echo -e "${YELLOW}Run 'npm run amplify:setup' first.${NC}"
    return 1
  fi
  
  # Get Amplify app ID
  local app_id=$(amplify status -v 2>/dev/null | grep "App ID" | awk '{print $3}')
  
  if [ -z "$app_id" ]; then
    echo -e "${RED}Could not find Amplify App ID.${NC}"
    return 1
  fi
  
  echo -e "${GREEN}Found Amplify App ID: $app_id${NC}"
  
  # Set environment variables in Amplify
  echo -e "${YELLOW}Setting environment variables in Amplify...${NC}"
  
  aws amplify put-backend-environment \
    --app-id "$app_id" \
    --environment-name "main" \
    --environment-variables \
      ARANGO_URL="$ARANGO_URL" \
      ARANGO_USERNAME="$ARANGO_USERNAME" \
      ARANGO_PASSWORD="$ARANGO_PASSWORD" \
      ARANGO_DB_NAME="$ARANGO_DB_NAME"
  
  if [ $? -eq 0 ]; then
    echo -e "${GREEN}Environment variables synced with Amplify successfully!${NC}"
  else
    echo -e "${RED}Failed to sync environment variables with Amplify.${NC}"
    return 1
  fi
}

# Function to display current credentials
show_credentials() {
  echo -e "${BLUE}Current ArangoDB Configuration:${NC}"
  echo -e "URL: ${GREEN}${ARANGO_URL:-Not set}${NC}"
  echo -e "Username: ${GREEN}${ARANGO_USERNAME:-Not set}${NC}"
  echo -e "Password: ${GREEN}${ARANGO_PASSWORD:+***Hidden***}${RED}${ARANGO_PASSWORD:-Not set}${NC}"
  echo -e "Database: ${GREEN}${ARANGO_DB_NAME:-Not set}${NC}"
}

# Function to test connection
test_connection() {
  echo -e "${YELLOW}Testing ArangoDB connection...${NC}"
  
  if [ -z "$ARANGO_URL" ] || [ -z "$ARANGO_USERNAME" ] || [ -z "$ARANGO_PASSWORD" ]; then
    echo -e "${RED}Missing credentials. Please set them first.${NC}"
    return 1
  fi
  
  # Test connection using curl
  local response=$(curl -s -w "%{http_code}" -o /dev/null \
    -u "$ARANGO_USERNAME:$ARANGO_PASSWORD" \
    "$ARANGO_URL/_api/version")
  
  if [ "$response" = "200" ]; then
    echo -e "${GREEN}✓ Connection successful!${NC}"
    
    # Get version info
    local version=$(curl -s -u "$ARANGO_USERNAME:$ARANGO_PASSWORD" \
      "$ARANGO_URL/_api/version" | grep -o '"version":"[^"]*"' | cut -d'"' -f4)
    echo -e "${GREEN}ArangoDB Version: $version${NC}"
  else
    echo -e "${RED}✗ Connection failed (HTTP $response)${NC}"
    return 1
  fi
}

# Main menu
while true; do
  echo -e "\n${BLUE}What would you like to do?${NC}"
  echo "1. Set up new ArangoDB credentials"
  echo "2. Show current credentials"
  echo "3. Test connection"
  echo "4. Sync credentials with AWS Amplify"
  echo "5. Exit"
  
  read -p "Enter your choice (1-5): " choice
  
  case $choice in
    1)
      echo -e "\n${YELLOW}Setting up ArangoDB credentials...${NC}"
      echo -e "${BLUE}You can find these credentials in:${NC}"
      echo -e "• ArangoDB Cloud: Dashboard > Overview > Connection tab"
      echo -e "• Local Docker: http://localhost:8529 (root/rootpassword)"
      echo -e "• Self-hosted: Your server configuration"
      echo
      
      read -p "Enter ArangoDB URL (e.g., https://your-deployment.arangodb.cloud): " url
      read -p "Enter username: " username
      read -s -p "Enter password: " password
      echo
      read -p "Enter database name (default: candid_connections): " db_name
      
      # Set defaults
      db_name=${db_name:-candid_connections}
      
      # Validate inputs
      if [ -z "$url" ] || [ -z "$username" ] || [ -z "$password" ]; then
        echo -e "${RED}All fields are required!${NC}"
        continue
      fi
      
      # Add to zsh config
      add_to_zsh_config "$url" "$username" "$password" "$db_name"
      
      # Load the new configuration
      export ARANGO_URL="$url"
      export ARANGO_USERNAME="$username"
      export ARANGO_PASSWORD="$password"
      export ARANGO_DB_NAME="$db_name"
      
      echo -e "${GREEN}Credentials configured successfully!${NC}"
      ;;
    2)
      show_credentials
      ;;
    3)
      test_connection
      ;;
    4)
      sync_with_amplify
      ;;
    5)
      echo -e "${GREEN}Goodbye!${NC}"
      exit 0
      ;;
    *)
      echo -e "${RED}Invalid choice. Please try again.${NC}"
      ;;
  esac
done
