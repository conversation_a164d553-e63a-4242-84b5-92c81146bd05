#!/usr/bin/env node

const { Database } = require('arangojs');

async function seedSampleData() {
  console.log('🌱 Seeding Candid Connections with sample data...');

  // Use the correct credentials
  const config = {
    url: 'https://f2c8a97499a8.arangodb.cloud:8529',
    auth: {
      username: 'root',
      password: 'XMx2qSsHU8RWMX9VSxAx'
    },
    databaseName: 'candid_connections'
  };

  try {
    const db = new Database(config);

    // Test connection
    console.log('Testing connection...');
    await db.version();
    console.log('✅ Connected to ArangoDB');

    // Sample skills
    console.log('\n📚 Adding skills...');
    const skillsCollection = db.collection('skills');
    const skills = [
      { _key: 'javascript', name: 'JavaScript', category: 'Programming' },
      { _key: 'python', name: 'Python', category: 'Programming' },
      { _key: 'react', name: 'React', category: 'Frontend' },
      { _key: 'nodejs', name: 'Node.js', category: 'Backend' },
      { _key: 'aws', name: 'A<PERSON>', category: 'Cloud' },
      { _key: 'docker', name: 'Docker', category: 'DevOps' },
      { _key: 'sql', name: 'SQL', category: 'Database' },
      { _key: 'mongodb', name: 'MongoDB', category: 'Database' },
      { _key: 'graphql', name: 'GraphQL', category: 'API' },
      { _key: 'typescript', name: 'TypeScript', category: 'Programming' }
    ];

    for (const skill of skills) {
      try {
        await skillsCollection.save(skill);
        console.log(`  ✅ Added skill: ${skill.name}`);
      } catch (error) {
        if (error.code === 1210) { // Document already exists
          console.log(`  ⚠️  Skill already exists: ${skill.name}`);
        } else {
          throw error;
        }
      }
    }

    // Sample companies
    console.log('\n🏢 Adding companies...');
    const companiesCollection = db.collection('companies');
    const companies = [
      {
        _key: 'techcorp',
        name: 'TechCorp Solutions',
        industry: 'Technology',
        size: 'Large',
        employeeCount: 2500,
        location: 'San Francisco, CA',
        description: 'Leading enterprise software solutions provider'
      },
      {
        _key: 'innovate',
        name: 'Innovate Labs',
        industry: 'Software',
        size: 'Medium',
        employeeCount: 450,
        location: 'Austin, TX',
        description: 'Cutting-edge AI and machine learning startup'
      },
      {
        _key: 'dataflow',
        name: 'DataFlow Analytics',
        industry: 'Data Science',
        size: 'Small',
        employeeCount: 85,
        location: 'Seattle, WA',
        description: 'Data analytics and business intelligence consultancy'
      },
      {
        _key: 'cloudnine',
        name: 'Cloud Nine Systems',
        industry: 'Cloud Computing',
        size: 'Medium',
        employeeCount: 320,
        location: 'Denver, CO',
        description: 'Cloud infrastructure and DevOps solutions'
      },
      {
        _key: 'webcraft',
        name: 'WebCraft Studio',
        industry: 'Web Development',
        size: 'Small',
        employeeCount: 25,
        location: 'Portland, OR',
        description: 'Boutique web development and design agency'
      }
    ];

    for (const company of companies) {
      try {
        await companiesCollection.save(company);
        console.log(`  ✅ Added company: ${company.name}`);
      } catch (error) {
        if (error.code === 1210) {
          console.log(`  ⚠️  Company already exists: ${company.name}`);
        } else {
          throw error;
        }
      }
    }

    // Sample job seekers
    console.log('\n👤 Adding job seekers...');
    const jobSeekersCollection = db.collection('jobSeekers');
    const jobSeekers = [
      {
        _key: 'alice',
        name: 'Alice Johnson',
        email: '<EMAIL>',
        experience: 'Senior',
        location: 'San Francisco, CA',
        title: 'Full Stack Developer'
      },
      {
        _key: 'bob',
        name: 'Bob Smith',
        email: '<EMAIL>',
        experience: 'Mid-level',
        location: 'Austin, TX',
        title: 'Backend Developer'
      },
      {
        _key: 'carol',
        name: 'Carol Davis',
        email: '<EMAIL>',
        experience: 'Junior',
        location: 'Seattle, WA',
        title: 'Frontend Developer'
      },
      {
        _key: 'david',
        name: 'David Wilson',
        email: '<EMAIL>',
        experience: 'Senior',
        location: 'Denver, CO',
        title: 'DevOps Engineer'
      },
      {
        _key: 'eve',
        name: 'Eve Brown',
        email: '<EMAIL>',
        experience: 'Mid-level',
        location: 'Portland, OR',
        title: 'Data Scientist'
      }
    ];

    for (const seeker of jobSeekers) {
      try {
        await jobSeekersCollection.save(seeker);
        console.log(`  ✅ Added job seeker: ${seeker.name}`);
      } catch (error) {
        if (error.code === 1210) {
          console.log(`  ⚠️  Job seeker already exists: ${seeker.name}`);
        } else {
          throw error;
        }
      }
    }

    // Sample positions
    console.log('\n💼 Adding positions...');
    const positionsCollection = db.collection('positions');
    const positions = [
      {
        _key: 'pos1',
        title: 'Senior React Developer',
        company: 'techcorp',
        level: 'Senior',
        location: 'San Francisco, CA',
        salary: '$120,000 - $150,000'
      },
      {
        _key: 'pos2',
        title: 'Backend Engineer',
        company: 'innovate',
        level: 'Mid-level',
        location: 'Austin, TX',
        salary: '$90,000 - $110,000'
      },
      {
        _key: 'pos3',
        title: 'Data Analyst',
        company: 'dataflow',
        level: 'Junior',
        location: 'Seattle, WA',
        salary: '$70,000 - $85,000'
      },
      {
        _key: 'pos4',
        title: 'Cloud Architect',
        company: 'cloudnine',
        level: 'Senior',
        location: 'Denver, CO',
        salary: '$130,000 - $160,000'
      },
      {
        _key: 'pos5',
        title: 'Frontend Developer',
        company: 'webcraft',
        level: 'Mid-level',
        location: 'Portland, OR',
        salary: '$80,000 - $100,000'
      }
    ];

    for (const position of positions) {
      try {
        await positionsCollection.save(position);
        console.log(`  ✅ Added position: ${position.title}`);
      } catch (error) {
        if (error.code === 1210) {
          console.log(`  ⚠️  Position already exists: ${position.title}`);
        } else {
          throw error;
        }
      }
    }

    // Sample hiring authorities
    console.log('\n👔 Adding hiring authorities...');
    const hiringAuthoritiesCollection = db.collection('hiringAuthorities');
    const hiringAuthorities = [
      // TechCorp (Large company) - Multiple levels
      {
        _key: 'techcorp_hr_director',
        name: 'Sarah Chen',
        title: 'HR Director',
        level: 'Director',
        company: 'techcorp',
        email: '<EMAIL>',
        hiringScope: 'All levels',
        department: 'Human Resources'
      },
      {
        _key: 'techcorp_eng_vp',
        name: 'Michael Rodriguez',
        title: 'VP of Engineering',
        level: 'VP',
        company: 'techcorp',
        email: '<EMAIL>',
        hiringScope: 'Senior+ Engineering',
        department: 'Engineering'
      },
      {
        _key: 'techcorp_eng_manager',
        name: 'Jennifer Kim',
        title: 'Engineering Manager',
        level: 'Manager',
        company: 'techcorp',
        email: '<EMAIL>',
        hiringScope: 'Junior-Mid Engineering',
        department: 'Engineering'
      },
      // Innovate Labs (Medium company) - Fewer levels
      {
        _key: 'innovate_cto',
        name: 'Alex Thompson',
        title: 'CTO',
        level: 'CTO',
        company: 'innovate',
        email: '<EMAIL>',
        hiringScope: 'All technical roles',
        department: 'Technology'
      },
      {
        _key: 'innovate_hr_manager',
        name: 'Lisa Wang',
        title: 'HR Manager',
        level: 'Manager',
        company: 'innovate',
        email: '<EMAIL>',
        hiringScope: 'All non-technical roles',
        department: 'Human Resources'
      },
      // DataFlow (Small company) - Direct to leadership
      {
        _key: 'dataflow_ceo',
        name: 'Robert Johnson',
        title: 'CEO & Founder',
        level: 'CEO',
        company: 'dataflow',
        email: '<EMAIL>',
        hiringScope: 'All roles',
        department: 'Executive'
      },
      // CloudNine (Medium company)
      {
        _key: 'cloudnine_ops_director',
        name: 'Maria Garcia',
        title: 'Director of Operations',
        level: 'Director',
        company: 'cloudnine',
        email: '<EMAIL>',
        hiringScope: 'DevOps and Infrastructure',
        department: 'Operations'
      },
      // WebCraft (Small company) - Direct to founder
      {
        _key: 'webcraft_founder',
        name: 'David Park',
        title: 'Founder & Creative Director',
        level: 'Founder',
        company: 'webcraft',
        email: '<EMAIL>',
        hiringScope: 'All roles',
        department: 'Creative'
      }
    ];

    for (const authority of hiringAuthorities) {
      try {
        await hiringAuthoritiesCollection.save(authority);
        console.log(`  ✅ Added hiring authority: ${authority.name} (${authority.title})`);
      } catch (error) {
        if (error.code === 1210) {
          console.log(`  ⚠️  Hiring authority already exists: ${authority.name}`);
        } else {
          throw error;
        }
      }
    }

    // Sample skill connections
    console.log('\n🔗 Adding skill connections...');
    const seekerSkillsCollection = db.collection('seekerSkills');
    const skillConnections = [
      { _from: 'jobSeekers/alice', _to: 'skills/javascript', proficiency: 'Expert' },
      { _from: 'jobSeekers/alice', _to: 'skills/react', proficiency: 'Expert' },
      { _from: 'jobSeekers/alice', _to: 'skills/nodejs', proficiency: 'Advanced' },
      { _from: 'jobSeekers/bob', _to: 'skills/python', proficiency: 'Expert' },
      { _from: 'jobSeekers/bob', _to: 'skills/sql', proficiency: 'Advanced' },
      { _from: 'jobSeekers/carol', _to: 'skills/javascript', proficiency: 'Intermediate' },
      { _from: 'jobSeekers/carol', _to: 'skills/react', proficiency: 'Intermediate' },
      { _from: 'jobSeekers/david', _to: 'skills/aws', proficiency: 'Expert' },
      { _from: 'jobSeekers/david', _to: 'skills/docker', proficiency: 'Expert' },
      { _from: 'jobSeekers/eve', _to: 'skills/python', proficiency: 'Advanced' },
      { _from: 'jobSeekers/eve', _to: 'skills/mongodb', proficiency: 'Intermediate' }
    ];

    for (const connection of skillConnections) {
      try {
        await seekerSkillsCollection.save(connection);
        console.log(`  ✅ Added skill connection`);
      } catch (error) {
        if (error.code === 1210) {
          console.log(`  ⚠️  Skill connection already exists`);
        } else {
          throw error;
        }
      }
    }

    // Company-Hiring Authority relationships
    console.log('\n🏢 Adding company-hiring authority relationships...');
    const companyHiringAuthoritiesCollection = db.collection('companyHiringAuthorities');
    const companyHiringRelations = [
      { _from: 'companies/techcorp', _to: 'hiringAuthorities/techcorp_hr_director', role: 'HR Director' },
      { _from: 'companies/techcorp', _to: 'hiringAuthorities/techcorp_eng_vp', role: 'VP Engineering' },
      { _from: 'companies/techcorp', _to: 'hiringAuthorities/techcorp_eng_manager', role: 'Engineering Manager' },
      { _from: 'companies/innovate', _to: 'hiringAuthorities/innovate_cto', role: 'CTO' },
      { _from: 'companies/innovate', _to: 'hiringAuthorities/innovate_hr_manager', role: 'HR Manager' },
      { _from: 'companies/dataflow', _to: 'hiringAuthorities/dataflow_ceo', role: 'CEO' },
      { _from: 'companies/cloudnine', _to: 'hiringAuthorities/cloudnine_ops_director', role: 'Operations Director' },
      { _from: 'companies/webcraft', _to: 'hiringAuthorities/webcraft_founder', role: 'Founder' }
    ];

    for (const relation of companyHiringRelations) {
      try {
        await companyHiringAuthoritiesCollection.save(relation);
        console.log(`  ✅ Added company-hiring authority relationship`);
      } catch (error) {
        if (error.code === 1210) {
          console.log(`  ⚠️  Relationship already exists`);
        } else {
          throw error;
        }
      }
    }

    // Position skill requirements
    console.log('\n💼 Adding position skill requirements...');
    const positionSkillsCollection = db.collection('positionSkills');
    const positionSkillRequirements = [
      // Senior React Developer at TechCorp
      { _from: 'positions/pos1', _to: 'skills/javascript', required: true, level: 'Expert' },
      { _from: 'positions/pos1', _to: 'skills/react', required: true, level: 'Expert' },
      { _from: 'positions/pos1', _to: 'skills/typescript', required: true, level: 'Advanced' },
      { _from: 'positions/pos1', _to: 'skills/nodejs', required: false, level: 'Intermediate' },

      // Backend Engineer at Innovate
      { _from: 'positions/pos2', _to: 'skills/python', required: true, level: 'Expert' },
      { _from: 'positions/pos2', _to: 'skills/sql', required: true, level: 'Advanced' },
      { _from: 'positions/pos2', _to: 'skills/mongodb', required: false, level: 'Intermediate' },

      // Data Analyst at DataFlow
      { _from: 'positions/pos3', _to: 'skills/python', required: true, level: 'Intermediate' },
      { _from: 'positions/pos3', _to: 'skills/sql', required: true, level: 'Advanced' },

      // Cloud Architect at CloudNine
      { _from: 'positions/pos4', _to: 'skills/aws', required: true, level: 'Expert' },
      { _from: 'positions/pos4', _to: 'skills/docker', required: true, level: 'Expert' },

      // Frontend Developer at WebCraft
      { _from: 'positions/pos5', _to: 'skills/javascript', required: true, level: 'Advanced' },
      { _from: 'positions/pos5', _to: 'skills/react', required: true, level: 'Advanced' }
    ];

    for (const requirement of positionSkillRequirements) {
      try {
        await positionSkillsCollection.save(requirement);
        console.log(`  ✅ Added position skill requirement`);
      } catch (error) {
        if (error.code === 1210) {
          console.log(`  ⚠️  Skill requirement already exists`);
        } else {
          throw error;
        }
      }
    }

    // Hiring Authority-Position relationships
    console.log('\n👔 Adding hiring authority-position relationships...');
    const hiringAuthorityPositionsCollection = db.collection('hiringAuthorityPositions');
    const hiringPositionRelations = [
      { _from: 'hiringAuthorities/techcorp_eng_vp', _to: 'positions/pos1', authority: 'primary' },
      { _from: 'hiringAuthorities/innovate_cto', _to: 'positions/pos2', authority: 'primary' },
      { _from: 'hiringAuthorities/dataflow_ceo', _to: 'positions/pos3', authority: 'primary' },
      { _from: 'hiringAuthorities/cloudnine_ops_director', _to: 'positions/pos4', authority: 'primary' },
      { _from: 'hiringAuthorities/webcraft_founder', _to: 'positions/pos5', authority: 'primary' }
    ];

    for (const relation of hiringPositionRelations) {
      try {
        await hiringAuthorityPositionsCollection.save(relation);
        console.log(`  ✅ Added hiring authority-position relationship`);
      } catch (error) {
        if (error.code === 1210) {
          console.log(`  ⚠️  Relationship already exists`);
        } else {
          throw error;
        }
      }
    }

    console.log('\n🎉 Sample data seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`  • ${skills.length} skills`);
    console.log(`  • ${companies.length} companies`);
    console.log(`  • ${jobSeekers.length} job seekers`);
    console.log(`  • ${positions.length} positions`);
    console.log(`  • ${hiringAuthorities.length} hiring authorities`);
    console.log(`  • ${skillConnections.length} job seeker skill connections`);
    console.log(`  • ${companyHiringRelations.length} company-hiring authority relationships`);
    console.log(`  • ${positionSkillRequirements.length} position skill requirements`);
    console.log(`  • ${hiringPositionRelations.length} hiring authority-position relationships`);

    console.log('\n🚀 Ready to view your dashboard at http://localhost:3000');

  } catch (error) {
    console.error('❌ Error seeding data:', error.message);
    process.exit(1);
  }
}

seedSampleData();
