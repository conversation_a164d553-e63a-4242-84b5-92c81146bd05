#!/usr/bin/env node

const { Database } = require('arangojs');

async function testAndInitializeDatabase() {
  console.log('🔍 Testing ArangoDB connection and initializing database...');

  // Use the correct credentials
  const config = {
    url: 'https://f2c8a97499a8.arangodb.cloud:8529',
    auth: {
      username: 'root',
      password: 'XMx2qSsHU8RWMX9VSxAx'
    },
    databaseName: '_system'
  };

  console.log(`Connecting to: ${config.url}`);

  try {
    const db = new Database(config);

    // Test connection
    console.log('Testing connection...');
    const version = await db.version();
    console.log(`✅ Connected! ArangoDB version: ${version.version}`);

    // Create database if it doesn't exist
    const dbName = 'candid_connections';
    console.log(`\nChecking if database '${dbName}' exists...`);

    const databases = await db.listDatabases();
    console.log('Available databases:', databases);

    if (!databases.includes(dbName)) {
      console.log(`Creating database: ${dbName}`);
      await db.createDatabase(dbName);
      console.log(`✅ Database ${dbName} created successfully.`);
    } else {
      console.log(`✅ Database ${dbName} already exists.`);
    }

    // Switch to the application database
    const appDb = db.database(dbName);

    // Create collections
    console.log('\nCreating collections...');
    const collections = [
      { name: 'jobSeekers', type: 'document' },
      { name: 'companies', type: 'document' },
      { name: 'hiringAuthorities', type: 'document' },
      { name: 'positions', type: 'document' },
      { name: 'skills', type: 'document' },
      { name: 'seekerSkills', type: 'edge' },
      { name: 'positionSkills', type: 'edge' },
      { name: 'companyHiringAuthorities', type: 'edge' },
      { name: 'hiringAuthorityPositions', type: 'edge' },
      { name: 'matches', type: 'edge' },
      { name: 'companyPositions', type: 'edge' }
    ];

    for (const collection of collections) {
      try {
        const exists = await appDb.collection(collection.name).exists();

        if (!exists) {
          console.log(`Creating ${collection.type} collection: ${collection.name}`);
          if (collection.type === 'edge') {
            await appDb.createEdgeCollection(collection.name);
          } else {
            await appDb.createCollection(collection.name);
          }
          console.log(`✅ Collection ${collection.name} created.`);
        } else {
          console.log(`✅ Collection ${collection.name} already exists.`);
        }
      } catch (error) {
        console.error(`❌ Error with collection ${collection.name}:`, error.message);
      }
    }

    console.log('\n🎉 Database initialization completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('1. Run: npm run dev');
    console.log('2. Open: http://localhost:3000');
    console.log('3. When ready to deploy: npm run amplify:setup');

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
}

testAndInitializeDatabase();
