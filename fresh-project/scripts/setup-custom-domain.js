#!/usr/bin/env node

const { execSync } = require('child_process');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// ANSI color codes
const YELLOW = '\x1b[33m';
const GREEN = '\x1b[32m';
const RED = '\x1b[31m';
const NC = '\x1b[0m'; // No Color

console.log(`${YELLOW}Setting up custom domain for Amplify app...${NC}`);

// Function to execute shell commands
function executeCommand(command) {
  try {
    return execSync(command, { encoding: 'utf8' });
  } catch (error) {
    console.error(`${RED}Error executing command: ${command}${NC}`);
    console.error(error.message);
    process.exit(1);
  }
}

// Function to get Amplify app ID
function getAmplifyAppId() {
  try {
    const amplifyStatus = executeCommand('amplify status -v');
    const appIdMatch = amplifyStatus.match(/App ID: ([a-z0-9]+)/i);
    if (appIdMatch && appIdMatch[1]) {
      return appIdMatch[1];
    } else {
      throw new Error('Could not find Amplify App ID');
    }
  } catch (error) {
    console.error(`${RED}Error getting Amplify App ID: ${error.message}${NC}`);
    console.error(`${YELLOW}Make sure you've initialized Amplify in this project.${NC}`);
    process.exit(1);
  }
}

// Main function
async function setupCustomDomain() {
  // Check if AWS CLI is installed
  try {
    executeCommand('aws --version');
  } catch (error) {
    console.error(`${RED}AWS CLI is not installed. Please install it and try again.${NC}`);
    process.exit(1);
  }

  // Check if Amplify CLI is installed
  try {
    executeCommand('amplify --version');
  } catch (error) {
    console.error(`${RED}Amplify CLI is not installed. Please install it and try again.${NC}`);
    process.exit(1);
  }

  // Get Amplify app ID
  const appId = getAmplifyAppId();
  console.log(`${GREEN}Found Amplify App ID: ${appId}${NC}`);

  // Prompt for domain name
  rl.question(`${YELLOW}Enter the domain name (default: candid.pbradygeorgen.com): ${NC}`, (domainName) => {
    const domain = domainName || 'candid.pbradygeorgen.com';
    
    console.log(`${YELLOW}Setting up custom domain: ${domain}${NC}`);
    
    // Create custom domain in Amplify
    try {
      console.log(`${YELLOW}Creating custom domain in Amplify...${NC}`);
      const createDomainCommand = `aws amplify create-domain-association --app-id ${appId} --domain-name pbradygeorgen.com --sub-domain-settings subDomainSetting="{\\"prefix\\":\\"candid\\",\\"branchName\\":\\"main\\"}"`;
      
      const result = executeCommand(createDomainCommand);
      console.log(`${GREEN}Custom domain created successfully!${NC}`);
      console.log(result);
      
      // Get DNS records
      console.log(`${YELLOW}Getting DNS records...${NC}`);
      const getDnsCommand = `aws amplify get-domain-association --app-id ${appId} --domain-name pbradygeorgen.com`;
      const dnsResult = executeCommand(getDnsCommand);
      
      console.log(`${GREEN}DNS configuration:${NC}`);
      console.log(dnsResult);
      
      console.log(`${YELLOW}Please add the above DNS records to your domain provider.${NC}`);
      console.log(`${YELLOW}It may take up to 48 hours for DNS changes to propagate.${NC}`);
      console.log(`${GREEN}Your app will be available at: https://${domain}${NC}`);
    } catch (error) {
      console.error(`${RED}Error setting up custom domain: ${error.message}${NC}`);
      
      // Check if domain already exists
      if (error.message.includes('already exists')) {
        console.log(`${YELLOW}Domain already exists. Getting current configuration...${NC}`);
        try {
          const getDnsCommand = `aws amplify get-domain-association --app-id ${appId} --domain-name pbradygeorgen.com`;
          const dnsResult = executeCommand(getDnsCommand);
          
          console.log(`${GREEN}Current DNS configuration:${NC}`);
          console.log(dnsResult);
        } catch (innerError) {
          console.error(`${RED}Error getting domain configuration: ${innerError.message}${NC}`);
        }
      }
    }
    
    rl.close();
  });
}

// Run the main function
setupCustomDomain();
