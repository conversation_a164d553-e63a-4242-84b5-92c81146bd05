#!/bin/bash

# Colors for output
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

echo -e "${BLUE}🔍 ArangoDB Connection Diagnostics${NC}"
echo

# Test different URL formats
URLS=(
  "https://12c8a97499a8.arangodb.cloud"
  "https://12c8a97499a8.arangodb.cloud:8529"
  "https://12c8a97499a8.arangodb.cloud:443"
  "http://12c8a97499a8.arangodb.cloud"
  "http://12c8a97499a8.arangodb.cloud:8529"
)

USERNAME="root"
PASSWORD="XMx2qSsHU8RWMX9VSxAx"

echo -e "${YELLOW}Testing different URL formats...${NC}"
echo

for URL in "${URLS[@]}"; do
  echo -e "${BLUE}Testing: $URL${NC}"
  
  # Test basic connectivity first
  echo -n "  Basic connectivity: "
  if curl -s --connect-timeout 10 --max-time 15 "$URL" > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Reachable${NC}"
  else
    echo -e "${RED}✗ Not reachable${NC}"
    continue
  fi
  
  # Test with credentials
  echo -n "  With credentials: "
  RESPONSE=$(curl -s -w "%{http_code}" -o /dev/null \
    --connect-timeout 10 --max-time 15 \
    -u "$USERNAME:$PASSWORD" \
    "$URL/_api/version" 2>/dev/null)
  
  if [ "$RESPONSE" = "200" ]; then
    echo -e "${GREEN}✓ Success (HTTP $RESPONSE)${NC}"
    echo -e "${GREEN}🎯 Working URL found: $URL${NC}"
    
    # Get version info
    VERSION=$(curl -s -u "$USERNAME:$PASSWORD" \
      "$URL/_api/version" 2>/dev/null | grep -o '"version":"[^"]*"' | cut -d'"' -f4)
    echo -e "${GREEN}ArangoDB Version: $VERSION${NC}"
    
    echo -e "\n${YELLOW}Use this configuration:${NC}"
    echo -e "export ARANGO_URL=\"$URL\""
    echo -e "export ARANGO_USERNAME=\"$USERNAME\""
    echo -e "export ARANGO_PASSWORD=\"$PASSWORD\""
    echo -e "export ARANGO_DB_NAME=\"candid_connections\""
    
    exit 0
  elif [ "$RESPONSE" = "401" ]; then
    echo -e "${YELLOW}✗ Authentication failed (HTTP $RESPONSE)${NC}"
  elif [ "$RESPONSE" = "000" ]; then
    echo -e "${RED}✗ Connection failed (HTTP $RESPONSE)${NC}"
  else
    echo -e "${YELLOW}✗ HTTP $RESPONSE${NC}"
  fi
done

echo -e "\n${RED}No working URL found. Let's try some additional diagnostics...${NC}"
echo

# Additional diagnostics
echo -e "${YELLOW}DNS Resolution:${NC}"
nslookup 12c8a97499a8.arangodb.cloud

echo -e "\n${YELLOW}Ping test:${NC}"
ping -c 3 12c8a97499a8.arangodb.cloud

echo -e "\n${YELLOW}Port connectivity tests:${NC}"
for port in 443 8529 80; do
  echo -n "Port $port: "
  if nc -z -w5 12c8a97499a8.arangodb.cloud $port 2>/dev/null; then
    echo -e "${GREEN}Open${NC}"
  else
    echo -e "${RED}Closed/Filtered${NC}"
  fi
done

echo -e "\n${YELLOW}Curl verbose test:${NC}"
curl -v --connect-timeout 10 --max-time 15 \
  -u "$USERNAME:$PASSWORD" \
  "https://12c8a97499a8.arangodb.cloud/_api/version" 2>&1 | head -20

echo -e "\n${BLUE}Possible issues:${NC}"
echo -e "1. ${YELLOW}Firewall blocking the connection${NC}"
echo -e "2. ${YELLOW}VPN or corporate network restrictions${NC}"
echo -e "3. ${YELLOW}ArangoDB deployment not fully ready${NC}"
echo -e "4. ${YELLOW}Incorrect endpoint URL${NC}"
echo -e "5. ${YELLOW}DNS resolution issues${NC}"

echo -e "\n${BLUE}Try these solutions:${NC}"
echo -e "1. ${YELLOW}Check if you're on a corporate network or VPN${NC}"
echo -e "2. ${YELLOW}Try from a different network (mobile hotspot)${NC}"
echo -e "3. ${YELLOW}Check ArangoDB Cloud dashboard for deployment status${NC}"
echo -e "4. ${YELLOW}Verify the endpoint URL in your ArangoDB Cloud console${NC}"
