#!/bin/bash

# Colors for output
GREEN="\033[0;32m"
YELLOW="\033[0;33m"
RED="\033[0;31m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

echo -e "${BLUE}🚀 Quick ArangoDB Cloud Setup${NC}"
echo -e "${YELLOW}This will set up your ArangoDB Cloud credentials automatically.${NC}"
echo

# Pre-filled values from your deployment
URL="https://12c8a97499a8.arangodb.cloud:8529"
USERNAME="root"
DB_NAME="candid_connections"

echo -e "${GREEN}Auto-detected from your ArangoDB Cloud deployment:${NC}"
echo -e "URL: ${BLUE}$URL${NC}"
echo -e "Username: ${BLUE}$USERNAME${NC}"
echo -e "Database: ${BLUE}$DB_NAME${NC}"
echo

echo -e "${YELLOW}📋 Instructions:${NC}"
echo -e "1. In your browser, go to your ArangoDB Cloud dashboard"
echo -e "2. Look for the 'Root password' field with asterisks (****************)"
echo -e "3. Copy the actual password (you might need to click 'Show' or similar)"
echo -e "4. Paste it below when prompted"
echo

read -s -p "Enter your ArangoDB password: " PASSWORD
echo

if [ -z "$PASSWORD" ]; then
  echo -e "${RED}Password is required!${NC}"
  exit 1
fi

echo -e "\n${YELLOW}Testing connection...${NC}"

# Test connection
RESPONSE=$(curl -s -w "%{http_code}" -o /dev/null \
  -u "$USERNAME:$PASSWORD" \
  "$URL/_api/version")

if [ "$RESPONSE" = "200" ]; then
  echo -e "${GREEN}✓ Connection successful!${NC}"
  
  # Get ArangoDB version
  VERSION=$(curl -s -u "$USERNAME:$PASSWORD" \
    "$URL/_api/version" | grep -o '"version":"[^"]*"' | cut -d'"' -f4)
  echo -e "${GREEN}ArangoDB Version: $VERSION${NC}"
  
  echo -e "\n${YELLOW}Adding credentials to ~/.zshrc...${NC}"
  
  # Backup existing .zshrc
  if [ -f ~/.zshrc ]; then
    cp ~/.zshrc ~/.zshrc.backup.$(date +%Y%m%d_%H%M%S)
    echo -e "${GREEN}Backed up existing .zshrc${NC}"
  fi
  
  # Remove existing ArangoDB configuration if it exists
  if grep -q "# ArangoDB Configuration" ~/.zshrc 2>/dev/null; then
    echo -e "${YELLOW}Removing existing ArangoDB configuration...${NC}"
    sed -i.bak '/# ArangoDB Configuration/,/# End ArangoDB Configuration/d' ~/.zshrc
  fi
  
  # Add new configuration
  cat >> ~/.zshrc << EOF

# ArangoDB Configuration
export ARANGO_URL="$URL"
export ARANGO_USERNAME="$USERNAME"
export ARANGO_PASSWORD="$PASSWORD"
export ARANGO_DB_NAME="$DB_NAME"
export ARANGO_HOST="12c8a97499a8.arangodb.cloud"
export ARANGO_PORT="8529"
# End ArangoDB Configuration
EOF
  
  # Load the new configuration in current session
  export ARANGO_URL="$URL"
  export ARANGO_USERNAME="$USERNAME"
  export ARANGO_PASSWORD="$PASSWORD"
  export ARANGO_DB_NAME="$DB_NAME"
  export ARANGO_HOST="12c8a97499a8.arangodb.cloud"
  export ARANGO_PORT="8529"
  
  echo -e "${GREEN}✅ Credentials saved to ~/.zshrc${NC}"
  echo -e "${GREEN}✅ Credentials loaded in current session${NC}"
  
  echo -e "\n${BLUE}🎯 Next Steps:${NC}"
  echo -e "1. ${YELLOW}Create the database:${NC}"
  echo -e "   • Go to your ArangoDB Cloud web interface"
  echo -e "   • Click 'Databases' → 'Add Database'"
  echo -e "   • Name it: ${BLUE}candid_connections${NC}"
  echo
  echo -e "2. ${YELLOW}Initialize and seed the database:${NC}"
  echo -e "   ${BLUE}npm run db:setup-all${NC}"
  echo
  echo -e "3. ${YELLOW}Start development:${NC}"
  echo -e "   ${BLUE}npm run dev${NC}"
  echo
  echo -e "4. ${YELLOW}When ready to deploy:${NC}"
  echo -e "   ${BLUE}npm run amplify:setup${NC}"
  echo -e "   ${BLUE}npm run amplify:deploy${NC}"
  echo
  echo -e "${GREEN}🚀 You're all set! Run 'source ~/.zshrc' in other terminals to load the config.${NC}"
  
else
  echo -e "${RED}✗ Connection failed (HTTP $RESPONSE)${NC}"
  echo -e "${YELLOW}Please check your password and try again.${NC}"
  echo -e "${YELLOW}Make sure you copied the actual password, not the asterisks.${NC}"
  exit 1
fi
