#!/usr/bin/env node

const { Database } = require('arangojs');

async function resetAndSeedData() {
  console.log('🔄 Resetting and seeding Candid Connections with fresh data...');
  
  // Use the correct credentials
  const config = {
    url: 'https://f2c8a97499a8.arangodb.cloud:8529',
    auth: { 
      username: 'root', 
      password: 'XMx2qSsHU8RWMX9VSxAx' 
    },
    databaseName: 'candid_connections'
  };
  
  try {
    const db = new Database(config);
    
    // Test connection
    console.log('Testing connection...');
    await db.version();
    console.log('✅ Connected to ArangoDB');
    
    // Clear existing data
    console.log('\n🗑️  Clearing existing data...');
    const collections = [
      'jobSeekers', 'companies', 'hiringAuthorities', 'positions', 'skills',
      'seekerSkills', 'positionSkills', 'companyHiringAuthorities', 'hiringAuthorityPositions'
    ];
    
    for (const collectionName of collections) {
      try {
        const collection = db.collection(collectionName);
        await collection.truncate();
        console.log(`  ✅ Cleared ${collectionName}`);
      } catch (error) {
        console.log(`  ⚠️  Could not clear ${collectionName}: ${error.message}`);
      }
    }
    
    console.log('\n🌱 Now seeding fresh data...');
    
    // Run the seeding script
    const { execSync } = require('child_process');
    execSync('node scripts/seed-sample-data.js', { stdio: 'inherit' });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

resetAndSeedData();
