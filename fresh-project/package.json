{"name": "fresh-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "setup": "./setup.sh", "db:init": "node scripts/init-db.js", "db:seed": "node scripts/seed-data.js", "db:reset": "node scripts/init-db.js && node scripts/seed-data.js", "db:setup-all": "./scripts/db-setup-all.sh", "db:start": "cd docker && docker-compose up -d && cd ..", "db:stop": "cd docker && docker-compose down && cd ..", "db:backup": "./scripts/backup-db.sh", "db:restore": "./scripts/restore-db.sh", "db:install-tools": "./scripts/install-arango-tools.sh", "db:credentials": "./scripts/manage-arango-credentials.sh", "db:quick-setup": "./scripts/quick-arango-setup.sh", "db:paste-setup": "./scripts/paste-password-setup.sh", "amplify:setup": "./amplify-setup.sh", "amplify:deploy": "amplify publish", "amplify:domain": "node scripts/setup-custom-domain.js"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@react-three/drei": "^10.0.8", "@react-three/fiber": "^9.1.2", "arangojs": "^10.1.1", "axios": "^1.9.0", "d3": "^7.9.0", "d3-force-3d": "^3.0.6", "express": "^5.1.0", "next": "14.2.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-force-graph": "^1.29.3", "react-force-graph-2d": "^1.27.1", "react-force-graph-3d": "^1.26.1", "react-hook-form": "^7.56.4", "swr": "^2.3.3", "three": "^0.176.0", "uuid": "^11.1.0", "zod": "^3.25.23"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/three": "^0.176.0", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "14.2.5", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}