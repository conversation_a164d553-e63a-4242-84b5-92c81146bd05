'use client';

import { useEffect, useRef, useState } from 'react';
import dynamic from 'next/dynamic';
import { Database } from 'arangojs';

// Dynamically import ForceGraph2D to avoid SSR issues
const ForceGraph2D = dynamic(() => import('react-force-graph-2d'), { ssr: false });

interface Node {
  id: string;
  name: string;
  type: 'jobSeeker' | 'company' | 'hiringAuthority' | 'position' | 'skill';
  color: string;
  size: number;
  data?: any;
}

interface Link {
  source: string;
  target: string;
  type: string;
  value: number;
}

interface GraphData {
  nodes: Node[];
  links: Link[];
}

export default function GraphVisualization() {
  const [graphData, setGraphData] = useState<GraphData>({ nodes: [], links: [] });
  const [isLoading, setIsLoading] = useState(true);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fgRef = useRef<any>();

  useEffect(() => {
    fetchGraphData();
  }, []);

  const fetchGraphData = async () => {
    try {
      setIsLoading(true);

      const db = new Database({
        url: process.env.NEXT_PUBLIC_ARANGO_URL || 'https://f2c8a97499a8.arangodb.cloud:8529',
        auth: {
          username: process.env.NEXT_PUBLIC_ARANGO_USERNAME || 'root',
          password: process.env.NEXT_PUBLIC_ARANGO_PASSWORD || 'XMx2qSsHU8RWMX9VSxAx'
        },
        databaseName: process.env.NEXT_PUBLIC_ARANGO_DB_NAME || 'candid_connections'
      });

      // Fetch all data
      const [jobSeekers, companies, hiringAuthorities, positions, skills, seekerSkills, companyHiring, positionSkills] = await Promise.all([
        db.collection('jobSeekers').all().then(cursor => cursor.all()),
        db.collection('companies').all().then(cursor => cursor.all()),
        db.collection('hiringAuthorities').all().then(cursor => cursor.all()),
        db.collection('positions').all().then(cursor => cursor.all()),
        db.collection('skills').all().then(cursor => cursor.all()),
        db.collection('seekerSkills').all().then(cursor => cursor.all()),
        db.collection('companyHiringAuthorities').all().then(cursor => cursor.all()),
        db.collection('positionSkills').all().then(cursor => cursor.all())
      ]);

      // Create nodes
      const nodes: Node[] = [
        ...jobSeekers.map(seeker => ({
          id: seeker._id,
          name: seeker.name,
          type: 'jobSeeker' as const,
          color: '#3B82F6', // Blue
          size: 8,
          data: seeker
        })),
        ...companies.map(company => ({
          id: company._id,
          name: company.name,
          type: 'company' as const,
          color: '#10B981', // Green
          size: 10,
          data: company
        })),
        ...hiringAuthorities.map(authority => ({
          id: authority._id,
          name: authority.name,
          type: 'hiringAuthority' as const,
          color: '#F97316', // Orange
          size: 7,
          data: authority
        })),
        ...positions.map(position => ({
          id: position._id,
          name: position.title,
          type: 'position' as const,
          color: '#8B5CF6', // Purple
          size: 6,
          data: position
        })),
        ...skills.map(skill => ({
          id: skill._id,
          name: skill.name,
          type: 'skill' as const,
          color: '#F59E0B', // Amber
          size: 4,
          data: skill
        }))
      ];

      // Create links
      const links: Link[] = [
        ...seekerSkills.map(edge => ({
          source: edge._from,
          target: edge._to,
          type: 'hasSkill',
          value: 1
        })),
        ...companyHiring.map(edge => ({
          source: edge._from,
          target: edge._to,
          type: 'employs',
          value: 1
        })),
        ...positionSkills.map(edge => ({
          source: edge._from,
          target: edge._to,
          type: 'requires',
          value: 1
        }))
      ];

      setGraphData({ nodes, links });
    } catch (err) {
      console.error('Error fetching graph data:', err);
      setError('Failed to load graph data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleNodeClick = (node: any) => {
    setSelectedNode(node);
  };

  const getNodeColor = (node: Node) => {
    if (selectedNode && selectedNode.id === node.id) {
      return '#EF4444'; // Red for selected
    }
    return node.color;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading graph visualization...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <div className="text-red-600 text-4xl mb-4">⚠️</div>
          <p className="text-red-600">{error}</p>
          <button
            onClick={fetchGraphData}
            className="mt-4 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
      <div className="p-4 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-900">Network Graph</h3>
          <div className="flex space-x-4 text-sm">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
              <span>Job Seekers</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
              <span>Companies</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
              <span>Hiring Authorities</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
              <span>Positions</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-amber-500 rounded-full mr-2"></div>
              <span>Skills</span>
            </div>
          </div>
        </div>
      </div>

      <div className="flex">
        <div className="flex-1">
          <ForceGraph2D
            ref={fgRef}
            graphData={graphData}
            nodeLabel="name"
            nodeColor={getNodeColor}
            nodeRelSize={6}
            linkColor={() => '#94A3B8'}
            linkWidth={2}
            onNodeClick={handleNodeClick}
            width={800}
            height={500}
            backgroundColor="#F8FAFC"
            enableNodeDrag={true}
            enableZoomInteraction={true}
            enablePanInteraction={true}
          />
        </div>

        {/* Node Details Panel */}
        {selectedNode && (
          <div className="w-80 border-l border-gray-200 p-4 bg-gray-50">
            <div className="mb-4">
              <h4 className="text-lg font-semibold text-gray-900 mb-2">
                {selectedNode.name}
              </h4>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                selectedNode.type === 'jobSeeker' ? 'bg-blue-100 text-blue-800' :
                selectedNode.type === 'company' ? 'bg-green-100 text-green-800' :
                selectedNode.type === 'hiringAuthority' ? 'bg-orange-100 text-orange-800' :
                selectedNode.type === 'position' ? 'bg-purple-100 text-purple-800' :
                'bg-amber-100 text-amber-800'
              }`}>
                {selectedNode.type === 'jobSeeker' ? 'Job Seeker' :
                 selectedNode.type === 'company' ? 'Company' :
                 selectedNode.type === 'hiringAuthority' ? 'Hiring Authority' :
                 selectedNode.type === 'position' ? 'Position' : 'Skill'}
              </span>
            </div>

            <div className="space-y-3">
              {selectedNode.data && Object.entries(selectedNode.data).map(([key, value]) => {
                if (key.startsWith('_') || !value) return null;
                return (
                  <div key={key}>
                    <dt className="text-sm font-medium text-gray-500 capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()}
                    </dt>
                    <dd className="text-sm text-gray-900">{String(value)}</dd>
                  </div>
                );
              })}
            </div>

            <div className="mt-6 space-y-2">
              <button className="w-full bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                Edit Details
              </button>
              <button className="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                View Connections
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
