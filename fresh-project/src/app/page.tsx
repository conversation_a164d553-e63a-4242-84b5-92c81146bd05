'use client';

import { useState, useEffect } from 'react';
import { Database } from 'arangojs';

interface Stats {
  jobSeekers: number;
  companies: number;
  hiringAuthorities: number;
  positions: number;
  skills: number;
  connections: number;
}

export default function CandidConnectionsDashboard() {
  const [stats, setStats] = useState<Stats>({
    jobSeekers: 0,
    companies: 0,
    hiringAuthorities: 0,
    positions: 0,
    skills: 0,
    connections: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      setIsLoading(true);

      // Initialize database connection
      const db = new Database({
        url: process.env.NEXT_PUBLIC_ARANGO_URL || 'https://f2c8a97499a8.arangodb.cloud:8529',
        auth: {
          username: process.env.NEXT_PUBLIC_ARANGO_USERNAME || 'root',
          password: process.env.NEXT_PUBLIC_ARANGO_PASSWORD || 'XMx2qSsHU8RWMX9VSxAx'
        },
        databaseName: process.env.NEXT_PUBLIC_ARANGO_DB_NAME || 'candid_connections'
      });

      // Get collection counts
      const [jobSeekers, companies, hiringAuthorities, positions, skills, connections] = await Promise.all([
        db.collection('jobSeekers').count(),
        db.collection('companies').count(),
        db.collection('hiringAuthorities').count(),
        db.collection('positions').count(),
        db.collection('skills').count(),
        db.collection('seekerSkills').count()
      ]);

      setStats({
        jobSeekers: jobSeekers.count,
        companies: companies.count,
        hiringAuthorities: hiringAuthorities.count,
        positions: positions.count,
        skills: skills.count,
        connections: connections.count
      });
    } catch (err) {
      console.error('Error fetching stats:', err);
      setError('Failed to connect to database. Please check your configuration.');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading Candid Connections...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-600 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-red-800 mb-2">Connection Error</h1>
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchStats}
            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
          >
            Retry Connection
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-3xl font-bold text-gray-900">
                  🤝 Candid Connections
                </h1>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                ✅ Connected to ArangoDB
              </span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* Welcome Section */}
        <div className="px-4 py-6 sm:px-0">
          <div className="bg-white overflow-hidden shadow rounded-lg mb-6">
            <div className="px-4 py-5 sm:p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Welcome to Candid Connections! 🚀
              </h2>
              <p className="text-gray-600 mb-4">
                Your intelligent job matching platform powered by ArangoDB graph database.
                Connect job seekers with the perfect opportunities through advanced relationship mapping.
              </p>
              <div className="flex space-x-4">
                <a
                  href="/data"
                  className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  Manage Data
                </a>
                <a
                  href="/graph"
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                >
                  View Graph
                </a>
                <a
                  href="/matches"
                  className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
                >
                  Find Matches
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="px-4 py-6 sm:px-0">
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-6">
            {/* Job Seekers */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="text-3xl">👤</div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Job Seekers
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {stats.jobSeekers.toLocaleString()}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            {/* Companies */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="text-3xl">🏢</div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Companies
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {stats.companies.toLocaleString()}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            {/* Hiring Authorities */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="text-3xl">👔</div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Hiring Authorities
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {stats.hiringAuthorities.toLocaleString()}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            {/* Positions */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="text-3xl">💼</div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Open Positions
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {stats.positions.toLocaleString()}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            {/* Skills */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="text-3xl">🎯</div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Skills Tracked
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {stats.skills.toLocaleString()}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            {/* Connections */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="text-3xl">🔗</div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        Connections Made
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {stats.connections.toLocaleString()}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="px-4 py-6 sm:px-0">
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                Quick Actions
              </h3>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
                <a href="/data" className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors">
                  <div className="text-2xl mb-2">📊</div>
                  <h4 className="font-medium text-gray-900">Manage Data</h4>
                  <p className="text-sm text-gray-500">View, edit, and manage all your data records</p>
                </a>
                <a href="/graph" className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors">
                  <div className="text-2xl mb-2">🌐</div>
                  <h4 className="font-medium text-gray-900">Network Graph</h4>
                  <p className="text-sm text-gray-500">Visualize connections and relationships</p>
                </a>
                <a href="/matches" className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors">
                  <div className="text-2xl mb-2">⚙️</div>
                  <h4 className="font-medium text-gray-900">Find Matches</h4>
                  <p className="text-sm text-gray-500">Discover optimal job seeker and position matches</p>
                </a>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
