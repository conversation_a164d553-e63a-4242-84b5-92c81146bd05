'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { Database } from 'arangojs';
import dynamic from 'next/dynamic';

// Dynamically import ForceGraph2D to avoid SSR issues
const ForceGraph2D = dynamic(() => import('react-force-graph-2d'), { ssr: false });

interface JobSeeker {
  _id: string;
  _key: string;
  name: string;
  email: string;
  experience: string;
  location: string;
  title: string;
}

interface Skill {
  _id: string;
  name: string;
  category: string;
}

interface SkillConnection {
  skill: Skill;
  proficiency: string;
}

interface Match {
  position: {
    _id: string;
    title: string;
    company: string;
    level: string;
    location: string;
    salary: string;
  };
  company: {
    name: string;
    industry: string;
    size: string;
  };
  score: number;
}

export default function JobSeekerDetailPage() {
  const params = useParams();
  const [jobSeeker, setJobSeeker] = useState<JobSeeker | null>(null);
  const [skills, setSkills] = useState<SkillConnection[]>([]);
  const [matches, setMatches] = useState<Match[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (params.id) {
      fetchJobSeekerData(params.id as string);
    }
  }, [params.id]);

  const fetchJobSeekerData = async (seekerId: string) => {
    try {
      setIsLoading(true);

      const db = new Database({
        url: process.env.NEXT_PUBLIC_ARANGO_URL || 'https://f2c8a97499a8.arangodb.cloud:8529',
        auth: {
          username: process.env.NEXT_PUBLIC_ARANGO_USERNAME || 'root',
          password: process.env.NEXT_PUBLIC_ARANGO_PASSWORD || 'XMx2qSsHU8RWMX9VSxAx'
        },
        databaseName: process.env.NEXT_PUBLIC_ARANGO_DB_NAME || 'candid_connections'
      });

      // Get job seeker details
      const seekerDoc = await db.collection('jobSeekers').document(seekerId);
      setJobSeeker(seekerDoc);

      // Get skills
      const skillQuery = `
        FOR seeker IN jobSeekers
          FILTER seeker._key == @seekerId
          FOR skill, edge IN 1..1 OUTBOUND seeker seekerSkills
            RETURN {
              skill: skill,
              proficiency: edge.proficiency
            }
      `;
      
      const skillCursor = await db.query(skillQuery, { seekerId });
      const skillData = await skillCursor.all();
      setSkills(skillData);

      // Get matches
      const matchResponse = await fetch(`/api/matches?jobSeekerId=jobSeekers/${seekerId}&limit=5`);
      const matchData = await matchResponse.json();
      if (matchData.success) {
        setMatches(matchData.data);
      }

    } catch (err) {
      console.error('Error fetching job seeker data:', err);
      setError('Failed to load job seeker details');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading job seeker details...</p>
        </div>
      </div>
    );
  }

  if (error || !jobSeeker) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-600 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-red-800 mb-2">Job Seeker Not Found</h1>
          <p className="text-red-600 mb-4">{error}</p>
          <Link href="/data" className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
            Back to Data
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <Link href="/data" className="text-indigo-600 hover:text-indigo-800">
                ← Back to Data
              </Link>
              <h1 className="text-3xl font-bold text-gray-900">
                👤 {jobSeeker.name}
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <Link 
                href={`/matches?jobSeekerId=${jobSeeker._id}`}
                className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
              >
                View All Matches
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            
            {/* Job Seeker Details */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow p-6 mb-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Profile Details</h2>
                <div className="space-y-3">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Name</dt>
                    <dd className="text-sm text-gray-900">{jobSeeker.name}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Current Title</dt>
                    <dd className="text-sm text-gray-900">{jobSeeker.title}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Experience Level</dt>
                    <dd className="text-sm text-gray-900">{jobSeeker.experience}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Location</dt>
                    <dd className="text-sm text-gray-900">{jobSeeker.location}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Email</dt>
                    <dd className="text-sm text-gray-900">{jobSeeker.email}</dd>
                  </div>
                </div>
              </div>

              {/* Skills */}
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Skills & Proficiency</h2>
                <div className="space-y-3">
                  {skills.map((skillConn, index) => (
                    <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                      <div>
                        <span className="font-medium text-gray-900">{skillConn.skill.name}</span>
                        <span className="text-sm text-gray-500 ml-2">({skillConn.skill.category})</span>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        skillConn.proficiency === 'Expert' ? 'bg-green-100 text-green-800' :
                        skillConn.proficiency === 'Advanced' ? 'bg-blue-100 text-blue-800' :
                        skillConn.proficiency === 'Intermediate' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {skillConn.proficiency}
                      </span>
                    </div>
                  ))}
                  {skills.length === 0 && (
                    <p className="text-gray-500 text-center py-4">No skills recorded</p>
                  )}
                </div>
              </div>
            </div>

            {/* Matches and Recommendations */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow p-6 mb-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Top Job Matches</h2>
                <div className="space-y-4">
                  {matches.map((match, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h3 className="font-semibold text-gray-900">{match.position.title}</h3>
                          <p className="text-sm text-gray-600">{match.company.name}</p>
                          <p className="text-sm text-gray-500">{match.position.location}</p>
                        </div>
                        <div className="text-right">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            match.score >= 80 ? 'bg-green-100 text-green-800' :
                            match.score >= 60 ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {match.score}% Match
                          </span>
                          <p className="text-sm text-gray-500 mt-1">{match.position.salary}</p>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">
                          {match.company.industry} • {match.company.size} company
                        </span>
                        <Link 
                          href={`/positions/${match.position._id.split('/')[1]}`}
                          className="text-indigo-600 hover:text-indigo-800 text-sm font-medium"
                        >
                          View Details →
                        </Link>
                      </div>
                    </div>
                  ))}
                  {matches.length === 0 && (
                    <div className="text-center py-8">
                      <div className="text-4xl mb-2">🔍</div>
                      <p className="text-gray-500">No matches found yet</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Actions</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Link 
                    href={`/matches?jobSeekerId=${jobSeeker._id}`}
                    className="bg-purple-600 text-white px-4 py-3 rounded-lg hover:bg-purple-700 transition-colors text-center"
                  >
                    🎯 Find All Matches
                  </Link>
                  <Link 
                    href={`/graph?focus=${jobSeeker._id}`}
                    className="bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 transition-colors text-center"
                  >
                    🌐 View in Graph
                  </Link>
                  <button className="bg-indigo-600 text-white px-4 py-3 rounded-lg hover:bg-indigo-700 transition-colors">
                    ✏️ Edit Profile
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
