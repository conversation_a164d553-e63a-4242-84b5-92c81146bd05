'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface Match {
  jobSeeker: {
    _id: string;
    name: string;
    email: string;
    experience: string;
    location: string;
    title: string;
  };
  position: {
    _id: string;
    title: string;
    company: string;
    level: string;
    location: string;
    salary: string;
  };
  company: {
    _id: string;
    name: string;
    industry: string;
    size: string;
    employeeCount: number;
    location: string;
    description: string;
  };
  hiringAuthority: {
    _id: string;
    name: string;
    title: string;
    level: string;
    email: string;
    hiringScope: string;
    department: string;
  };
  score: number;
  skillMatches: Array<{
    skill: { name: string; category: string };
    seekerProficiency: string;
    requiredLevel: string;
    match: boolean;
    weight: number;
  }>;
  locationMatch: boolean;
  experienceMatch: boolean;
  companySize: string;
  hiringPath: string;
}

export default function MatchesPage() {
  const [matches, setMatches] = useState<Match[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedMatch, setSelectedMatch] = useState<Match | null>(null);

  useEffect(() => {
    fetchMatches();
  }, []);

  const fetchMatches = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/matches');
      const data = await response.json();
      
      if (data.success) {
        setMatches(data.data);
      } else {
        setError(data.error || 'Failed to fetch matches');
      }
    } catch (err) {
      console.error('Error fetching matches:', err);
      setError('Failed to load matches');
    } finally {
      setIsLoading(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 bg-green-100';
    if (score >= 60) return 'text-yellow-600 bg-yellow-100';
    if (score >= 40) return 'text-orange-600 bg-orange-100';
    return 'text-red-600 bg-red-100';
  };

  const getScoreLabel = (score: number) => {
    if (score >= 80) return 'Excellent Match';
    if (score >= 60) return 'Good Match';
    if (score >= 40) return 'Fair Match';
    return 'Poor Match';
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Finding perfect matches...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-600 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-red-800 mb-2">Error Loading Matches</h1>
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchMatches}
            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <Link href="/" className="text-indigo-600 hover:text-indigo-800">
                ← Back to Dashboard
              </Link>
              <h1 className="text-3xl font-bold text-gray-900">
                🎯 Job Matches
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                {matches.length} matches found
              </span>
              <button
                onClick={fetchMatches}
                className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
              >
                Refresh Matches
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Matches Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {matches.map((match, index) => (
              <div
                key={index}
                className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow cursor-pointer"
                onClick={() => setSelectedMatch(match)}
              >
                {/* Match Score Header */}
                <div className={`px-6 py-4 ${getScoreColor(match.score)}`}>
                  <div className="flex justify-between items-center">
                    <span className="text-2xl font-bold">{match.score}%</span>
                    <span className="text-sm font-medium">{getScoreLabel(match.score)}</span>
                  </div>
                </div>

                {/* Match Details */}
                <div className="p-6">
                  <div className="mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">
                      {match.jobSeeker.name}
                    </h3>
                    <p className="text-sm text-gray-600">{match.jobSeeker.title}</p>
                    <p className="text-sm text-gray-500">{match.jobSeeker.location}</p>
                  </div>

                  <div className="border-t border-gray-200 pt-4 mb-4">
                    <h4 className="font-medium text-gray-900 mb-1">{match.position.title}</h4>
                    <p className="text-sm text-gray-600">{match.company.name}</p>
                    <p className="text-sm text-gray-500">{match.position.location}</p>
                  </div>

                  {/* Quick Indicators */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {match.locationMatch && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        📍 Location Match
                      </span>
                    )}
                    {match.experienceMatch && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        💼 Experience Match
                      </span>
                    )}
                  </div>

                  {/* Hiring Authority */}
                  <div className="border-t border-gray-200 pt-4">
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Contact:</span> {match.hiringAuthority.name}
                    </p>
                    <p className="text-sm text-gray-500">{match.hiringAuthority.title}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {matches.length === 0 && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No matches found</h3>
              <p className="text-gray-600">Try adjusting your search criteria or add more data.</p>
            </div>
          )}
        </div>
      </main>

      {/* Match Detail Modal */}
      {selectedMatch && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <div className="flex justify-between items-start mb-6">
                <h3 className="text-2xl font-bold text-gray-900">
                  Match Details - {selectedMatch.score}% Match
                </h3>
                <button
                  onClick={() => setSelectedMatch(null)}
                  className="text-gray-400 hover:text-gray-600 text-2xl"
                >
                  ×
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Job Seeker Details */}
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-blue-900 mb-3">👤 Job Seeker</h4>
                  <div className="space-y-2 text-sm">
                    <p><span className="font-medium">Name:</span> {selectedMatch.jobSeeker.name}</p>
                    <p><span className="font-medium">Title:</span> {selectedMatch.jobSeeker.title}</p>
                    <p><span className="font-medium">Experience:</span> {selectedMatch.jobSeeker.experience}</p>
                    <p><span className="font-medium">Location:</span> {selectedMatch.jobSeeker.location}</p>
                    <p><span className="font-medium">Email:</span> {selectedMatch.jobSeeker.email}</p>
                  </div>
                </div>

                {/* Position Details */}
                <div className="bg-purple-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-purple-900 mb-3">💼 Position</h4>
                  <div className="space-y-2 text-sm">
                    <p><span className="font-medium">Title:</span> {selectedMatch.position.title}</p>
                    <p><span className="font-medium">Company:</span> {selectedMatch.company.name}</p>
                    <p><span className="font-medium">Level:</span> {selectedMatch.position.level}</p>
                    <p><span className="font-medium">Location:</span> {selectedMatch.position.location}</p>
                    <p><span className="font-medium">Salary:</span> {selectedMatch.position.salary}</p>
                  </div>
                </div>
              </div>

              {/* Skill Matches */}
              <div className="mt-6">
                <h4 className="font-semibold text-gray-900 mb-3">🎯 Skill Analysis</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {selectedMatch.skillMatches.map((skillMatch, index) => (
                    <div
                      key={index}
                      className={`p-3 rounded-lg border ${
                        skillMatch.match ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                      }`}
                    >
                      <div className="flex justify-between items-start mb-1">
                        <span className="font-medium text-sm">{skillMatch.skill.name}</span>
                        <span className={`text-xs px-2 py-1 rounded ${
                          skillMatch.match ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {skillMatch.match ? '✓' : '✗'}
                        </span>
                      </div>
                      <div className="text-xs text-gray-600">
                        <p>Has: {skillMatch.seekerProficiency}</p>
                        <p>Needs: {skillMatch.requiredLevel}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Hiring Path */}
              <div className="mt-6 bg-orange-50 p-4 rounded-lg">
                <h4 className="font-semibold text-orange-900 mb-3">👔 Hiring Process</h4>
                <div className="space-y-2 text-sm">
                  <p><span className="font-medium">Contact:</span> {selectedMatch.hiringAuthority.name}</p>
                  <p><span className="font-medium">Title:</span> {selectedMatch.hiringAuthority.title}</p>
                  <p><span className="font-medium">Email:</span> {selectedMatch.hiringAuthority.email}</p>
                  <p><span className="font-medium">Company Size:</span> {selectedMatch.company.size} ({selectedMatch.company.employeeCount} employees)</p>
                  <p><span className="font-medium">Hiring Path:</span> {selectedMatch.hiringPath}</p>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setSelectedMatch(null)}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                >
                  Close
                </button>
                <button className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                  Contact Hiring Authority
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
