'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { Database } from 'arangojs';

interface Company {
  _id: string;
  _key: string;
  name: string;
  industry: string;
  size: string;
  employeeCount: number;
  location: string;
  description: string;
}

interface HiringAuthority {
  _id: string;
  _key: string;
  name: string;
  title: string;
  level: string;
  email: string;
  hiringScope: string;
  department: string;
}

interface Position {
  _id: string;
  _key: string;
  title: string;
  level: string;
  location: string;
  salary: string;
}

interface Match {
  jobSeeker: {
    _id: string;
    name: string;
    title: string;
    experience: string;
    location: string;
  };
  position: Position;
  score: number;
}

export default function CompanyDetailPage() {
  const params = useParams();
  const [company, setCompany] = useState<Company | null>(null);
  const [hiringAuthorities, setHiringAuthorities] = useState<HiringAuthority[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [topMatches, setTopMatches] = useState<Match[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (params.id) {
      fetchCompanyData(params.id as string);
    }
  }, [params.id]);

  const fetchCompanyData = async (companyId: string) => {
    try {
      setIsLoading(true);

      const db = new Database({
        url: process.env.NEXT_PUBLIC_ARANGO_URL || 'https://f2c8a97499a8.arangodb.cloud:8529',
        auth: {
          username: process.env.NEXT_PUBLIC_ARANGO_USERNAME || 'root',
          password: process.env.NEXT_PUBLIC_ARANGO_PASSWORD || 'XMx2qSsHU8RWMX9VSxAx'
        },
        databaseName: process.env.NEXT_PUBLIC_ARANGO_DB_NAME || 'candid_connections'
      });

      // Get company details
      const companyDoc = await db.collection('companies').document(companyId);
      setCompany(companyDoc);

      // Get hiring authorities
      const hiringQuery = `
        FOR company IN companies
          FILTER company._key == @companyId
          FOR authority, edge IN 1..1 OUTBOUND company companyHiringAuthorities
            RETURN authority
      `;
      
      const hiringCursor = await db.query(hiringQuery, { companyId });
      const hiringData = await hiringCursor.all();
      setHiringAuthorities(hiringData);

      // Get positions
      const positionsQuery = `
        FOR position IN positions
          FILTER position.company == @companyId
          RETURN position
      `;
      
      const positionsCursor = await db.query(positionsQuery, { companyId });
      const positionsData = await positionsCursor.all();
      setPositions(positionsData);

      // Get top matches for all positions
      const allMatches: Match[] = [];
      for (const position of positionsData) {
        const matchResponse = await fetch(`/api/matches?positionId=${position._id}&limit=3`);
        const matchData = await matchResponse.json();
        if (matchData.success) {
          allMatches.push(...matchData.data);
        }
      }
      
      // Sort by score and take top 5
      const sortedMatches = allMatches.sort((a, b) => b.score - a.score).slice(0, 5);
      setTopMatches(sortedMatches);

    } catch (err) {
      console.error('Error fetching company data:', err);
      setError('Failed to load company details');
    } finally {
      setIsLoading(false);
    }
  };

  const getHiringPathDescription = (company: Company) => {
    if (company.employeeCount < 100) {
      return 'Small company - Direct access to leadership for hiring decisions';
    } else if (company.employeeCount < 500) {
      return 'Medium company - Department heads and CTO involved in hiring';
    } else if (company.employeeCount < 1000) {
      return 'Large company - HR managers work with department heads';
    } else {
      return 'Enterprise company - Structured HR department hierarchy';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading company details...</p>
        </div>
      </div>
    );
  }

  if (error || !company) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-600 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-red-800 mb-2">Company Not Found</h1>
          <p className="text-red-600 mb-4">{error}</p>
          <Link href="/data" className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
            Back to Data
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <Link href="/data" className="text-green-600 hover:text-green-800">
                ← Back to Data
              </Link>
              <h1 className="text-3xl font-bold text-gray-900">
                🏢 {company.name}
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                company.size === 'Large' ? 'bg-red-100 text-red-800' :
                company.size === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                'bg-green-100 text-green-800'
              }`}>
                {company.size} Company
              </span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            
            {/* Company Details */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow p-6 mb-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Company Overview</h2>
                <div className="space-y-3">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Industry</dt>
                    <dd className="text-sm text-gray-900">{company.industry}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Size</dt>
                    <dd className="text-sm text-gray-900">{company.size} ({company.employeeCount} employees)</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Location</dt>
                    <dd className="text-sm text-gray-900">{company.location}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Description</dt>
                    <dd className="text-sm text-gray-900">{company.description}</dd>
                  </div>
                </div>
              </div>

              {/* Hiring Process */}
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Hiring Process</h2>
                <div className="mb-4">
                  <p className="text-sm text-gray-600 mb-3">{getHiringPathDescription(company)}</p>
                </div>
                
                <h3 className="font-medium text-gray-900 mb-3">Hiring Authorities</h3>
                <div className="space-y-3">
                  {hiringAuthorities.map((authority, index) => (
                    <div key={index} className="p-3 bg-gray-50 rounded-lg">
                      <div className="flex justify-between items-start">
                        <div>
                          <span className="font-medium text-gray-900">{authority.name}</span>
                          <p className="text-sm text-gray-600">{authority.title}</p>
                          <p className="text-xs text-gray-500">{authority.department}</p>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          authority.level === 'CEO' || authority.level === 'Founder' ? 'bg-purple-100 text-purple-800' :
                          authority.level === 'VP' || authority.level === 'CTO' ? 'bg-blue-100 text-blue-800' :
                          authority.level === 'Director' ? 'bg-green-100 text-green-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {authority.level}
                        </span>
                      </div>
                      <p className="text-xs text-gray-500 mt-2">{authority.hiringScope}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Positions and Matches */}
            <div className="lg:col-span-2">
              {/* Open Positions */}
              <div className="bg-white rounded-lg shadow p-6 mb-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Open Positions ({positions.length})</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {positions.map((position, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <h3 className="font-semibold text-gray-900 mb-1">{position.title}</h3>
                      <p className="text-sm text-gray-600 mb-2">{position.level} Level</p>
                      <p className="text-sm text-gray-500 mb-2">{position.location}</p>
                      <p className="text-sm font-medium text-green-600 mb-3">{position.salary}</p>
                      <Link 
                        href={`/positions/${position._key}`}
                        className="text-indigo-600 hover:text-indigo-800 text-sm font-medium"
                      >
                        View Details →
                      </Link>
                    </div>
                  ))}
                  {positions.length === 0 && (
                    <div className="col-span-2 text-center py-8">
                      <div className="text-4xl mb-2">💼</div>
                      <p className="text-gray-500">No open positions</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Top Candidate Matches */}
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Top Candidate Matches</h2>
                <div className="space-y-4">
                  {topMatches.map((match, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h3 className="font-semibold text-gray-900">{match.jobSeeker.name}</h3>
                          <p className="text-sm text-gray-600">{match.jobSeeker.title}</p>
                          <p className="text-sm text-gray-500">{match.jobSeeker.location}</p>
                        </div>
                        <div className="text-right">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            match.score >= 80 ? 'bg-green-100 text-green-800' :
                            match.score >= 60 ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {match.score}% Match
                          </span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">
                          For: {match.position.title} • {match.jobSeeker.experience} level
                        </span>
                        <Link 
                          href={`/job-seekers/${match.jobSeeker._id.split('/')[1]}`}
                          className="text-indigo-600 hover:text-indigo-800 text-sm font-medium"
                        >
                          View Profile →
                        </Link>
                      </div>
                    </div>
                  ))}
                  {topMatches.length === 0 && (
                    <div className="text-center py-8">
                      <div className="text-4xl mb-2">🔍</div>
                      <p className="text-gray-500">No candidate matches found</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
