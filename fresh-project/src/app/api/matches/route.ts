import { NextRequest, NextResponse } from 'next/server';
import { JobMatchingService } from '@/lib/matching';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobSeekerId = searchParams.get('jobSeekerId');
    const positionId = searchParams.get('positionId');
    const limit = parseInt(searchParams.get('limit') || '10');

    const matchingService = new JobMatchingService();
    
    let matches;
    if (jobSeekerId) {
      matches = await matchingService.getJobSeekerMatches(jobSeekerId);
    } else if (positionId) {
      matches = await matchingService.getPositionMatches(positionId);
    } else {
      matches = await matchingService.getAllMatches();
    }

    // Limit results
    const limitedMatches = matches.slice(0, limit);

    return NextResponse.json({
      success: true,
      data: limitedMatches,
      total: matches.length,
      limited: limitedMatches.length
    });

  } catch (error) {
    console.error('Error in matches API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch matches',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
