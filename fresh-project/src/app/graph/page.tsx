'use client';

import { useState } from 'react';
import Link from 'next/link';
import GraphVisualization from '@/components/GraphVisualization';

export default function GraphPage() {
  const [viewMode, setViewMode] = useState<'2d' | '3d'>('2d');

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <Link href="/" className="text-indigo-600 hover:text-indigo-800">
                ← Back to Dashboard
              </Link>
              <h1 className="text-3xl font-bold text-gray-900">
                🌐 Network Graph Visualization
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('2d')}
                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                    viewMode === '2d'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  2D View
                </button>
                <button
                  onClick={() => setViewMode('3d')}
                  className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                    viewMode === '3d'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  3D View
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Instructions */}
          <div className="bg-white rounded-lg shadow mb-6 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-3">
              How to Use the Graph Visualization
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
              <div>
                <h3 className="font-medium text-gray-900 mb-2">Navigation:</h3>
                <ul className="space-y-1">
                  <li>• Click and drag to pan around</li>
                  <li>• Scroll to zoom in/out</li>
                  <li>• Click on nodes to view details</li>
                </ul>
              </div>
              <div>
                <h3 className="font-medium text-gray-900 mb-2">Node Types:</h3>
                <ul className="space-y-1">
                  <li>• <span className="text-blue-600">Blue:</span> Job Seekers</li>
                  <li>• <span className="text-green-600">Green:</span> Companies</li>
                  <li>• <span className="text-purple-600">Purple:</span> Positions</li>
                  <li>• <span className="text-amber-600">Amber:</span> Skills</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Graph Visualization */}
          <GraphVisualization viewMode={viewMode} />

          {/* Analytics Panel */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                🔍 Graph Analytics
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Nodes:</span>
                  <span className="font-medium">25</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Connections:</span>
                  <span className="font-medium">11</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Avg. Connections:</span>
                  <span className="font-medium">2.2</span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                🎯 Matching Insights
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Most Connected Skill:</span>
                  <span className="font-medium">JavaScript</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Top Company:</span>
                  <span className="font-medium">TechCorp</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Match Rate:</span>
                  <span className="font-medium">78%</span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                ⚡ Quick Actions
              </h3>
              <div className="space-y-2">
                <Link
                  href="/data"
                  className="block w-full text-center bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors text-sm"
                >
                  Manage Data
                </Link>
                <button className="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm">
                  Export Graph
                </button>
                <button className="w-full bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors text-sm">
                  Find Matches
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
