'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { Database } from 'arangojs';

interface Position {
  _id: string;
  _key: string;
  title: string;
  company: string;
  level: string;
  location: string;
  salary: string;
}

interface Company {
  _id: string;
  _key: string;
  name: string;
  industry: string;
  size: string;
  employeeCount: number;
  location: string;
  description: string;
}

interface Skill {
  _id: string;
  name: string;
  category: string;
}

interface SkillRequirement {
  skill: Skill;
  required: boolean;
  level: string;
}

interface HiringAuthority {
  _id: string;
  name: string;
  title: string;
  level: string;
  email: string;
  hiringScope: string;
  department: string;
}

interface Match {
  jobSeeker: {
    _id: string;
    name: string;
    title: string;
    experience: string;
    location: string;
    email: string;
  };
  score: number;
  skillMatches: Array<{
    skill: { name: string };
    seekerProficiency: string;
    requiredLevel: string;
    match: boolean;
  }>;
  locationMatch: boolean;
  experienceMatch: boolean;
}

export default function PositionDetailPage() {
  const params = useParams();
  const [position, setPosition] = useState<Position | null>(null);
  const [company, setCompany] = useState<Company | null>(null);
  const [skillRequirements, setSkillRequirements] = useState<SkillRequirement[]>([]);
  const [hiringAuthority, setHiringAuthority] = useState<HiringAuthority | null>(null);
  const [matches, setMatches] = useState<Match[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (params.id) {
      fetchPositionData(params.id as string);
    }
  }, [params.id]);

  const fetchPositionData = async (positionId: string) => {
    try {
      setIsLoading(true);

      const db = new Database({
        url: process.env.NEXT_PUBLIC_ARANGO_URL || 'https://f2c8a97499a8.arangodb.cloud:8529',
        auth: {
          username: process.env.NEXT_PUBLIC_ARANGO_USERNAME || 'root',
          password: process.env.NEXT_PUBLIC_ARANGO_PASSWORD || 'XMx2qSsHU8RWMX9VSxAx'
        },
        databaseName: process.env.NEXT_PUBLIC_ARANGO_DB_NAME || 'candid_connections'
      });

      // Get position details
      const positionDoc = await db.collection('positions').document(positionId);
      setPosition(positionDoc);

      // Get company details
      const companyDoc = await db.collection('companies').document(positionDoc.company);
      setCompany(companyDoc);

      // Get skill requirements
      const skillQuery = `
        FOR position IN positions
          FILTER position._key == @positionId
          FOR skill, edge IN 1..1 OUTBOUND position positionSkills
            RETURN {
              skill: skill,
              required: edge.required,
              level: edge.level
            }
      `;
      
      const skillCursor = await db.query(skillQuery, { positionId });
      const skillData = await skillCursor.all();
      setSkillRequirements(skillData);

      // Get hiring authority
      const hiringQuery = `
        FOR position IN positions
          FILTER position._key == @positionId
          FOR authority, edge IN 1..1 INBOUND position hiringAuthorityPositions
            RETURN authority
      `;
      
      const hiringCursor = await db.query(hiringQuery, { positionId });
      const hiringData = await hiringCursor.all();
      if (hiringData.length > 0) {
        setHiringAuthority(hiringData[0]);
      }

      // Get matches
      const matchResponse = await fetch(`/api/matches?positionId=positions/${positionId}&limit=10`);
      const matchData = await matchResponse.json();
      if (matchData.success) {
        setMatches(matchData.data);
      }

    } catch (err) {
      console.error('Error fetching position data:', err);
      setError('Failed to load position details');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading position details...</p>
        </div>
      </div>
    );
  }

  if (error || !position || !company) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-600 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-red-800 mb-2">Position Not Found</h1>
          <p className="text-red-600 mb-4">{error}</p>
          <Link href="/data" className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
            Back to Data
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <Link href="/data" className="text-purple-600 hover:text-purple-800">
                ← Back to Data
              </Link>
              <h1 className="text-3xl font-bold text-gray-900">
                💼 {position.title}
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <Link 
                href={`/companies/${company._key}`}
                className="text-green-600 hover:text-green-800 font-medium"
              >
                View Company →
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            
            {/* Position Details */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow p-6 mb-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Position Details</h2>
                <div className="space-y-3">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Title</dt>
                    <dd className="text-sm text-gray-900">{position.title}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Company</dt>
                    <dd className="text-sm text-gray-900">
                      <Link href={`/companies/${company._key}`} className="text-green-600 hover:text-green-800">
                        {company.name}
                      </Link>
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Level</dt>
                    <dd className="text-sm text-gray-900">{position.level}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Location</dt>
                    <dd className="text-sm text-gray-900">{position.location}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Salary</dt>
                    <dd className="text-sm text-gray-900 font-medium text-green-600">{position.salary}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Industry</dt>
                    <dd className="text-sm text-gray-900">{company.industry}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Company Size</dt>
                    <dd className="text-sm text-gray-900">{company.size} ({company.employeeCount} employees)</dd>
                  </div>
                </div>
              </div>

              {/* Skill Requirements */}
              <div className="bg-white rounded-lg shadow p-6 mb-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Required Skills</h2>
                <div className="space-y-3">
                  {skillRequirements.map((req, index) => (
                    <div key={index} className={`p-3 rounded-lg border ${
                      req.required ? 'bg-red-50 border-red-200' : 'bg-blue-50 border-blue-200'
                    }`}>
                      <div className="flex justify-between items-center">
                        <div>
                          <span className="font-medium text-gray-900">{req.skill.name}</span>
                          <span className="text-sm text-gray-500 ml-2">({req.skill.category})</span>
                        </div>
                        <div className="text-right">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            req.level === 'Expert' ? 'bg-green-100 text-green-800' :
                            req.level === 'Advanced' ? 'bg-blue-100 text-blue-800' :
                            req.level === 'Intermediate' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {req.level}
                          </span>
                        </div>
                      </div>
                      <div className="mt-1">
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          req.required ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'
                        }`}>
                          {req.required ? 'Required' : 'Preferred'}
                        </span>
                      </div>
                    </div>
                  ))}
                  {skillRequirements.length === 0 && (
                    <p className="text-gray-500 text-center py-4">No skill requirements specified</p>
                  )}
                </div>
              </div>

              {/* Hiring Authority */}
              {hiringAuthority && (
                <div className="bg-white rounded-lg shadow p-6">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">Hiring Authority</h2>
                  <div className="space-y-3">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Name</dt>
                      <dd className="text-sm text-gray-900">{hiringAuthority.name}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Title</dt>
                      <dd className="text-sm text-gray-900">{hiringAuthority.title}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Department</dt>
                      <dd className="text-sm text-gray-900">{hiringAuthority.department}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Email</dt>
                      <dd className="text-sm text-gray-900">{hiringAuthority.email}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Hiring Scope</dt>
                      <dd className="text-sm text-gray-900">{hiringAuthority.hiringScope}</dd>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Candidate Matches */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Candidate Matches ({matches.length})
                </h2>
                <div className="space-y-4">
                  {matches.map((match, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <div className="flex justify-between items-start mb-3">
                        <div>
                          <h3 className="font-semibold text-gray-900">{match.jobSeeker.name}</h3>
                          <p className="text-sm text-gray-600">{match.jobSeeker.title}</p>
                          <p className="text-sm text-gray-500">{match.jobSeeker.location}</p>
                        </div>
                        <div className="text-right">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            match.score >= 80 ? 'bg-green-100 text-green-800' :
                            match.score >= 60 ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {match.score}% Match
                          </span>
                        </div>
                      </div>

                      {/* Match Indicators */}
                      <div className="flex flex-wrap gap-2 mb-3">
                        {match.locationMatch && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            📍 Location Match
                          </span>
                        )}
                        {match.experienceMatch && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            💼 Experience Match
                          </span>
                        )}
                      </div>

                      {/* Skill Matches Preview */}
                      <div className="mb-3">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Skill Matches:</h4>
                        <div className="flex flex-wrap gap-1">
                          {match.skillMatches.slice(0, 5).map((skillMatch, skillIndex) => (
                            <span
                              key={skillIndex}
                              className={`text-xs px-2 py-1 rounded-full ${
                                skillMatch.match ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                              }`}
                            >
                              {skillMatch.skill.name} {skillMatch.match ? '✓' : '✗'}
                            </span>
                          ))}
                          {match.skillMatches.length > 5 && (
                            <span className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-600">
                              +{match.skillMatches.length - 5} more
                            </span>
                          )}
                        </div>
                      </div>

                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">
                          {match.jobSeeker.experience} level • {match.jobSeeker.email}
                        </span>
                        <Link 
                          href={`/job-seekers/${match.jobSeeker._id.split('/')[1]}`}
                          className="text-indigo-600 hover:text-indigo-800 text-sm font-medium"
                        >
                          View Profile →
                        </Link>
                      </div>
                    </div>
                  ))}
                  {matches.length === 0 && (
                    <div className="text-center py-8">
                      <div className="text-4xl mb-2">🔍</div>
                      <p className="text-gray-500">No candidate matches found</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
