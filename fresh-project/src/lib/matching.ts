import { Database } from 'arangojs';

interface JobSeeker {
  _id: string;
  _key: string;
  name: string;
  email: string;
  experience: string;
  location: string;
  title: string;
}

interface Position {
  _id: string;
  _key: string;
  title: string;
  company: string;
  level: string;
  location: string;
  salary: string;
}

interface Company {
  _id: string;
  _key: string;
  name: string;
  industry: string;
  size: string;
  employeeCount: number;
  location: string;
  description: string;
}

interface HiringAuthority {
  _id: string;
  _key: string;
  name: string;
  title: string;
  level: string;
  company: string;
  email: string;
  hiringScope: string;
  department: string;
}

interface Skill {
  _id: string;
  _key: string;
  name: string;
  category: string;
}

interface SkillConnection {
  _from: string;
  _to: string;
  proficiency: string;
}

interface PositionSkillRequirement {
  _from: string;
  _to: string;
  required: boolean;
  level: string;
}

interface Match {
  jobSeeker: JobSeeker;
  position: Position;
  company: Company;
  hiringAuthority: HiringAuthority;
  score: number;
  skillMatches: Array<{
    skill: Skill;
    seekerProficiency: string;
    requiredLevel: string;
    match: boolean;
    weight: number;
  }>;
  locationMatch: boolean;
  experienceMatch: boolean;
  companySize: string;
  hiringPath: string;
}

export class JobMatchingService {
  private db: Database;

  constructor() {
    this.db = new Database({
      url: process.env.NEXT_PUBLIC_ARANGO_URL || 'https://f2c8a97499a8.arangodb.cloud:8529',
      auth: {
        username: process.env.NEXT_PUBLIC_ARANGO_USERNAME || 'root',
        password: process.env.NEXT_PUBLIC_ARANGO_PASSWORD || 'XMx2qSsHU8RWMX9VSxAx'
      },
      databaseName: process.env.NEXT_PUBLIC_ARANGO_DB_NAME || 'candid_connections'
    });
  }

  private getSkillLevelScore(proficiency: string): number {
    const levels = {
      'Beginner': 1,
      'Intermediate': 2,
      'Advanced': 3,
      'Expert': 4
    };
    return levels[proficiency as keyof typeof levels] || 0;
  }

  private getExperienceLevelScore(experience: string): number {
    const levels = {
      'Junior': 1,
      'Mid-level': 2,
      'Senior': 3,
      'Lead': 4,
      'Principal': 5
    };
    return levels[experience as keyof typeof levels] || 0;
  }

  private determineHiringPath(company: Company): string {
    if (company.employeeCount < 100) {
      return 'Direct to leadership (CEO/Founder)';
    } else if (company.employeeCount < 500) {
      return 'Department head or CTO';
    } else if (company.employeeCount < 1000) {
      return 'HR Manager + Department head';
    } else {
      return 'HR Department hierarchy';
    }
  }

  async findMatches(jobSeekerId?: string, positionId?: string): Promise<Match[]> {
    try {
      // Get all relevant data
      const [jobSeekers, positions, companies, hiringAuthorities, skills] = await Promise.all([
        this.db.collection('jobSeekers').all().then(cursor => cursor.all()),
        this.db.collection('positions').all().then(cursor => cursor.all()),
        this.db.collection('companies').all().then(cursor => cursor.all()),
        this.db.collection('hiringAuthorities').all().then(cursor => cursor.all()),
        this.db.collection('skills').all().then(cursor => cursor.all())
      ]);

      const [seekerSkills, positionSkills, companyHiring] = await Promise.all([
        this.db.collection('seekerSkills').all().then(cursor => cursor.all()),
        this.db.collection('positionSkills').all().then(cursor => cursor.all()),
        this.db.collection('companyHiringAuthorities').all().then(cursor => cursor.all())
      ]);

      // Create lookup maps
      const companyMap = new Map(companies.map(c => [c._key, c]));
      const skillMap = new Map(skills.map(s => [s._id, s]));
      const hiringMap = new Map(hiringAuthorities.map(h => [h._key, h]));

      // Group skills by entity
      const seekerSkillMap = new Map<string, SkillConnection[]>();
      seekerSkills.forEach(ss => {
        const seekerId = ss._from;
        if (!seekerSkillMap.has(seekerId)) {
          seekerSkillMap.set(seekerId, []);
        }
        seekerSkillMap.get(seekerId)!.push(ss);
      });

      const positionSkillMap = new Map<string, PositionSkillRequirement[]>();
      positionSkills.forEach(ps => {
        const positionId = ps._from;
        if (!positionSkillMap.has(positionId)) {
          positionSkillMap.set(positionId, []);
        }
        positionSkillMap.get(positionId)!.push(ps);
      });

      // Group hiring authorities by company
      const companyHiringMap = new Map<string, HiringAuthority[]>();
      companyHiring.forEach(ch => {
        const companyId = ch._from.split('/')[1];
        const authorityId = ch._to.split('/')[1];
        const authority = hiringMap.get(authorityId);
        if (authority) {
          if (!companyHiringMap.has(companyId)) {
            companyHiringMap.set(companyId, []);
          }
          companyHiringMap.get(companyId)!.push(authority);
        }
      });

      const matches: Match[] = [];

      // Filter data if specific IDs provided
      const targetJobSeekers = jobSeekerId ? jobSeekers.filter(js => js._id === jobSeekerId) : jobSeekers;
      const targetPositions = positionId ? positions.filter(p => p._id === positionId) : positions;

      // Calculate matches
      for (const jobSeeker of targetJobSeekers) {
        for (const position of targetPositions) {
          const company = companyMap.get(position.company);
          if (!company) continue;

          const hiringAuthorities = companyHiringMap.get(position.company) || [];
          const primaryHiringAuthority = hiringAuthorities[0]; // Simplified - take first
          if (!primaryHiringAuthority) continue;

          const seekerSkillConnections = seekerSkillMap.get(jobSeeker._id) || [];
          const positionSkillRequirements = positionSkillMap.get(position._id) || [];

          // Calculate skill matches
          const skillMatches = [];
          let totalScore = 0;
          let maxPossibleScore = 0;

          for (const requirement of positionSkillRequirements) {
            const skill = skillMap.get(requirement._to);
            if (!skill) continue;

            const seekerSkill = seekerSkillConnections.find(ss => ss._to === requirement._to);
            const weight = requirement.required ? 3 : 1;
            maxPossibleScore += weight;

            if (seekerSkill) {
              const seekerLevel = this.getSkillLevelScore(seekerSkill.proficiency);
              const requiredLevel = this.getSkillLevelScore(requirement.level);
              const match = seekerLevel >= requiredLevel;
              
              if (match) {
                totalScore += weight;
              }

              skillMatches.push({
                skill,
                seekerProficiency: seekerSkill.proficiency,
                requiredLevel: requirement.level,
                match,
                weight
              });
            } else {
              skillMatches.push({
                skill,
                seekerProficiency: 'None',
                requiredLevel: requirement.level,
                match: false,
                weight
              });
            }
          }

          // Calculate other factors
          const locationMatch = jobSeeker.location === position.location;
          const seekerExpLevel = this.getExperienceLevelScore(jobSeeker.experience);
          const positionExpLevel = this.getExperienceLevelScore(position.level);
          const experienceMatch = seekerExpLevel >= positionExpLevel;

          // Calculate final score (0-100)
          let finalScore = maxPossibleScore > 0 ? (totalScore / maxPossibleScore) * 70 : 0;
          if (locationMatch) finalScore += 15;
          if (experienceMatch) finalScore += 15;

          matches.push({
            jobSeeker,
            position,
            company,
            hiringAuthority: primaryHiringAuthority,
            score: Math.round(finalScore),
            skillMatches,
            locationMatch,
            experienceMatch,
            companySize: company.size,
            hiringPath: this.determineHiringPath(company)
          });
        }
      }

      // Sort by score descending
      return matches.sort((a, b) => b.score - a.score);

    } catch (error) {
      console.error('Error finding matches:', error);
      throw error;
    }
  }

  async getJobSeekerMatches(jobSeekerId: string): Promise<Match[]> {
    return this.findMatches(jobSeekerId);
  }

  async getPositionMatches(positionId: string): Promise<Match[]> {
    return this.findMatches(undefined, positionId);
  }

  async getAllMatches(): Promise<Match[]> {
    return this.findMatches();
  }
}
