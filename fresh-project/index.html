<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤝 Candid Connections - Job Matching Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
    <script src="https://unpkg.com/three@0.158.0/examples/js/controls/OrbitControls.js"></script>
    <script src="https://unpkg.com/3d-force-graph@1.72.3/dist/3d-force-graph.min.js"></script>
    <style>
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .modal-overlay { background: rgba(0, 0, 0, 0.5); }
        .graph-container { position: relative; }
        .graph-controls { position: absolute; top: 10px; right: 10px; z-index: 100; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="gradient-bg text-white shadow-lg">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <h1 class="text-3xl font-bold">🤝 Candid Connections</h1>
                    <p class="text-lg">Intelligent Job Matching Platform</p>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <div class="px-4 py-6 sm:px-0">
                <!-- Stats Grid -->
                <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-6 mb-8" id="stats-grid">
                    <!-- Stats will be loaded here -->
                </div>

                <!-- Navigation Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-6 mb-8">
                    <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadJobSeekers()">
                        <div class="text-3xl mb-3">👤</div>
                        <h3 class="text-lg font-semibold text-gray-900">Job Seekers</h3>
                        <p class="text-sm text-gray-600">View all job seekers and their profiles</p>
                    </div>
                    <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadCompanies()">
                        <div class="text-3xl mb-3">🏢</div>
                        <h3 class="text-lg font-semibold text-gray-900">Companies</h3>
                        <p class="text-sm text-gray-600">Browse companies and open positions</p>
                    </div>
                    <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadPositions()">
                        <div class="text-3xl mb-3">💼</div>
                        <h3 class="text-lg font-semibold text-gray-900">Positions</h3>
                        <p class="text-sm text-gray-600">View all available job positions</p>
                    </div>
                    <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadMatches()">
                        <div class="text-3xl mb-3">🎯</div>
                        <h3 class="text-lg font-semibold text-gray-900">Job Matches</h3>
                        <p class="text-sm text-gray-600">See intelligent job matching results</p>
                    </div>
                    <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadHiringAuthorities()">
                        <div class="text-3xl mb-3">👔</div>
                        <h3 class="text-lg font-semibold text-gray-900">Hiring Authorities</h3>
                        <p class="text-sm text-gray-600">View hiring managers and their best matches</p>
                    </div>
                    <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadSkills()">
                        <div class="text-3xl mb-3">🎯</div>
                        <h3 class="text-lg font-semibold text-gray-900">Skills</h3>
                        <p class="text-sm text-gray-600">View skills demand and availability</p>
                    </div>
                    <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadGraphVisualization()">
                        <div class="text-3xl mb-3">🌐</div>
                        <h3 class="text-lg font-semibold text-gray-900">Graph Visualization</h3>
                        <p class="text-sm text-gray-600">Interactive 2D/3D network graph</p>
                    </div>
                </div>

                <!-- Content Area -->
                <div class="bg-white rounded-lg shadow" id="content-area">
                    <div class="p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Welcome to Candid Connections</h2>
                        <p class="text-gray-600 mb-4">
                            This intelligent job matching platform uses advanced graph database technology to connect
                            job seekers with the perfect opportunities through relationship mapping and smart algorithms.
                        </p>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h3 class="font-semibold text-blue-900 mb-2">🚀 Platform Features:</h3>
                            <ul class="text-blue-800 space-y-1">
                                <li>• Intelligent job matching based on skills, experience, and location</li>
                                <li>• Company hierarchy modeling for appropriate hiring authority routing</li>
                                <li>• Graph database relationships for complex data modeling</li>
                                <li>• Real-time statistics and comprehensive data management</li>
                                <li>• Enhanced 3D visualization with node labels and camera animations</li>
                                <li>• Comprehensive CRUD operations with real-time updates</li>
                                <li>• Skills demand and availability analysis</li>
                                <li>• Company hierarchy with drilling capabilities</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="app.js"></script>
    <script>
        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
        });
    </script>
</body>
</html>
