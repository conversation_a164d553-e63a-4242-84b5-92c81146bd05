<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤝 Candid Connections - Job Matching Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
    <script src="https://unpkg.com/three@0.158.0/examples/js/controls/OrbitControls.js"></script>
    <script src="https://unpkg.com/3d-force-graph@1.72.3/dist/3d-force-graph.min.js"></script>
    <style>
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .modal-overlay { background: rgba(0, 0, 0, 0.5); }
        .graph-container { position: relative; }
        .graph-controls { position: absolute; top: 10px; right: 10px; z-index: 100; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="gradient-bg text-white shadow-lg">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <h1 class="text-3xl font-bold">🤝 Candid Connections</h1>
                    <p class="text-lg">Intelligent Job Matching Platform</p>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <div class="px-4 py-6 sm:px-0">
                <!-- Navigation Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-8 gap-6 mb-8">
                    <!-- 1. Job Matches - Priority #1 -->
                    <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadMatches()">
                        <div class="flex justify-between items-start mb-3">
                            <div class="text-3xl">🎯</div>
                            <span id="matches-count" class="text-lg font-bold text-emerald-600">-</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Job Matches</h3>
                        <p class="text-sm text-gray-600 mb-3">See intelligent job matching results</p>
                        <div class="flex space-x-1">
                            <button onclick="loadGraphVisualization('matches', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">2D</button>
                            <button onclick="loadGraphVisualization('matches', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200">3D</button>
                        </div>
                    </div>

                    <!-- 2. Job Seekers -->
                    <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadJobSeekers()">
                        <div class="flex justify-between items-start mb-3">
                            <div class="text-3xl">👤</div>
                            <span id="job-seekers-count" class="text-lg font-bold text-blue-600">-</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Job Seekers</h3>
                        <p class="text-sm text-gray-600 mb-3">View all job seekers and their profiles</p>
                        <div class="flex space-x-1">
                            <button onclick="loadGraphVisualization('jobSeekers', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">2D</button>
                            <button onclick="loadGraphVisualization('jobSeekers', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200">3D</button>
                        </div>
                    </div>

                    <!-- 3. Companies -->
                    <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadCompanies()">
                        <div class="flex justify-between items-start mb-3">
                            <div class="text-3xl">🏢</div>
                            <span id="companies-count" class="text-lg font-bold text-teal-600">-</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Companies</h3>
                        <p class="text-sm text-gray-600 mb-3">Browse companies and open positions</p>
                        <div class="flex space-x-1">
                            <button onclick="loadGraphVisualization('companies', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">2D</button>
                            <button onclick="loadGraphVisualization('companies', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200">3D</button>
                        </div>
                    </div>

                    <!-- 4. Hiring Authorities -->
                    <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadHiringAuthorities()">
                        <div class="flex justify-between items-start mb-3">
                            <div class="text-3xl">👔</div>
                            <span id="hiring-authorities-count" class="text-lg font-bold text-orange-600">-</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Hiring Authorities</h3>
                        <p class="text-sm text-gray-600 mb-3">View hiring managers and their best matches</p>
                        <div class="flex space-x-1">
                            <button onclick="loadGraphVisualization('hiringAuthorities', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">2D</button>
                            <button onclick="loadGraphVisualization('hiringAuthorities', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200">3D</button>
                        </div>
                    </div>

                    <!-- 5. Positions -->
                    <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadPositions()">
                        <div class="flex justify-between items-start mb-3">
                            <div class="text-3xl">💼</div>
                            <span id="positions-count" class="text-lg font-bold text-violet-600">-</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Positions</h3>
                        <p class="text-sm text-gray-600 mb-3">View all available job positions</p>
                        <div class="flex space-x-1">
                            <button onclick="loadGraphVisualization('positions', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">2D</button>
                            <button onclick="loadGraphVisualization('positions', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200">3D</button>
                        </div>
                    </div>

                    <!-- 6. Skills -->
                    <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadSkills()">
                        <div class="flex justify-between items-start mb-3">
                            <div class="text-3xl">⚡</div>
                            <span id="skills-count" class="text-lg font-bold text-amber-600">-</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Skills</h3>
                        <p class="text-sm text-gray-600 mb-3">View skills demand and availability</p>
                        <div class="flex space-x-1">
                            <button onclick="loadGraphVisualization('skills', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">2D</button>
                            <button onclick="loadGraphVisualization('skills', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200">3D</button>
                        </div>
                    </div>

                    <!-- 7. Connections -->
                    <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadConnections()">
                        <div class="flex justify-between items-start mb-3">
                            <div class="text-3xl">🔗</div>
                            <span id="connections-count" class="text-lg font-bold text-indigo-600">-</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Connections</h3>
                        <p class="text-sm text-gray-600 mb-3">Network relationships and analysis</p>
                        <div class="flex space-x-1">
                            <button onclick="loadGraphVisualization('connections', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">2D</button>
                            <button onclick="loadGraphVisualization('connections', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200">3D</button>
                        </div>
                    </div>

                    <!-- 8. Graph Visualization - Global "All in One" -->
                    <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadGraphVisualization()">
                        <div class="flex justify-between items-start mb-3">
                            <div class="text-3xl">🌐</div>
                            <div class="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full"></div>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Graph Visualization</h3>
                        <p class="text-sm text-gray-600 mb-3">Global graph database interface with filtering</p>
                        <div class="flex space-x-1">
                            <button onclick="loadGraphVisualization('overview', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200">2D</button>
                            <button onclick="loadGraphVisualization('overview', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200">3D</button>
                        </div>
                    </div>
                </div>

                <!-- Content Area -->
                <div class="bg-white rounded-lg shadow" id="content-area">
                    <div class="p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Welcome to Candid Connections</h2>
                        <p class="text-gray-600 mb-4">
                            This intelligent job matching platform uses advanced graph database technology to connect
                            job seekers with the perfect opportunities through relationship mapping and smart algorithms.
                        </p>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h3 class="font-semibold text-blue-900 mb-2">🚀 Platform Features:</h3>
                            <ul class="text-blue-800 space-y-1">
                                <li>• Intelligent job matching based on skills, experience, and location</li>
                                <li>• Company hierarchy modeling for appropriate hiring authority routing</li>
                                <li>• Graph database relationships for complex data modeling</li>
                                <li>• Real-time statistics and comprehensive data management</li>
                                <li>• Enhanced 3D visualization with node labels and camera animations</li>
                                <li>• Comprehensive CRUD operations with real-time updates</li>
                                <li>• Skills demand and availability analysis</li>
                                <li>• Company hierarchy with drilling capabilities</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="app.js"></script>
    <script>
        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
        });
    </script>
</body>
</html>
