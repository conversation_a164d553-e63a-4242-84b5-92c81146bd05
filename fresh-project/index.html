<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤝 Candid Connections - Job Matching Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
    <script src="https://unpkg.com/three@0.158.0/examples/js/controls/OrbitControls.js"></script>
    <script src="https://unpkg.com/3d-force-graph@1.72.3/dist/3d-force-graph.min.js"></script>
    <style>
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .modal-overlay { background: rgba(0, 0, 0, 0.5); }
        .graph-container { position: relative; }
        .graph-controls { position: absolute; top: 10px; right: 10px; z-index: 100; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="gradient-bg text-white shadow-lg">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <h1 class="text-3xl font-bold">🤝 Candid Connections</h1>
                    <p class="text-lg">Intelligent Job Matching Platform</p>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <div class="px-4 py-6 sm:px-0">
                <!-- Navigation Cards - Optimized Layout -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 mb-8">
                    <!-- 1. Job Matches - Priority #1 -->
                    <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadMatches()">
                        <div class="flex justify-between items-start mb-3">
                            <div class="text-3xl">🎯</div>
                            <span id="matches-count" class="text-lg font-bold text-emerald-600">-</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Job Matches</h3>
                        <p class="text-sm text-gray-600 mb-3">See intelligent job matching results</p>
                        <div class="border-t pt-3">
                            <div class="text-xs font-medium text-gray-600 mb-2">Visualization</div>
                            <div class="flex space-x-1">
                                <button onclick="event.stopPropagation(); loadGraphVisualization('matches', '2d')" class="px-3 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200" title="View job matches in 2D graph visualization">2D Graph</button>
                                <button onclick="event.stopPropagation(); loadGraphVisualization('matches', '3d')" class="px-3 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200" title="View job matches in 3D graph visualization">3D Graph</button>
                            </div>
                        </div>
                    </div>

                    <!-- 2. Job Seekers -->
                    <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadJobSeekers()">
                        <div class="flex justify-between items-start mb-3">
                            <div class="text-3xl">👤</div>
                            <span id="job-seekers-count" class="text-lg font-bold text-blue-600">-</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Job Seekers</h3>
                        <p class="text-sm text-gray-600 mb-3">View all job seekers and their profiles</p>
                        <div class="border-t pt-3">
                            <div class="text-xs font-medium text-gray-600 mb-2">Visualization</div>
                            <div class="flex space-x-1">
                                <button onclick="event.stopPropagation(); loadGraphVisualization('jobSeekers', '2d')" class="px-3 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200" title="View job seekers in 2D graph visualization">2D Graph</button>
                                <button onclick="event.stopPropagation(); loadGraphVisualization('jobSeekers', '3d')" class="px-3 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200" title="View job seekers in 3D graph visualization">3D Graph</button>
                            </div>
                        </div>
                    </div>

                    <!-- 3. Companies (with embedded hierarchical navigation) -->
                    <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadCompanies()">
                        <div class="flex justify-between items-start mb-3">
                            <div class="text-3xl">🏢</div>
                            <span id="companies-count" class="text-lg font-bold text-teal-600">-</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Companies</h3>
                        <p class="text-sm text-gray-600 mb-3">Browse companies and organizational structure</p>

                        <!-- Embedded Hierarchical Navigation -->
                        <div class="mb-3 space-y-2">
                            <!-- Hiring Authorities Sub-Navigation -->
                            <div class="flex items-center justify-between p-2 bg-orange-50 rounded border border-orange-200">
                                <div class="flex items-center">
                                    <span class="w-2 h-2 bg-orange-400 rounded-full mr-2"></span>
                                    <span class="text-sm text-orange-800 font-medium">👔 Hiring Authorities</span>
                                    <span id="hiring-authorities-count" class="ml-2 text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded">-</span>
                                </div>
                                <div class="flex space-x-1">
                                    <button onclick="event.stopPropagation(); loadHiringAuthorities()" class="px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs hover:bg-orange-200" title="View hiring authorities list">List</button>
                                    <button onclick="event.stopPropagation(); loadGraphVisualization('hiringAuthorities', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200" title="View hiring authorities in 2D">2D</button>
                                    <button onclick="event.stopPropagation(); loadGraphVisualization('hiringAuthorities', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200" title="View hiring authorities in 3D">3D</button>
                                </div>
                            </div>

                            <!-- Positions Sub-Navigation -->
                            <div class="flex items-center justify-between p-2 bg-violet-50 rounded border border-violet-200 ml-4">
                                <div class="flex items-center">
                                    <span class="w-2 h-2 bg-violet-400 rounded-full mr-2"></span>
                                    <span class="text-sm text-violet-800 font-medium">💼 Open Positions</span>
                                    <span id="positions-count" class="ml-2 text-xs text-violet-600 bg-violet-100 px-2 py-1 rounded">-</span>
                                </div>
                                <div class="flex space-x-1">
                                    <button onclick="event.stopPropagation(); loadPositions()" class="px-2 py-1 bg-violet-100 text-violet-800 rounded text-xs hover:bg-violet-200" title="View positions list">List</button>
                                    <button onclick="event.stopPropagation(); loadGraphVisualization('positions', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200" title="View positions in 2D">2D</button>
                                    <button onclick="event.stopPropagation(); loadGraphVisualization('positions', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200" title="View positions in 3D">3D</button>
                                </div>
                            </div>
                        </div>

                        <!-- Company-Level Visualization -->
                        <div class="border-t pt-3">
                            <div class="text-xs font-medium text-gray-600 mb-2">Visualization</div>
                            <div class="flex space-x-1">
                                <button onclick="event.stopPropagation(); loadGraphVisualization('companies', '2d')" class="px-3 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200" title="View company hierarchy in 2D graph">2D Graph</button>
                                <button onclick="event.stopPropagation(); loadGraphVisualization('companies', '3d')" class="px-3 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200" title="View company hierarchy in 3D graph">3D Graph</button>
                            </div>
                        </div>
                    </div>

                    <!-- 4. Skills -->
                    <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadSkills()">
                        <div class="flex justify-between items-start mb-3">
                            <div class="text-3xl">⚡</div>
                            <span id="skills-count" class="text-lg font-bold text-amber-600">-</span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Skills</h3>
                        <p class="text-sm text-gray-600 mb-3">View skills demand and availability</p>
                        <div class="border-t pt-3">
                            <div class="text-xs font-medium text-gray-600 mb-2">Visualization</div>
                            <div class="flex space-x-1">
                                <button onclick="event.stopPropagation(); loadGraphVisualization('skills', '2d')" class="px-3 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200" title="View skills in 2D graph visualization">2D Graph</button>
                                <button onclick="event.stopPropagation(); loadGraphVisualization('skills', '3d')" class="px-3 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200" title="View skills in 3D graph visualization">3D Graph</button>
                            </div>
                        </div>
                    </div>

                    <!-- 5. Graph Visualization - Global "All in One" -->
                    <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="loadGraphVisualization()">
                        <div class="flex justify-between items-start mb-3">
                            <div class="text-3xl">🌐</div>
                            <div class="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full"></div>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Graph Visualization</h3>
                        <p class="text-sm text-gray-600 mb-3">Global graph database interface with filtering</p>

                        <!-- Embedded Connections Navigation -->
                        <div class="mb-3">
                            <div class="flex items-center justify-between p-2 bg-indigo-50 rounded border border-indigo-200">
                                <div class="flex items-center">
                                    <span class="w-2 h-2 bg-indigo-400 rounded-full mr-2"></span>
                                    <span class="text-sm text-indigo-800 font-medium">🔗 Connections</span>
                                    <span id="connections-count" class="ml-2 text-xs text-indigo-600 bg-indigo-100 px-2 py-1 rounded">-</span>
                                </div>
                                <div class="flex space-x-1">
                                    <button onclick="event.stopPropagation(); loadConnections()" class="px-2 py-1 bg-indigo-100 text-indigo-800 rounded text-xs hover:bg-indigo-200" title="View connections analysis">List</button>
                                    <button onclick="event.stopPropagation(); loadGraphVisualization('connections', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200" title="View connections in 2D">2D</button>
                                    <button onclick="event.stopPropagation(); loadGraphVisualization('connections', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200" title="View connections in 3D">3D</button>
                                </div>
                            </div>
                        </div>

                        <div class="border-t pt-3">
                            <div class="text-xs font-medium text-gray-600 mb-2">Visualization</div>
                            <div class="flex space-x-1">
                                <button onclick="event.stopPropagation(); loadGraphVisualization('overview', '2d')" class="px-3 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200" title="View complete graph in 2D visualization">2D Graph</button>
                                <button onclick="event.stopPropagation(); loadGraphVisualization('overview', '3d')" class="px-3 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200" title="View complete graph in 3D visualization">3D Graph</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Content Area -->
                <div class="bg-white rounded-lg shadow" id="content-area">
                    <div class="p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Welcome to Candid Connections</h2>
                        <p class="text-gray-600 mb-4">
                            This intelligent job matching platform uses advanced graph database technology to connect
                            job seekers with the perfect opportunities through relationship mapping and smart algorithms.
                        </p>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h3 class="font-semibold text-blue-900 mb-2">🚀 Platform Features:</h3>
                            <ul class="text-blue-800 space-y-1">
                                <li>• Intelligent job matching based on skills, experience, and location</li>
                                <li>• Company hierarchy modeling for appropriate hiring authority routing</li>
                                <li>• Graph database relationships for complex data modeling</li>
                                <li>• Real-time statistics and comprehensive data management</li>
                                <li>• Enhanced 3D visualization with node labels and camera animations</li>
                                <li>• Comprehensive CRUD operations with real-time updates</li>
                                <li>• Skills demand and availability analysis</li>
                                <li>• Company hierarchy with drilling capabilities</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 3D Visualization Dependencies -->
    <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
    <script src="https://unpkg.com/3d-force-graph@1.73.0/dist/3d-force-graph.min.js"></script>
    <script src="https://unpkg.com/d3-force@3.0.0/dist/d3-force.min.js"></script>

    <script src="app.js"></script>
    <script>
        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
        });
    </script>
</body>
</html>
