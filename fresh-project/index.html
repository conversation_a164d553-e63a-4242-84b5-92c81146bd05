<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤝 Candid Connections - Job Matching Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
    <script src="https://unpkg.com/three@0.158.0/examples/js/controls/OrbitControls.js"></script>
    <script src="https://unpkg.com/3d-force-graph@1.72.3/dist/3d-force-graph.min.js"></script>
    <style>
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .modal-overlay { background: rgba(0, 0, 0, 0.5); }
        .graph-container { position: relative; }
        .graph-controls { position: absolute; top: 10px; right: 10px; z-index: 100; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="gradient-bg text-white shadow-lg">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <h1 class="text-3xl font-bold">🤝 Candid Connections</h1>
                    <p class="text-lg">Intelligent Job Matching Platform</p>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            <div class="px-4 py-6 sm:px-0">
                <!-- Navigation Cards - Bento-Style Layout -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-3 mb-6">
                    <!-- 1. Job Matches - Priority #1 -->
                    <div class="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-all cursor-pointer" onclick="loadMatches()">
                        <div class="flex justify-between items-start mb-2">
                            <div class="text-2xl">🎯</div>
                            <span id="matches-count" class="text-sm font-bold text-emerald-600">-</span>
                        </div>
                        <h3 class="text-sm font-semibold text-gray-900 mb-1">Job Matches</h3>
                        <p class="text-xs text-gray-600 mb-3">Intelligent matching results</p>
                        <div class="border-t pt-2">
                            <div class="text-xs font-medium text-gray-500 mb-1">Visualization</div>
                            <div class="flex space-x-1">
                                <button onclick="event.stopPropagation(); loadGraphVisualization('matches', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200" title="2D graph">2D</button>
                                <button onclick="event.stopPropagation(); loadGraphVisualization('matches', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200" title="3D graph">3D</button>
                            </div>
                        </div>
                    </div>

                    <!-- 2. Job Seekers -->
                    <div class="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-all cursor-pointer" onclick="loadJobSeekers()">
                        <div class="flex justify-between items-start mb-2">
                            <div class="text-2xl">👤</div>
                            <span id="job-seekers-count" class="text-sm font-bold text-blue-600">-</span>
                        </div>
                        <h3 class="text-sm font-semibold text-gray-900 mb-1">Job Seekers</h3>
                        <p class="text-xs text-gray-600 mb-3">Candidate profiles & skills</p>
                        <div class="border-t pt-2">
                            <div class="text-xs font-medium text-gray-500 mb-1">Visualization</div>
                            <div class="flex space-x-1">
                                <button onclick="event.stopPropagation(); loadGraphVisualization('jobSeekers', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200" title="2D graph">2D</button>
                                <button onclick="event.stopPropagation(); loadGraphVisualization('jobSeekers', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200" title="3D graph">3D</button>
                            </div>
                        </div>
                    </div>

                    <!-- 3. Companies (Smart Context-Aware Card) -->
                    <div class="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-all cursor-pointer relative" onclick="loadCompanies()">
                        <div class="flex justify-between items-start mb-2">
                            <div class="text-2xl">🏢</div>
                            <span id="companies-count" class="text-sm font-bold text-teal-600">-</span>
                        </div>
                        <h3 class="text-sm font-semibold text-gray-900 mb-1">Companies</h3>
                        <p class="text-xs text-gray-600 mb-2">Organizational structure & hierarchy</p>

                        <!-- Smart Preview -->
                        <div class="bg-gray-50 rounded p-2 mb-2">
                            <div class="flex justify-between text-xs">
                                <span class="text-gray-600">👔 Authorities: <span id="hiring-authorities-count" class="font-medium text-orange-600">-</span></span>
                                <span class="text-gray-600">💼 Positions: <span id="positions-count" class="font-medium text-violet-600">-</span></span>
                            </div>
                        </div>

                        <!-- Context Actions -->
                        <div class="flex space-x-1">
                            <button onclick="event.stopPropagation(); toggleCompanyActions()" id="company-actions-btn" class="flex-1 px-2 py-1 bg-teal-50 text-teal-800 rounded text-xs hover:bg-teal-100 transition-all">
                                Actions ▼
                            </button>
                            <button onclick="event.stopPropagation(); loadGraphVisualization('companies', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200" title="2D company visualization">📊</button>
                            <button onclick="event.stopPropagation(); loadGraphVisualization('companies', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200" title="3D company visualization">🌐</button>
                        </div>

                        <!-- Smart Actions Dropdown -->
                        <div id="company-actions-menu" class="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-20 overflow-hidden" style="display: none;">
                            <div class="p-2 space-y-1">
                                <!-- Hierarchical Navigation -->
                                <div class="text-xs font-medium text-gray-500 mb-1">Navigate Hierarchy</div>
                                <button onclick="event.stopPropagation(); loadHiringAuthorities(); closeCompanyActions()" class="w-full px-2 py-1 text-left text-xs hover:bg-orange-50 rounded flex items-center">
                                    <span class="w-2 h-2 bg-orange-400 rounded-full mr-2"></span>
                                    👔 Hiring Authorities
                                </button>
                                <button onclick="event.stopPropagation(); loadPositions(); closeCompanyActions()" class="w-full px-2 py-1 text-left text-xs hover:bg-violet-50 rounded flex items-center ml-3">
                                    <span class="w-2 h-2 bg-violet-400 rounded-full mr-2"></span>
                                    💼 Open Positions
                                </button>

                                <!-- Visualization Options -->
                                <hr class="my-2 border-gray-100">
                                <div class="text-xs font-medium text-gray-500 mb-1">Visualizations</div>
                                <button onclick="event.stopPropagation(); loadGraphVisualization('hiringAuthorities', '2d'); closeCompanyActions()" class="w-full px-2 py-1 text-left text-xs hover:bg-blue-50 rounded">
                                    📊 Authorities Network (2D)
                                </button>
                                <button onclick="event.stopPropagation(); loadGraphVisualization('hiringAuthorities', '3d'); closeCompanyActions()" class="w-full px-2 py-1 text-left text-xs hover:bg-purple-50 rounded">
                                    🌐 Authorities Network (3D)
                                </button>
                                <button onclick="event.stopPropagation(); loadGraphVisualization('positions', '2d'); closeCompanyActions()" class="w-full px-2 py-1 text-left text-xs hover:bg-blue-50 rounded">
                                    📊 Positions Network (2D)
                                </button>
                                <button onclick="event.stopPropagation(); loadGraphVisualization('positions', '3d'); closeCompanyActions()" class="w-full px-2 py-1 text-left text-xs hover:bg-purple-50 rounded">
                                    🌐 Positions Network (3D)
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 4. Skills -->
                    <div class="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-all cursor-pointer" onclick="loadSkills()">
                        <div class="flex justify-between items-start mb-2">
                            <div class="text-2xl">⚡</div>
                            <span id="skills-count" class="text-sm font-bold text-amber-600">-</span>
                        </div>
                        <h3 class="text-sm font-semibold text-gray-900 mb-1">Skills</h3>
                        <p class="text-xs text-gray-600 mb-3">Demand & availability analysis</p>
                        <div class="border-t pt-2">
                            <div class="text-xs font-medium text-gray-500 mb-1">Visualization</div>
                            <div class="flex space-x-1">
                                <button onclick="event.stopPropagation(); loadGraphVisualization('skills', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200" title="2D graph">2D</button>
                                <button onclick="event.stopPropagation(); loadGraphVisualization('skills', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200" title="3D graph">3D</button>
                            </div>
                        </div>
                    </div>

                    <!-- 5. Graph Visualization - Global "All in One" -->
                    <div class="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-all cursor-pointer" onclick="loadGraphVisualization()">
                        <div class="flex justify-between items-start mb-2">
                            <div class="text-2xl">🌐</div>
                            <div class="w-4 h-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full"></div>
                        </div>
                        <h3 class="text-sm font-semibold text-gray-900 mb-1">Graph Visualization</h3>
                        <p class="text-xs text-gray-600 mb-2">Global database interface</p>

                        <!-- Embedded Connections Navigation -->
                        <div class="mb-2">
                            <div class="flex items-center justify-between p-1.5 bg-indigo-50 rounded border border-indigo-200">
                                <div class="flex items-center">
                                    <span class="w-1.5 h-1.5 bg-indigo-400 rounded-full mr-1.5"></span>
                                    <span class="text-xs text-indigo-800 font-medium">🔗</span>
                                    <span id="connections-count" class="ml-1 text-xs text-indigo-600 bg-indigo-100 px-1 py-0.5 rounded">-</span>
                                </div>
                                <div class="flex space-x-0.5">
                                    <button onclick="event.stopPropagation(); loadConnections()" class="px-1.5 py-0.5 bg-indigo-100 text-indigo-800 rounded text-xs hover:bg-indigo-200" title="Connections analysis">List</button>
                                    <button onclick="event.stopPropagation(); loadGraphVisualization('connections', '2d')" class="px-1.5 py-0.5 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200" title="2D">2D</button>
                                    <button onclick="event.stopPropagation(); loadGraphVisualization('connections', '3d')" class="px-1.5 py-0.5 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200" title="3D">3D</button>
                                </div>
                            </div>
                        </div>

                        <div class="border-t pt-2">
                            <div class="text-xs font-medium text-gray-500 mb-1">Visualization</div>
                            <div class="flex space-x-1">
                                <button onclick="event.stopPropagation(); loadGraphVisualization('overview', '2d')" class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs hover:bg-blue-200" title="2D graph">2D</button>
                                <button onclick="event.stopPropagation(); loadGraphVisualization('overview', '3d')" class="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs hover:bg-purple-200" title="3D graph">3D</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Content Area -->
                <div class="bg-white rounded-lg shadow" id="content-area">
                    <div class="p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Welcome to Candid Connections</h2>
                        <p class="text-gray-600 mb-4">
                            This intelligent job matching platform uses advanced graph database technology to connect
                            job seekers with the perfect opportunities through relationship mapping and smart algorithms.
                        </p>
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h3 class="font-semibold text-blue-900 mb-2">🚀 Platform Features:</h3>
                            <ul class="text-blue-800 space-y-1">
                                <li>• Intelligent job matching based on skills, experience, and location</li>
                                <li>• Company hierarchy modeling for appropriate hiring authority routing</li>
                                <li>• Graph database relationships for complex data modeling</li>
                                <li>• Real-time statistics and comprehensive data management</li>
                                <li>• Enhanced 3D visualization with node labels and camera animations</li>
                                <li>• Comprehensive CRUD operations with real-time updates</li>
                                <li>• Skills demand and availability analysis</li>
                                <li>• Company hierarchy with drilling capabilities</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 3D Visualization Dependencies -->
    <script src="https://unpkg.com/three@0.158.0/build/three.min.js"></script>
    <script src="https://unpkg.com/3d-force-graph@1.73.0/dist/3d-force-graph.min.js"></script>
    <script src="https://unpkg.com/d3-force@3.0.0/dist/d3-force.min.js"></script>

    <script src="app.js"></script>
    <script>
        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
        });
    </script>
</body>
</html>
