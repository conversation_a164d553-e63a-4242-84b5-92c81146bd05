#!/bin/bash

# Job Match Platform Generator
# This script creates a Next.js 15 application with a graph database backend
# for matching job seekers with hiring authorities

echo "🚀 Generating Job Match Platform with Next.js 15 and ArangoDB..."

# Create Next.js app
npx create-next-app@latest job-match-platform --ts --tailwind --app --src-dir --import-alias "@/*"
cd job-match-platform

# Install dependencies
npm install arangodb react-force-graph d3 @aws-sdk/client-s3 aws-amplify @aws-amplify/ui-react react-hook-form zod @hookform/resolvers

# Create directory structure
mkdir -p src/app/api/job-seekers src/app/api/companies src/app/api/hiring-authorities src/app/api/matches
mkdir -p src/app/(dashboard)/job-seekers src/app/(dashboard)/companies src/app/(dashboard)/hiring-authorities
mkdir -p src/lib/db src/lib/utils src/components/ui src/components/graphs
mkdir -p scripts/db-seed

# Create ArangoDB setup script
cat > scripts/db-setup.js << 'EOL'
const { Database } = require('arangodb');

async function setupDatabase() {
  const db = new Database({
    url: process.env.ARANGO_URL || 'http://localhost:8529',
    databaseName: 'job_match',
    auth: { username: 'root', password: process.env.ARANGO_PASSWORD || 'password' }
  });
  
  // Create collections
  await db.createCollection('jobSeekers');
  await db.createCollection('companies');
  await db.createCollection('hiringAuthorities');
  await db.createCollection('positions');
  await db.createCollection('skills');
  
  // Create edge collections
  await db.createEdgeCollection('hasSkill');
  await db.createEdgeCollection('requiresSkill');
  await db.createEdgeCollection('belongsTo');
  await db.createEdgeCollection('manages');
  
  console.log('Database setup complete!');
}

setupDatabase().catch(console.error);
EOL

# Create seed data script
cat > scripts/db-seed/seed-data.js << 'EOL'
// Mock data generation for job matching platform
const { Database } = require('arangodb');

async function seedDatabase() {
  const db = new Database({
    url: process.env.ARANGO_URL || 'http://localhost:8529',
    databaseName: 'job_match',
    auth: { username: 'root', password: process.env.ARANGO_PASSWORD || 'password' }
  });
  
  // Sample skills
  const skills = [
    { name: 'JavaScript', category: 'Programming' },
    { name: 'React', category: 'Frontend' },
    { name: 'Node.js', category: 'Backend' },
    // More skills would be added here
  ];
  
  // Sample job seekers
  const jobSeekers = [
    { 
      name: 'Alice Johnson', 
      title: 'Frontend Developer',
      experience: 5,
      education: 'BS Computer Science'
    },
    // More job seekers would be added here
  ];
  
  // Sample companies with hiring authorities based on size
  const companies = [
    {
      name: 'TechStartup',
      size: 45,
      industry: 'SaaS',
      location: 'San Francisco, CA'
    },
    {
      name: 'EnterpriseGlobal',
      size: 2500,
      industry: 'Enterprise Software',
      location: 'New York, NY'
    }
    // More companies would be added here
  ];
  
  // Insert data and create relationships
  // This would be expanded with more detailed mock data and relationships
  
  console.log('Database seeded successfully!');
}

seedDatabase().catch(console.error);
EOL

# Create README with documentation
cat > README.md << 'EOL'
# Job Match Platform

A Next.js 15 application with a graph database backend for matching job seekers with hiring authorities.

## Architecture

```mermaid
graph TD
    A[Job Seeker] -->|has| B[Skills]
    C[Position] -->|requires| B
    D[Hiring Authority] -->|manages| C
    E[Company] -->|employs| D
    A -->|matches with| C
    A -->|connects to| D
```

## Database Schema

```mermaid
erDiagram
    JOBSEEKER ||--o{ SKILL : has
    POSITION ||--o{ SKILL : requires
    HIRINGAUTHORITY ||--o{ POSITION : manages
    COMPANY ||--o{ HIRINGAUTHORITY : employs
```

## Getting Started

1. Install Docker and run ArangoDB:
   ```
   docker run -p 8529:8529 -e ARANGO_ROOT_PASSWORD=password arangodb/arangodb:latest
   ```

2. Set up the database:
   ```
   node scripts/db-setup.js
   ```

3. Seed the database:
   ```
   node scripts/db-seed/seed-data.js
   ```

4. Run the development server:
   ```
   npm run dev
   ```

## Deployment

This application can be deployed to AWS using Amplify Gen 2:

```
amplify init
amplify add hosting
amplify publish
```

## Graph Database Benefits

Graph databases are ideal for this application because:

1. **Relationship-First Design**: Graph databases excel at representing complex relationships between entities.
2. **Efficient Path Finding**: Finding connections between job seekers and hiring authorities requires traversing relationship paths.
3. **Flexible Schema**: Skills and requirements can evolve without major schema changes.
4. **Performance on Connected Data**: Graph queries perform well when exploring connections.
EOL

# Create database connection utility
cat > src/lib/db/client.ts << 'EOL'
import { Database } from 'arangodb';

let dbClient: Database | null = null;

export function getDbClient() {
  if (!dbClient) {
    dbClient = new Database({
      url: process.env.ARANGO_URL || 'http://localhost:8529',
      databaseName: 'job_match',
      auth: { 
        username: 'root', 
        password: process.env.ARANGO_PASSWORD || 'password' 
      }
    });
  }
  return dbClient;
}
EOL

# Create Docker Compose file for local development
cat > docker-compose.yml << 'EOL'
version: '3'
services:
  arangodb:
    image: arangodb:latest
    ports:
      - "8529:8529"
    environment:
      - ARANGO_ROOT_PASSWORD=password
    volumes:
      - arangodb_data:/var/lib/arangodb3
      - arangodb_apps:/var/lib/arangodb3-apps

volumes:
  arangodb_data:
  arangodb_apps:
EOL

# Create sample API route
cat > src/app/api/job-seekers/route.ts << 'EOL'
import { NextResponse } from 'next/server';
import { getDbClient } from '@/lib/db/client';

export async function GET() {
  try {
    const db = getDbClient();
    const cursor = await db.query('FOR doc IN jobSeekers RETURN doc');
    const result = await cursor.all();
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching job seekers:', error);
    return NextResponse.json(
      { error: 'Failed to fetch job seekers' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const db = getDbClient();
    const result = await db.collection('jobSeekers').save(body);
    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Error creating job seeker:', error);
    return NextResponse.json(
      { error: 'Failed to create job seeker' },
      { status: 500 }
    );
  }
}
EOL

# Create sample dashboard page
cat > src/app/page.tsx << 'EOL'
import Link from 'next/link';

export default function Home() {
  return (
    <main className="flex min-h-screen flex-col items-center justify-between p-24">
      <div className="z-10 max-w-5xl w-full items-center justify-between font-mono text-sm">
        <h1 className="text-4xl font-bold mb-8">Job Match Platform</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Link href="/job-seekers" className="p-6 border rounded-lg hover:bg-gray-100">
            <h2 className="text-2xl font-semibold mb-2">Job Seekers</h2>
            <p>Browse and manage job seeker profiles</p>
          </Link>
          
          <Link href="/companies" className="p-6 border rounded-lg hover:bg-gray-100">
            <h2 className="text-2xl font-semibold mb-2">Companies</h2>
            <p>Explore company profiles and hiring needs</p>
          </Link>
          
          <Link href="/hiring-authorities" className="p-6 border rounded-lg hover:bg-gray-100">
            <h2 className="text-2xl font-semibold mb-2">Hiring Authorities</h2>
            <p>View hiring managers and their positions</p>
          </Link>
        </div>
      </div>
    </main>
  );
}
EOL

# Create sample graph visualization component
cat > src/components/graphs/ConnectionGraph.tsx << 'EOL'
'use client';

import { useEffect, useRef } from 'react';
import ForceGraph2D from 'react-force-graph-2d';

type Node = {
  id: string;
  name: string;
  type: 'jobSeeker' | 'company' | 'hiringAuthority' | 'skill' | 'position';
};

type Link = {
  source: string;
  target: string;
  type: string;
};

type GraphData = {
  nodes: Node[];
  links: Link[];
};

export default function ConnectionGraph({ data }: { data: GraphData }) {
  const graphRef = useRef<any>();

  useEffect(() => {
    if (graphRef.current) {
      graphRef.current.d3Force('charge').strength(-120);
    }
  }, []);

  return (
    <div className="h-[600px] w-full border rounded-lg">
      <ForceGraph2D
        ref={graphRef}
        graphData={data}
        nodeLabel="name"
        nodeColor={(node: any) => {
          switch (node.type) {
            case 'jobSeeker': return '#4CAF50';
            case 'company': return '#2196F3';
            case 'hiringAuthority': return '#9C27B0';
            case 'skill': return '#FF9800';
            case 'position': return '#F44336';
            default: return '#999';
          }
        }}
        linkDirectionalArrowLength={3.5}
        linkDirectionalArrowRelPos={1}
      />
    </div>
  );
}
EOL

echo "✅ Job Match Platform generated successfully!"
echo "To start the application:"
echo "1. Start ArangoDB: docker-compose up -d"
echo "2. Set up the database: node scripts/db-setup.js"
echo "3. Seed the database: node scripts/db-seed/seed-data.js"
echo "4. Run the development server: npm run dev"